<?php
// Script untuk mengirim notifikasi ke driver untuk pesanan yang siap diambil

// Include required files
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'config/database.php';

// Connect to database
$conn = connectDB();

// Get orders with status "ready_for_pickup" and no driver assigned
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.order_status = 'ready_for_pickup'
    AND o.driver_id IS NULL
");
$stmt->execute();
$orders = $stmt->fetchAll();

// Send notifications for each order
$notificationsSent = 0;
foreach ($orders as $order) {
    $message = "Pesanan #{$order['order_id']} dari restoran {$order['restaurant_name']} siap untuk diambil!";
    $result = notifyAvailableDrivers($order['order_id'], $order['restaurant_id'], 'ready_for_pickup', $message);
    
    if ($result) {
        $notificationsSent++;
        echo "Notifikasi berhasil dikirim untuk pesanan #{$order['order_id']}.<br>";
    } else {
        echo "Tidak ada driver yang tersedia dalam radius 10km untuk pesanan #{$order['order_id']}.<br>";
    }
}

echo "<br>Total notifikasi yang dikirim: $notificationsSent";
?>
