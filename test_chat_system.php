<?php
// Test file untuk memverifikasi sistem chat
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Test Sistem Chat KikaZen Ship</h2>";

// Test 1: Cek tabel chat
echo "<h3>1. Struktur Tabel Chat</h3>";
$conn = connectDB();

$tables = ['chat_rooms', 'chat_participants', 'chat_messages'];
foreach ($tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE '$table'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Tabel $table ada</p>";
        
        // Show table structure
        $stmt = $conn->prepare("DESCRIBE $table");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<details><summary>Struktur tabel $table</summary>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table></details>";
    } else {
        echo "<p style='color: red;'>✗ Tabel $table tidak ada</p>";
    }
}

// Test 2: Cek data chat rooms
echo "<h3>2. Data Chat Rooms</h3>";
$stmt = $conn->prepare("
    SELECT cr.*, o.order_id, o.order_status, 
           COUNT(cp.participant_id) as participant_count,
           COUNT(cm.message_id) as message_count
    FROM chat_rooms cr
    LEFT JOIN orders o ON cr.order_id = o.order_id
    LEFT JOIN chat_participants cp ON cr.room_id = cp.room_id
    LEFT JOIN chat_messages cm ON cr.room_id = cm.room_id
    GROUP BY cr.room_id
    ORDER BY cr.created_at DESC
    LIMIT 10
");
$stmt->execute();
$chatRooms = $stmt->fetchAll();

if (!empty($chatRooms)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Room ID</th><th>Order ID</th><th>Type</th><th>Status</th><th>Participants</th><th>Messages</th><th>Created</th></tr>";
    
    foreach ($chatRooms as $room) {
        echo "<tr>";
        echo "<td>{$room['room_id']}</td>";
        echo "<td>{$room['order_id']}</td>";
        echo "<td>{$room['room_type']}</td>";
        echo "<td>{$room['status']}</td>";
        echo "<td>{$room['participant_count']}</td>";
        echo "<td>{$room['message_count']}</td>";
        echo "<td>{$room['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>Tidak ada chat room ditemukan.</p>";
}

// Test 3: Cek participants
echo "<h3>3. Chat Participants</h3>";
$stmt = $conn->prepare("
    SELECT cp.*, cr.order_id, cr.room_type,
           CASE 
               WHEN cp.user_type = 'customer' THEN u.name
               WHEN cp.user_type = 'restaurant_owner' THEN ro.name
               WHEN cp.user_type = 'driver' THEN d.name
               WHEN cp.user_type = 'admin' THEN a.name
               ELSE 'Unknown'
           END as user_name
    FROM chat_participants cp
    JOIN chat_rooms cr ON cp.room_id = cr.room_id
    LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
    LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
    LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
    LEFT JOIN admins a ON cp.user_type = 'admin' AND cp.user_id = a.admin_id
    ORDER BY cp.room_id, cp.user_type
    LIMIT 20
");
$stmt->execute();
$participants = $stmt->fetchAll();

if (!empty($participants)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Room ID</th><th>Order ID</th><th>User Type</th><th>User ID</th><th>User Name</th><th>Last Read</th></tr>";
    
    foreach ($participants as $participant) {
        echo "<tr>";
        echo "<td>{$participant['room_id']}</td>";
        echo "<td>{$participant['order_id']}</td>";
        echo "<td>{$participant['user_type']}</td>";
        echo "<td>{$participant['user_id']}</td>";
        echo "<td>{$participant['user_name']}</td>";
        echo "<td>{$participant['last_read_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>Tidak ada participants ditemukan.</p>";
}

// Test 4: Cek messages
echo "<h3>4. Chat Messages (10 terbaru)</h3>";
$stmt = $conn->prepare("
    SELECT cm.*, cr.order_id,
           CASE 
               WHEN cm.sender_type = 'customer' THEN u.name
               WHEN cm.sender_type = 'restaurant_owner' THEN ro.name
               WHEN cm.sender_type = 'driver' THEN d.name
               WHEN cm.sender_type = 'admin' THEN a.name
               WHEN cm.sender_type = 'system' THEN 'System'
               ELSE 'Unknown'
           END as sender_name
    FROM chat_messages cm
    JOIN chat_rooms cr ON cm.room_id = cr.room_id
    LEFT JOIN users u ON cm.sender_type = 'customer' AND cm.sender_id = u.user_id
    LEFT JOIN restaurant_owners ro ON cm.sender_type = 'restaurant_owner' AND cm.sender_id = ro.owner_id
    LEFT JOIN drivers d ON cm.sender_type = 'driver' AND cm.sender_id = d.driver_id
    LEFT JOIN admins a ON cm.sender_type = 'admin' AND cm.sender_id = a.admin_id
    ORDER BY cm.created_at DESC
    LIMIT 10
");
$stmt->execute();
$messages = $stmt->fetchAll();

if (!empty($messages)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Room ID</th><th>Order ID</th><th>Sender</th><th>Message</th><th>Type</th><th>Created</th></tr>";
    
    foreach ($messages as $message) {
        echo "<tr>";
        echo "<td>{$message['room_id']}</td>";
        echo "<td>{$message['order_id']}</td>";
        echo "<td>{$message['sender_name']} ({$message['sender_type']})</td>";
        echo "<td>" . substr($message['message_text'], 0, 50) . (strlen($message['message_text']) > 50 ? '...' : '') . "</td>";
        echo "<td>{$message['message_type']}</td>";
        echo "<td>{$message['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>Tidak ada messages ditemukan.</p>";
}

// Test 5: Test fungsi createOrderChatRoom
echo "<h3>5. Test Fungsi createOrderChatRoom</h3>";

// Get a sample order
$stmt = $conn->prepare("
    SELECT o.*, r.owner_id, u.name as customer_name, ro.name as owner_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.order_id NOT IN (SELECT DISTINCT order_id FROM chat_rooms WHERE order_id IS NOT NULL)
    LIMIT 1
");
$stmt->execute();
$testOrder = $stmt->fetch();

if ($testOrder) {
    echo "<p>Testing dengan Order ID: {$testOrder['order_id']}</p>";
    
    $room_id = createOrderChatRoom($testOrder['order_id']);
    
    if ($room_id) {
        echo "<p style='color: green;'>✓ Chat room berhasil dibuat dengan ID: $room_id</p>";
        
        // Check participants
        $stmt = $conn->prepare("
            SELECT cp.*, 
                   CASE 
                       WHEN cp.user_type = 'customer' THEN u.name
                       WHEN cp.user_type = 'restaurant_owner' THEN ro.name
                       WHEN cp.user_type = 'driver' THEN d.name
                       ELSE 'Unknown'
                   END as user_name
            FROM chat_participants cp
            LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
            LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
            LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
            WHERE cp.room_id = :room_id
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->execute();
        $newParticipants = $stmt->fetchAll();
        
        echo "<p>Participants yang ditambahkan:</p>";
        echo "<ul>";
        foreach ($newParticipants as $participant) {
            echo "<li>{$participant['user_type']}: {$participant['user_name']} (ID: {$participant['user_id']})</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>✗ Gagal membuat chat room</p>";
    }
} else {
    echo "<p style='color: orange;'>Tidak ada order yang belum memiliki chat room untuk testing.</p>";
}

// Test 6: Statistik
echo "<h3>6. Statistik Chat</h3>";

$stats = [];

// Total chat rooms
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM chat_rooms");
$stmt->execute();
$stats['total_rooms'] = $stmt->fetch()['count'];

// Total participants
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM chat_participants");
$stmt->execute();
$stats['total_participants'] = $stmt->fetch()['count'];

// Total messages
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM chat_messages");
$stmt->execute();
$stats['total_messages'] = $stmt->fetch()['count'];

// Orders with chat rooms
$stmt = $conn->prepare("SELECT COUNT(DISTINCT order_id) as count FROM chat_rooms WHERE order_id IS NOT NULL");
$stmt->execute();
$stats['orders_with_chat'] = $stmt->fetch()['count'];

// Orders without chat rooms
$stmt = $conn->prepare("
    SELECT COUNT(*) as count FROM orders o
    WHERE o.order_id NOT IN (SELECT DISTINCT order_id FROM chat_rooms WHERE order_id IS NOT NULL)
");
$stmt->execute();
$stats['orders_without_chat'] = $stmt->fetch()['count'];

echo "<ul>";
echo "<li>Total Chat Rooms: {$stats['total_rooms']}</li>";
echo "<li>Total Participants: {$stats['total_participants']}</li>";
echo "<li>Total Messages: {$stats['total_messages']}</li>";
echo "<li>Orders dengan Chat Room: {$stats['orders_with_chat']}</li>";
echo "<li>Orders tanpa Chat Room: {$stats['orders_without_chat']}</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>Catatan:</strong> Sistem chat sekarang akan otomatis membuat chat room ketika pesanan dibuat dan menambahkan driver ketika di-assign.</p>";
echo "<p><a href='index.php'>← Kembali ke Beranda</a></p>";
?>
