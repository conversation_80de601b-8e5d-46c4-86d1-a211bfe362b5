<?php
// Debug file untuk owner/orders.php
session_start();

echo "<h2>Debug Owner Orders Page</h2>";

// Check session
echo "<h3>1. Session Check</h3>";
if (isset($_SESSION['owner_id'])) {
    echo "<p style='color: green;'>✓ Owner logged in: ID = " . $_SESSION['owner_id'] . "</p>";
    echo "<p>Owner name: " . ($_SESSION['owner_name'] ?? 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>✗ Owner not logged in</p>";
    echo "<p><a href='login.php'>Login as Owner</a></p>";
    exit;
}

// Check includes
echo "<h3>2. Include Files Check</h3>";

$includes = [
    'includes/auth.php',
    'config/database.php',
    'includes/functions.php'
];

foreach ($includes as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
        require_once $file;
    } else {
        echo "<p style='color: red;'>✗ $file not found</p>";
    }
}

// Check database connection
echo "<h3>3. Database Connection</h3>";
try {
    $conn = connectDB();
    echo "<p style='color: green;'>✓ Database connected</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check formatCurrency function
echo "<h3>4. formatCurrency Function Test</h3>";
if (function_exists('formatCurrency')) {
    echo "<p style='color: green;'>✓ formatCurrency function exists</p>";
    
    $test_values = [5.07, 76.05, 900];
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Input</th><th>formatCurrency(input)</th><th>formatCurrency(input, true)</th><th>formatCurrency(input, false)</th></tr>";
    
    foreach ($test_values as $value) {
        echo "<tr>";
        echo "<td>$value</td>";
        echo "<td>" . formatCurrency($value) . "</td>";
        echo "<td>" . formatCurrency($value, true) . "</td>";
        echo "<td>" . formatCurrency($value, false) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ formatCurrency function not found</p>";
}

// Check owner data
echo "<h3>5. Owner Data</h3>";
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

if ($owner) {
    echo "<p style='color: green;'>✓ Owner data found</p>";
    echo "<ul>";
    echo "<li>Name: " . $owner['name'] . "</li>";
    echo "<li>Email: " . $owner['email'] . "</li>";
    echo "<li>Status: " . $owner['status'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Owner data not found</p>";
}

// Check restaurants
echo "<h3>6. Owner's Restaurants</h3>";
$stmt = $conn->prepare("SELECT restaurant_id, name FROM restaurants WHERE owner_id = :owner_id ORDER BY name");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();

if (!empty($restaurants)) {
    echo "<p style='color: green;'>✓ Found " . count($restaurants) . " restaurants</p>";
    echo "<ul>";
    foreach ($restaurants as $restaurant) {
        echo "<li>ID: " . $restaurant['restaurant_id'] . " - " . $restaurant['name'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠ No restaurants found for this owner</p>";
}

// Check orders
echo "<h3>7. Orders Data</h3>";
$stmt = $conn->prepare("
    SELECT
        o.*,
        r.name as restaurant_name,
        u.name as customer_name,
        u.phone as customer_phone,
        d.name as driver_name,
        d.phone as driver_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    WHERE r.owner_id = :owner_id
    ORDER BY o.created_at DESC
    LIMIT 5
");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$orders = $stmt->fetchAll();

if (!empty($orders)) {
    echo "<p style='color: green;'>✓ Found " . count($orders) . " orders (showing 5 latest)</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<th>Order ID</th>";
    echo "<th>Restaurant</th>";
    echo "<th>Customer</th>";
    echo "<th>Total (DB)</th>";
    echo "<th>formatCurrency</th>";
    echo "<th>Manual Calc</th>";
    echo "<th>Status</th>";
    echo "<th>Created</th>";
    echo "</tr>";
    
    foreach ($orders as $order) {
        echo "<tr>";
        echo "<td>#" . $order['order_id'] . "</td>";
        echo "<td>" . $order['restaurant_name'] . "</td>";
        echo "<td>" . $order['customer_name'] . "</td>";
        echo "<td>" . $order['total_amount'] . "</td>";
        echo "<td>" . formatCurrency($order['total_amount']) . "</td>";
        echo "<td>Rp" . number_format($order['total_amount'] * 15000, 0, ',', '.') . "</td>";
        echo "<td>" . $order['order_status'] . "</td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($order['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No orders found for this owner</p>";
}

// Check for PHP errors
echo "<h3>8. PHP Error Check</h3>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $recent_errors = shell_exec("tail -20 $error_log | grep -i 'owner/orders'");
    if ($recent_errors) {
        echo "<p style='color: red;'>Recent errors found:</p>";
        echo "<pre>$recent_errors</pre>";
    } else {
        echo "<p style='color: green;'>✓ No recent errors found</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Error log not accessible</p>";
}

// Test the actual page
echo "<h3>9. Test Page Access</h3>";
echo "<p><a href='owner/orders.php' target='_blank'>Open owner/orders.php in new tab</a></p>";

// Show current working directory and file paths
echo "<h3>10. File Paths</h3>";
echo "<ul>";
echo "<li>Current directory: " . getcwd() . "</li>";
echo "<li>owner/orders.php exists: " . (file_exists('owner/orders.php') ? 'Yes' : 'No') . "</li>";
echo "<li>includes/functions.php exists: " . (file_exists('includes/functions.php') ? 'Yes' : 'No') . "</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>Jika masih ada masalah, silakan copy error message yang muncul di halaman owner/orders.php</strong></p>";
?>
