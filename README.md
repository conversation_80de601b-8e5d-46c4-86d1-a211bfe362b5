# KikaZen Ship - Food Delivery Application

KikaZen Ship is a comprehensive food delivery application similar to Gofood or Grabfood, allowing users to order food from various restaurants with real-time delivery tracking.

## Features

### Customer Features
- User registration and login
- Browse restaurants and menus
- Add items to cart
- Place orders with delivery address
- Track order status in real-time
- Rate and review orders
- View order history

### Driver Features
- Driver registration and login
- Update availability status
- Accept delivery requests
- Real-time location tracking
- Navigate to restaurant and customer
- Update order status
- View earnings and history

### Restaurant Features
- Manage menu items
- Update restaurant information
- Receive and process orders
- Track delivery status

### Admin Features
- Manage users, drivers, and restaurants
- View all orders and their statuses
- Generate reports
- Resolve issues and disputes

## Technology Stack

- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5
- **Backend**: PHP
- **Database**: MySQL (via XAMPP)
- **APIs**: Custom RESTful APIs

## Installation

### Prerequisites
- XAMPP (Apache, MySQL, PHP)
- Web browser

### Setup Instructions

1. Clone the repository to your XAMPP htdocs folder:
   ```
   git clone https://github.com/yourusername/kikazen_ship.git
   ```

2. Start Apache and MySQL services in XAMPP control panel

3. Import the database schema:
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create a new database named `kikazen_ship`
   - Import the `database/schema.sql` file

4. Configure database connection:
   - Open `config/database.php`
   - Update the database credentials if needed

5. Access the application:
   - Open your web browser and navigate to http://localhost/kikazen_ship

## Project Structure

```
kikazen_ship/
├── admin/                 # Admin dashboard and management
├── api/                   # API endpoints
├── assets/                # Images and other static assets
├── config/                # Configuration files
├── css/                   # CSS stylesheets
├── database/              # Database schema and migrations
├── driver/                # Driver dashboard and functionality
├── includes/              # Reusable PHP components
├── js/                    # JavaScript files
├── index.php              # Main entry point
├── login.php              # User login page
├── logout.php             # Logout functionality
├── register.php           # User registration page
├── restaurants.php        # Restaurant listing page
├── restaurant.php         # Restaurant detail page
└── README.md              # Project documentation
```

## API Endpoints

### Authentication
- `POST /api/register.php` - Register a new user
- `POST /api/login.php` - User login

### Restaurants
- `GET /api/restaurants.php` - Get all restaurants
- `GET /api/restaurants.php?id={id}` - Get restaurant details
- `POST /api/restaurants.php` - Create a new restaurant (admin only)
- `PUT /api/restaurants.php?id={id}` - Update restaurant (admin only)
- `DELETE /api/restaurants.php?id={id}` - Delete restaurant (admin only)

### Menu Items
- `GET /api/menu_items.php?restaurant_id={id}` - Get menu items for a restaurant
- `GET /api/menu_items.php?id={id}` - Get menu item details
- `POST /api/menu_items.php` - Create a new menu item (admin only)
- `PUT /api/menu_items.php?id={id}` - Update menu item (admin only)
- `DELETE /api/menu_items.php?id={id}` - Delete menu item (admin only)

### Orders
- `GET /api/orders.php?user_id={id}` - Get orders for a user
- `GET /api/orders.php?driver_id={id}` - Get orders for a driver
- `GET /api/orders.php?restaurant_id={id}` - Get orders for a restaurant
- `GET /api/orders.php?id={id}` - Get order details
- `POST /api/orders.php` - Create a new order
- `PUT /api/orders.php?id={id}` - Update order status

## Database Schema

The database consists of the following main tables:

- `users` - Customer information
- `drivers` - Driver information
- `admins` - Admin users
- `restaurants` - Restaurant information
- `categories` - Food categories
- `menu_items` - Menu items for restaurants
- `orders` - Order information
- `order_items` - Items in an order
- `order_tracking` - Order status tracking
- `reviews` - Customer reviews

## Roadmap Pengembangan

Berikut adalah rencana pengembangan KikaZen Ship untuk meningkatkan layanan dan pengalaman pengguna:

### 1. Peningkatan Fitur Pengguna

- **Sistem Rating dan Ulasan yang Lebih Komprehensif**:
  - Rating terpisah untuk makanan, layanan restoran, dan layanan pengiriman
  - Ulasan dengan foto makanan
  - Fitur "helpful" untuk ulasan

- **Sistem Loyalitas dan Reward**:
  - Program poin untuk pelanggan setia
  - Diskon khusus untuk pengguna yang sering memesan
  - Sistem referral untuk mengajak teman

- **Fitur Pesanan Terjadwal**:
  - Pemesanan makanan untuk waktu tertentu di masa depan
  - Pesanan berulang (misalnya makan siang kantor setiap hari)

### 2. Peningkatan untuk Pemilik Restoran

- **Dashboard Analitik yang Lebih Lengkap**:
  - Grafik penjualan harian, mingguan, dan bulanan
  - Analisis item menu terlaris
  - Laporan pendapatan dan tren pemesanan

- **Manajemen Inventori**:
  - Sistem untuk melacak stok bahan makanan
  - Peringatan otomatis saat stok menipis
  - Integrasi dengan sistem pembelian

- **Promosi dan Diskon**:
  - Alat untuk membuat promosi khusus dan diskon
  - Kampanye pemasaran untuk menu baru

### 3. Peningkatan untuk Pengemudi

- **Optimasi Rute**:
  - Algoritma untuk menentukan rute pengiriman terbaik
  - Estimasi waktu pengiriman yang lebih akurat

- **Sistem Insentif**:
  - Program bonus untuk pengemudi yang menyelesaikan banyak pengiriman
  - Sistem peringkat untuk pengemudi

### 4. Peningkatan Teknis

- **Optimasi Performa**:
  - Implementasi caching untuk mempercepat loading halaman
  - Optimasi query database untuk mengurangi waktu respons

- **Keamanan**:
  - Autentikasi dua faktor
  - Enkripsi end-to-end untuk data sensitif
  - Audit keamanan secara berkala

- **Integrasi API**:
  - Integrasi dengan layanan pembayaran online tambahan
  - Integrasi dengan layanan peta yang lebih canggih untuk pelacakan real-time

### 5. Pengalaman Pengguna

- **Desain Responsif yang Lebih Baik**:
  - Aplikasi yang berfungsi dengan baik di semua ukuran layar
  - Implementasi tema gelap (dark mode)

- **Aplikasi Mobile**:
  - Aplikasi native untuk Android dan iOS
  - Fitur notifikasi push untuk pembaruan status pesanan

- **Fitur Pencarian dan Filter yang Lebih Canggih**:
  - Filter berdasarkan jenis makanan, harga, rating, waktu pengiriman
  - Pencarian dengan suara atau gambar

### 6. Fitur Sosial dan Komunitas

- **Berbagi Pesanan**:
  - Fitur untuk berbagi menu favorit di media sosial
  - Pesanan grup untuk kantor atau acara

- **Forum Komunitas**:
  - Ruang diskusi untuk ulasan makanan dan rekomendasi
  - Tips dan trik dari pengguna lain

### 7. Keberlanjutan dan Tanggung Jawab Sosial

- **Opsi Ramah Lingkungan**:
  - Pilihan untuk mengurangi penggunaan plastik dalam pesanan
  - Kemitraan dengan restoran yang menggunakan bahan-bahan lokal dan berkelanjutan

- **Program Donasi Makanan**:
  - Opsi untuk menyumbangkan makanan ke yang membutuhkan
  - Kemitraan dengan organisasi amal lokal

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributors

- Your Name - Initial work

## Acknowledgments

- Bootstrap team for the UI framework
- XAMPP team for the development environment
- Font Awesome for the icons
