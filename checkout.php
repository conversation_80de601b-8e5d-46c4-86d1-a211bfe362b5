<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    // Save current URL to session for redirect after login
    $_SESSION['redirect_after_login'] = 'checkout.php';
    header('Location: login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get user addresses
$stmt = $conn->prepare("
    SELECT * FROM user_addresses
    WHERE user_id = ?
    ORDER BY is_default DESC, created_at DESC
");
$stmt->execute([$user_id]);
$addresses = $stmt->fetchAll();

// Process order submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $address_id = $_POST['address_id'] ?? '';
    $payment_method = $_POST['payment_method'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $no_utensils = isset($_POST['no_utensils']) ? 1 : 0;
    $eco_packaging = isset($_POST['eco_packaging']) ? 1 : 0;
    $carbon_offset = isset($_POST['carbon_offset']) ? 1 : 0;

    // Get cart data
    $cart = json_decode($_POST['cart_data'], true);

    if (empty($cart['items']) || empty($cart['restaurant_id'])) {
        $error = 'Keranjang belanja Anda kosong';
    } elseif (empty($address_id)) {
        $error = 'Silakan pilih alamat pengiriman';
    } elseif (empty($payment_method)) {
        $error = 'Silakan pilih metode pembayaran';
    } else {
        try {
            // Get address details
            $stmt = $conn->prepare("SELECT * FROM user_addresses WHERE address_id = ? AND user_id = ?");
            $stmt->execute([$address_id, $user_id]);
            $address = $stmt->fetch();

            if (!$address) {
                $error = 'Alamat tidak valid';
            } else {
                // Get restaurant details
                $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = ?");
                $stmt->execute([$cart['restaurant_id']]);
                $restaurant = $stmt->fetch();

                if (!$restaurant) {
                    $error = 'Restoran tidak valid';
                } else {
                    // Calculate subtotal
                    $subtotal = 0;
                    foreach ($cart['items'] as $item) {
                        $subtotal += $item['price'] * $item['quantity'];
                    }

                    // Calculate delivery fee based on distance
                    $delivery_fee = $restaurant['delivery_fee']; // Default fallback

                    // Try to calculate based on coordinates if available
                    if ($address['latitude'] && $address['longitude'] &&
                        $restaurant['latitude'] && $restaurant['longitude']) {

                        $distance = getDistance(
                            $restaurant['latitude'], $restaurant['longitude'],
                            $address['latitude'], $address['longitude']
                        );

                        $delivery_fee = calculateDeliveryFeeInUnits($distance);
                    }

                    // Calculate carbon offset amount if selected
                    $carbon_offset_amount = 0;
                    $estimated_emissions = 0;

                    if ($carbon_offset) {
                        // Simple calculation based on distance
                        $distance = 5; // Default distance in km
                        $emissions_per_km = 0.1; // kg CO2 per km
                        $estimated_emissions = $distance * $emissions_per_km;
                        $carbon_offset_amount = 0.01; // 0.01 unit per kg CO2 (fixed amount in database units)
                    }

                    // Calculate total
                    $total_amount = $subtotal + $delivery_fee + $carbon_offset_amount;

                    // Log values for debugging
                    error_log("Checkout values: subtotal=$subtotal, delivery_fee=$delivery_fee, carbon_offset_amount=$carbon_offset_amount, total_amount=$total_amount");

                    // Begin transaction
                    $conn->beginTransaction();

                    // Check if the orders table has the carbon_offset_amount and estimated_emissions columns
                    $stmt = $conn->prepare("
                        SELECT COUNT(*) as count FROM information_schema.columns
                        WHERE table_schema = DATABASE()
                        AND table_name = 'orders'
                        AND column_name IN ('carbon_offset_amount', 'estimated_emissions')
                    ");
                    $stmt->execute();
                    $columnCheck = $stmt->fetch();

                    // If the columns exist, use the full query
                    if ($columnCheck['count'] == 2) {
                        // Insert order with carbon offset fields
                        $stmt = $conn->prepare("
                            INSERT INTO orders (
                                user_id, restaurant_id, order_status, payment_method, payment_status,
                                subtotal, delivery_fee, total_amount, delivery_address, delivery_latitude,
                                delivery_longitude, notes, no_utensils, eco_packaging, carbon_offset,
                                carbon_offset_amount, estimated_emissions
                            ) VALUES (
                                ?, ?, 'pending', ?, 'pending',
                                ?, ?, ?, ?, ?,
                                ?, ?, ?, ?, ?,
                                ?, ?
                            )
                        ");

                        $stmt->execute([
                            $user_id, $cart['restaurant_id'], $payment_method,
                            $subtotal, $delivery_fee, $total_amount, $address['address'], $address['latitude'],
                            $address['longitude'], $notes, $no_utensils, $eco_packaging, $carbon_offset,
                            $carbon_offset_amount, $estimated_emissions
                        ]);
                    } else {
                        // Insert order without carbon offset fields
                        $stmt = $conn->prepare("
                            INSERT INTO orders (
                                user_id, restaurant_id, order_status, payment_method, payment_status,
                                subtotal, delivery_fee, total_amount, delivery_address, delivery_latitude,
                                delivery_longitude, notes, no_utensils, eco_packaging, carbon_offset
                            ) VALUES (
                                ?, ?, 'pending', ?, 'pending',
                                ?, ?, ?, ?, ?,
                                ?, ?, ?, ?, ?
                            )
                        ");

                        $stmt->execute([
                            $user_id, $cart['restaurant_id'], $payment_method,
                            $subtotal, $delivery_fee, $total_amount, $address['address'], $address['latitude'],
                            $address['longitude'], $notes, $no_utensils, $eco_packaging, $carbon_offset
                        ]);
                    }

                    $order_id = $conn->lastInsertId();

                    // Insert order items
                    $stmt = $conn->prepare("
                        INSERT INTO order_items (
                            order_id, item_id, quantity, price, subtotal
                        ) VALUES (
                            ?, ?, ?, ?, ?
                        )
                    ");

                    foreach ($cart['items'] as $item) {
                        // Ensure item_id is available (backward compatibility)
                        $item_id = isset($item['item_id']) ? $item['item_id'] : (isset($item['id']) ? $item['id'] : null);

                        if ($item_id === null) {
                            // Log error and continue with next item
                            error_log("Error: item_id is null for item: " . json_encode($item));
                            continue;
                        }

                        $item_subtotal = $item['price'] * $item['quantity'];
                        $stmt->execute([
                            $order_id, $item_id, $item['quantity'], $item['price'], $item_subtotal
                        ]);
                    }

                    // Commit transaction
                    $conn->commit();

                    // Clear cart from session
                    if (isset($_SESSION['cart'])) {
                        unset($_SESSION['cart']);
                    }

                    $success = 'Pesanan berhasil dibuat! Nomor pesanan Anda: #' . $order_id;

                    // Redirect to order confirmation page
                    header('Location: order_confirmation.php?id=' . $order_id);
                    exit;
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isUserLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['user_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Masuk</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Daftar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Checkout Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Checkout</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="restaurants.php">Restoran</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Checkout</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <form id="checkout-form" method="post" action="checkout.php">
            <input type="hidden" name="cart_data" id="cart-data">

            <div class="row">
                <div class="col-md-8">
                    <!-- Delivery Address -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Alamat Pengiriman</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($addresses)): ?>
                                <div class="alert alert-warning">
                                    <p>Anda belum memiliki alamat tersimpan.</p>
                                    <a href="user/addresses.php" class="btn btn-sm btn-primary">Tambah Alamat Baru</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($addresses as $address): ?>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="address_id" id="address_<?= $address['address_id'] ?>" value="<?= $address['address_id'] ?>" <?= $address['is_default'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="address_<?= $address['address_id'] ?>">
                                            <strong><?= $address['address_label'] ?></strong>
                                            <p class="mb-0"><?= $address['address'] ?></p>
                                            <?php if ($address['is_default']): ?>
                                                <span class="badge bg-primary">Alamat Utama</span>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                                <div class="mt-3">
                                    <a href="user/addresses.php" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus me-1"></i> Tambah Alamat Baru
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Metode Pembayaran</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_cash" value="cash" checked>
                                <label class="form-check-label" for="payment_cash">
                                    <i class="fas fa-money-bill-wave me-2 text-success"></i> Tunai
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_credit_card" value="credit_card">
                                <label class="form-check-label" for="payment_credit_card">
                                    <i class="fas fa-credit-card me-2 text-primary"></i> Kartu Kredit
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_e_wallet" value="e-wallet">
                                <label class="form-check-label" for="payment_e_wallet">
                                    <i class="fas fa-wallet me-2 text-warning"></i> E-Wallet
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Sustainability Options -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Opsi Keberlanjutan</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="no_utensils" id="no_utensils" value="1">
                                <label class="form-check-label" for="no_utensils">
                                    <i class="fas fa-utensils-slash me-2 text-success"></i> Tanpa Alat Makan
                                    <p class="text-muted small mb-0">Pilih opsi ini jika Anda tidak memerlukan alat makan sekali pakai</p>
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="eco_packaging" id="eco_packaging" value="1">
                                <label class="form-check-label" for="eco_packaging">
                                    <i class="fas fa-box me-2 text-success"></i> Kemasan Ramah Lingkungan
                                    <p class="text-muted small mb-0">Pilih opsi ini untuk mendapatkan kemasan yang lebih ramah lingkungan (mungkin dikenakan biaya tambahan)</p>
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="carbon_offset" id="carbon_offset" value="1">
                                <label class="form-check-label" for="carbon_offset">
                                    <i class="fas fa-leaf me-2 text-success"></i> Offset Karbon
                                    <p class="text-muted small mb-0">Tambahkan Rp2.000 untuk mengimbangi emisi karbon dari pengiriman Anda</p>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Catatan</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="notes" class="form-label">Catatan untuk Restoran</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Misalnya: Tidak pedas, tanpa bawang, dll."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Order Summary -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Ringkasan Pesanan</h5>
                        </div>
                        <div class="card-body">
                            <div id="order-items">
                                <!-- Order items will be dynamically added here -->
                                <p class="text-center text-muted">Memuat item pesanan...</p>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span id="subtotal">Rp0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Biaya Pengiriman:</span>
                                <div class="text-end">
                                    <span id="delivery-fee">Rp0</span>
                                    <div class="btn-group btn-group-sm ms-2">
                                        <button type="button" class="btn btn-outline-primary" onclick="calculateDeliveryFeeByGPS()" title="Gunakan lokasi GPS saya">
                                            <i class="fas fa-location-arrow"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="showManualLocationModal()" title="Pilih lokasi manual">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div id="location-info" class="small text-muted mb-2" style="display: none;"></div>
                            <div class="d-flex justify-content-between mb-2 carbon-offset-row" style="display: none !important;">
                                <span>Offset Karbon:</span>
                                <span id="carbon-offset-fee">Rp2.000</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <span class="fw-bold">Total:</span>
                                <span class="fw-bold" id="total">Rp0</span>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="place-order-btn">
                                    <i class="fas fa-check-circle me-2"></i>Buat Pesanan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Manual Location Modal -->
    <div class="modal fade" id="manualLocationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Pilih Lokasi Pengiriman</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Pilih lokasi pengiriman Anda untuk menghitung biaya yang akurat.
                    </div>

                    <!-- Quick Location Options -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6>Lokasi Populer:</h6>
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100 text-start" onclick="setQuickLocation(-6.2088, 106.8456, 'Jakarta Pusat')">
                                        <i class="fas fa-building"></i> Jakarta Pusat
                                    </button>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100 text-start" onclick="setQuickLocation(-6.1751, 106.8650, 'Jakarta Timur')">
                                        <i class="fas fa-home"></i> Jakarta Timur
                                    </button>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100 text-start" onclick="setQuickLocation(-6.2297, 106.7663, 'Jakarta Barat')">
                                        <i class="fas fa-store"></i> Jakarta Barat
                                    </button>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100 text-start" onclick="setQuickLocation(-6.2615, 106.8106, 'Jakarta Selatan')">
                                        <i class="fas fa-university"></i> Jakarta Selatan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Try GPS Again -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-success w-100" onclick="tryGPSAgain()">
                            <i class="fas fa-location-arrow"></i> Coba GPS Lagi
                        </button>
                        <small class="text-muted d-block mt-1 text-center">
                            Pastikan Anda mengizinkan akses lokasi di browser
                        </small>
                    </div>

                    <!-- Selected Location Display -->
                    <div id="selected-location" class="alert alert-success" style="display: none;">
                        <h6><i class="fas fa-map-marker-alt"></i> Lokasi Terpilih:</h6>
                        <p id="location-name" class="mb-1"></p>
                        <small class="text-muted">
                            Koordinat: <span id="location-coords"></span>
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" onclick="useManualLocation()" disabled id="use-manual-btn">
                        Gunakan Lokasi Ini
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- GPS Permission Help Modal -->
    <div class="modal fade" id="gpsHelpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cara Mengaktifkan GPS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Akses lokasi diperlukan untuk menghitung biaya pengiriman yang akurat.
                    </div>

                    <h6>Langkah-langkah mengaktifkan GPS:</h6>
                    <ol>
                        <li>Klik ikon <i class="fas fa-lock"></i> atau <i class="fas fa-info-circle"></i> di address bar browser</li>
                        <li>Pilih "Allow" atau "Izinkan" untuk Location/Lokasi</li>
                        <li>Refresh halaman dan coba lagi</li>
                    </ol>

                    <div class="alert alert-info">
                        <strong>Alternatif:</strong> Jika GPS tidak bisa diaktifkan, Anda bisa memilih lokasi manual dengan mengklik tombol peta.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-primary" onclick="tryGPSAgain()">Coba GPS Lagi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Try to load cart from server first
            fetch('api/cart.php')
                .then(response => response.json())
                .then(data => {
                    let cart;
                    if (data.success && data.cart) {
                        // Update localStorage with server cart data
                        localStorage.setItem('cart', JSON.stringify(data.cart));
                        cart = data.cart;
                    } else {
                        // Fall back to localStorage if server data not found
                        cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };
                    }

                    // Set cart data to hidden input
                    document.getElementById('cart-data').value = JSON.stringify(cart);

                    // Update order summary
                    updateOrderSummary(cart);
                })
                .catch(error => {
                    console.error('Error loading cart from server:', error);
                    // Fall back to localStorage if server request fails
                    const cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };

                    // Set cart data to hidden input
                    document.getElementById('cart-data').value = JSON.stringify(cart);

                    // Update order summary
                    updateOrderSummary(cart);
                });

            // Handle carbon offset checkbox
            const carbonOffsetCheckbox = document.getElementById('carbon_offset');
            const carbonOffsetRow = document.querySelector('.carbon-offset-row');

            carbonOffsetCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    carbonOffsetRow.style.display = 'flex';
                } else {
                    carbonOffsetRow.style.display = 'none';
                }
                updateOrderSummary(cart);
            });

            // Form validation
            const checkoutForm = document.getElementById('checkout-form');

            checkoutForm.addEventListener('submit', function(event) {
                if (cart.items.length === 0) {
                    event.preventDefault();
                    alert('Keranjang belanja Anda kosong');
                    return;
                }

                const addressSelected = document.querySelector('input[name="address_id"]:checked');
                if (!addressSelected) {
                    event.preventDefault();
                    alert('Silakan pilih alamat pengiriman');
                    return;
                }

                const paymentSelected = document.querySelector('input[name="payment_method"]:checked');
                if (!paymentSelected) {
                    event.preventDefault();
                    alert('Silakan pilih metode pembayaran');
                    return;
                }
            });
        });

        function updateOrderSummary(cart) {
            const orderItemsContainer = document.getElementById('order-items');
            const subtotalElement = document.getElementById('subtotal');
            const deliveryFeeElement = document.getElementById('delivery-fee');
            const totalElement = document.getElementById('total');
            const carbonOffsetCheckbox = document.getElementById('carbon_offset');

            // Clear order items
            orderItemsContainer.innerHTML = '';

            if (cart.items.length === 0) {
                orderItemsContainer.innerHTML = '<p class="text-center text-muted">Keranjang belanja Anda kosong</p>';
                subtotalElement.textContent = 'Rp0';
                deliveryFeeElement.textContent = 'Rp0';
                totalElement.textContent = 'Rp0';
                return;
            }

            // Calculate subtotal
            let subtotal = 0;

            // Add order items
            cart.items.forEach(item => {
                // Convert price to Rupiah (1 unit = Rp 15,000)
                const priceInRupiah = item.price * 15000;
                const itemTotal = priceInRupiah * item.quantity;
                subtotal += itemTotal;

                const itemElement = document.createElement('div');
                itemElement.className = 'mb-3';
                itemElement.innerHTML = `
                    <div class="d-flex justify-content-between">
                        <div>
                            <span class="fw-bold">${item.quantity}x</span> ${item.name}
                        </div>
                        <div>
                            Rp${numberFormat(itemTotal)}
                        </div>
                    </div>
                    <div class="small text-muted">
                        Rp${numberFormat(priceInRupiah)} per item
                    </div>
                `;

                orderItemsContainer.appendChild(itemElement);
            });

            // Set subtotal
            subtotalElement.textContent = `Rp${numberFormat(subtotal)}`;

            // Set delivery fee (will be updated by GPS calculation)
            let deliveryFee = 15000; // Default fallback
            deliveryFeeElement.textContent = `Rp${numberFormat(deliveryFee)}`;

            // Try to calculate delivery fee based on GPS location
            calculateDeliveryFeeByGPS();

            // Calculate total
            let total = subtotal + deliveryFee;

            // Add carbon offset fee if selected
            if (carbonOffsetCheckbox.checked) {
                total += 2000;
            }

            // Set total
            totalElement.textContent = `Rp${numberFormat(total)}`;
        }

        function numberFormat(number) {
            return new Intl.NumberFormat('id-ID').format(number);
        }

        // Calculate delivery fee based on GPS location
        function calculateDeliveryFeeByGPS() {
            if (!navigator.geolocation) {
                console.log('Geolocation is not supported by this browser.');
                return;
            }

            // Show loading indicator
            const deliveryFeeElement = document.getElementById('delivery-fee');
            const originalText = deliveryFeeElement.textContent;
            deliveryFeeElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menghitung...';

            const options = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes cache
            };

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;

                    // Get restaurant ID from cart
                    const cart = JSON.parse(localStorage.getItem('cart') || '{}');
                    if (!cart.restaurant_id) {
                        console.error('Restaurant ID not found in cart');
                        deliveryFeeElement.textContent = originalText;
                        return;
                    }

                    // Call API to calculate delivery fee
                    fetch('api/calculate_delivery.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            restaurant_id: cart.restaurant_id,
                            user_lat: userLat,
                            user_lng: userLng
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update delivery fee
                            deliveryFee = data.delivery_fee.rupiah;
                            deliveryFeeElement.textContent = data.delivery_fee.formatted;

                            // Show distance and estimated time
                            const distanceInfo = document.createElement('small');
                            distanceInfo.className = 'text-muted d-block';
                            distanceInfo.textContent = `Jarak: ${data.distance} km • Estimasi: ${data.estimated_time} menit`;
                            deliveryFeeElement.appendChild(distanceInfo);

                            // Recalculate total
                            updateTotal();

                            console.log(`Delivery calculated: ${data.distance} km, ${data.delivery_fee.formatted}`);
                        } else {
                            console.error('Error calculating delivery fee:', data.error);
                            deliveryFeeElement.textContent = originalText;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        deliveryFeeElement.textContent = originalText;
                    });
                },
                function(error) {
                    console.error('Geolocation error:', error.message);
                    deliveryFeeElement.textContent = originalText;

                    // Handle different GPS errors
                    let errorMessage = '';
                    let showHelpModal = false;

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Akses lokasi ditolak. Klik tombol peta untuk pilih lokasi manual.';
                            showHelpModal = true;
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Lokasi tidak tersedia. Menggunakan tarif standar.';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'GPS timeout. Coba lagi atau pilih lokasi manual.';
                            break;
                        default:
                            errorMessage = 'GPS tidak tersedia, menggunakan tarif standar';
                            break;
                    }

                    // Show error message
                    const locationInfo = document.getElementById('location-info');
                    locationInfo.innerHTML = `<i class="fas fa-exclamation-triangle text-warning"></i> ${errorMessage}`;
                    locationInfo.style.display = 'block';

                    // Show help modal for permission denied
                    if (showHelpModal) {
                        setTimeout(() => {
                            const helpModal = new bootstrap.Modal(document.getElementById('gpsHelpModal'));
                            helpModal.show();
                        }, 1000);
                    }
                },
                options
            );
        }

        // Update total calculation
        function updateTotal() {
            const subtotalElement = document.getElementById('subtotal');
            const deliveryFeeElement = document.getElementById('delivery-fee');
            const totalElement = document.getElementById('total');
            const carbonOffsetCheckbox = document.getElementById('carbon-offset');

            // Parse current values
            const subtotalText = subtotalElement.textContent.replace(/[^\d]/g, '');
            const subtotal = parseInt(subtotalText) || 0;

            // Calculate total
            let total = subtotal + deliveryFee;

            // Add carbon offset fee if selected
            if (carbonOffsetCheckbox && carbonOffsetCheckbox.checked) {
                total += 2000;
            }

            // Update total display
            totalElement.textContent = `Rp${numberFormat(total)}`;
        }

        // Show manual location modal
        function showManualLocationModal() {
            const modal = new bootstrap.Modal(document.getElementById('manualLocationModal'));
            modal.show();
        }

        // Set quick location
        let selectedLat, selectedLng, selectedName;

        function setQuickLocation(lat, lng, name) {
            selectedLat = lat;
            selectedLng = lng;
            selectedName = name;

            // Update UI
            document.getElementById('location-name').textContent = name;
            document.getElementById('location-coords').textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            document.getElementById('selected-location').style.display = 'block';
            document.getElementById('use-manual-btn').disabled = false;
        }

        // Try GPS again
        function tryGPSAgain() {
            // Close any open modals
            const helpModal = bootstrap.Modal.getInstance(document.getElementById('gpsHelpModal'));
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('manualLocationModal'));

            if (helpModal) helpModal.hide();
            if (locationModal) locationModal.hide();

            // Try GPS again
            setTimeout(() => {
                calculateDeliveryFeeByGPS();
            }, 500);
        }

        // Use manual location
        function useManualLocation() {
            if (!selectedLat || !selectedLng) {
                alert('Pilih lokasi terlebih dahulu');
                return;
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('manualLocationModal'));
            modal.hide();

            // Calculate delivery fee using selected location
            calculateDeliveryFeeWithCoords(selectedLat, selectedLng, selectedName);
        }

        // Calculate delivery fee with specific coordinates
        function calculateDeliveryFeeWithCoords(lat, lng, locationName) {
            const deliveryFeeElement = document.getElementById('delivery-fee');
            const locationInfo = document.getElementById('location-info');

            // Show loading
            deliveryFeeElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menghitung...';

            // Get restaurant ID from cart
            const cart = JSON.parse(localStorage.getItem('cart') || '{}');
            if (!cart.restaurant_id) {
                console.error('Restaurant ID not found in cart');
                deliveryFeeElement.textContent = 'Rp15.000';
                return;
            }

            // Call API
            fetch('api/calculate_delivery.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    restaurant_id: cart.restaurant_id,
                    user_lat: lat,
                    user_lng: lng
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update delivery fee
                    deliveryFee = data.delivery_fee.rupiah;
                    deliveryFeeElement.textContent = data.delivery_fee.formatted;

                    // Show location and delivery info
                    locationInfo.innerHTML = `
                        <i class="fas fa-map-marker-alt text-success"></i>
                        ${locationName || 'Lokasi terpilih'} •
                        Jarak: ${data.distance} km •
                        Estimasi: ${data.estimated_time} menit
                    `;
                    locationInfo.style.display = 'block';

                    // Recalculate total
                    updateTotal();

                    console.log(`Manual location delivery calculated: ${data.distance} km, ${data.delivery_fee.formatted}`);
                } else {
                    console.error('Error calculating delivery fee:', data.error);
                    deliveryFeeElement.textContent = 'Rp15.000';
                    locationInfo.innerHTML = `<i class="fas fa-exclamation-triangle text-warning"></i> Error: ${data.error}`;
                    locationInfo.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                deliveryFeeElement.textContent = 'Rp15.000';
                locationInfo.innerHTML = `<i class="fas fa-exclamation-triangle text-danger"></i> Network error: ${error.message}`;
                locationInfo.style.display = 'block';
            });
        }
    </script>
</body>
</html>
