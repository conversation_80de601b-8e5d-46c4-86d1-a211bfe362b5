<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get restaurant owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Get restaurant information
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurant = $stmt->fetch();
$restaurant_id = $restaurant['restaurant_id'];

// Get active room ID from URL if present
$active_room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;

// Check if the room exists and owner has access
$has_access = false;
if ($active_room_id > 0) {
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM chat_participants 
        WHERE room_id = :room_id AND user_type = 'restaurant_owner' AND user_id = :owner_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':owner_id', $owner_id);
    $stmt->execute();
    $has_access = ($stmt->fetchColumn() > 0);
    
    if (!$has_access) {
        $active_room_id = 0;
    }
}

// Get chat rooms for this restaurant owner
$stmt = $conn->prepare("
    SELECT cr.*, 
           o.order_id, 
           o.total_amount,
           u.name AS customer_name,
           d.name AS driver_name,
           (SELECT COUNT(*) FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            AND cm.is_read = 0
            AND cm.sender_type != 'restaurant_owner'
            AND cm.sender_id != :owner_id) AS unread_count,
           (SELECT cm.message_text FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message,
           (SELECT cm.created_at FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message_time
    FROM chat_rooms cr
    JOIN chat_participants cp ON cr.room_id = cp.room_id
    LEFT JOIN orders o ON cr.order_id = o.order_id
    LEFT JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    WHERE cp.user_type = 'restaurant_owner' AND cp.user_id = :owner_id
    ORDER BY cr.updated_at DESC
");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$chat_rooms = $stmt->fetchAll();

// If no active room but rooms exist, select the first one
if ($active_room_id == 0 && count($chat_rooms) > 0) {
    $active_room_id = $chat_rooms[0]['room_id'];
    $has_access = true;
}

// Get room details and participants if active room exists
$room_details = null;
$participants = [];
if ($active_room_id > 0) {
    // Get room details
    $stmt = $conn->prepare("
        SELECT cr.*, 
               o.order_id, 
               o.total_amount,
               u.name AS customer_name,
               u.user_id,
               d.name AS driver_name,
               d.driver_id
        FROM chat_rooms cr
        LEFT JOIN orders o ON cr.order_id = o.order_id
        LEFT JOIN users u ON o.user_id = u.user_id
        LEFT JOIN drivers d ON o.driver_id = d.driver_id
        WHERE cr.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $room_details = $stmt->fetch();
    
    // Get participants
    $stmt = $conn->prepare("
        SELECT cp.*, 
               u.name AS customer_name,
               ro.name AS restaurant_owner_name,
               d.name AS driver_name,
               a.name AS admin_name
        FROM chat_participants cp
        LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
        LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
        LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
        LEFT JOIN admins a ON cp.user_type = 'admin' AND cp.user_id = a.admin_id
        WHERE cp.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $participants = $stmt->fetchAll();
    
    // Mark messages as read
    $stmt = $conn->prepare("
        UPDATE chat_messages 
        SET is_read = 1 
        WHERE room_id = :room_id 
        AND sender_type != 'restaurant_owner' 
        AND sender_id != :owner_id
        AND is_read = 0
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':owner_id', $owner_id);
    $stmt->execute();
    
    // Update last read timestamp
    $stmt = $conn->prepare("
        UPDATE chat_participants 
        SET last_read_at = NOW() 
        WHERE room_id = :room_id 
        AND user_type = 'restaurant_owner' 
        AND user_id = :owner_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':owner_id', $owner_id);
    $stmt->execute();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/chat.css">
</head>
<body data-user-type="restaurant_owner" data-user-id="<?= $owner_id ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship - Restoran
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">Pesanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="menu.php">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="chat.php">Chat</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $owner['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="restaurant_profile.php">Profil Restoran</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Chat Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Chat</h1>
                <p class="text-muted">Komunikasi dengan pelanggan dan pengemudi</p>
            </div>
        </div>

        <div class="row">
            <!-- Chat Rooms List -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Percakapan</h5>
                    </div>
                    <div class="list-group list-group-flush" id="chat-rooms-list">
                        <?php if (count($chat_rooms) === 0): ?>
                            <div class="text-center p-3">Belum ada percakapan</div>
                        <?php else: ?>
                            <?php foreach ($chat_rooms as $room): ?>
                                <?php
                                $unreadBadge = $room['unread_count'] > 0 ? '<span class="badge bg-danger rounded-pill">' . $room['unread_count'] . '</span>' : '';
                                $lastMessageTime = $room['last_message_time'] ? date('H:i', strtotime($room['last_message_time'])) : '';
                                
                                $roomTitle = '';
                                if ($room['room_type'] === 'order') {
                                    $roomTitle = 'Pesanan #' . $room['order_id'];
                                } elseif ($room['room_type'] === 'complaint') {
                                    $roomTitle = 'Dukungan Keluhan';
                                } else {
                                    $roomTitle = 'Chat Dukungan';
                                }
                                
                                $participantInfo = '';
                                if ($room['customer_name']) $participantInfo .= '<div class="small text-muted">Pelanggan: ' . $room['customer_name'] . '</div>';
                                if ($room['driver_name']) $participantInfo .= '<div class="small text-muted">Pengemudi: ' . $room['driver_name'] . '</div>';
                                ?>
                                <a href="chat.php?room_id=<?= $room['room_id'] ?>" class="chat-room-item list-group-item list-group-item-action d-flex justify-content-between align-items-start <?= $room['room_id'] == $active_room_id ? 'active' : '' ?> <?= $room['status'] === 'closed' ? 'bg-light' : '' ?>" data-room-id="<?= $room['room_id'] ?>">
                                    <div class="ms-2 me-auto">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?= $roomTitle ?></h6>
                                            <small><?= $lastMessageTime ?></small>
                                        </div>
                                        <?= $participantInfo ?>
                                        <p class="mb-1 text-truncate"><?= $room['last_message'] ?: 'Belum ada pesan' ?></p>
                                    </div>
                                    <?= $unreadBadge ?>
                                </a>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div class="col-md-8">
                <?php if ($active_room_id > 0 && $has_access): ?>
                    <div class="card chat-card">
                        <div class="card-header bg-primary text-white">
                            <?php
                            $headerTitle = '';
                            if ($room_details['room_type'] === 'order') {
                                $headerTitle = 'Pesanan #' . $room_details['order_id'];
                                if ($room_details['customer_name']) $headerTitle .= ' - ' . $room_details['customer_name'];
                            } elseif ($room_details['room_type'] === 'complaint') {
                                $headerTitle = 'Dukungan Keluhan';
                            } else {
                                $headerTitle = 'Chat Dukungan';
                            }
                            ?>
                            <h5 class="card-title mb-0"><?= $headerTitle ?></h5>
                            <?php if ($room_details['status'] === 'closed'): ?>
                                <span class="badge bg-secondary">Ditutup</span>
                            <?php endif; ?>
                        </div>
                        <div class="card-body p-0">
                            <div class="chat-messages" id="chat-messages" data-room-id="<?= $active_room_id ?>">
                                <div class="text-center p-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Memuat...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if ($room_details['status'] === 'active'): ?>
                                <form id="message-form" class="d-flex">
                                    <input type="text" id="message-input" class="form-control me-2" placeholder="Ketik pesan..." required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </form>
                                <div class="mt-2 d-flex">
                                    <button id="emoji-button" class="btn btn-sm btn-outline-secondary me-2" type="button">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <button id="share-location" class="btn btn-sm btn-outline-secondary me-2" type="button">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </button>
                                    <div class="position-relative">
                                        <input type="file" id="file-upload" class="d-none" accept="image/*">
                                        <button id="upload-button" class="btn btn-sm btn-outline-secondary" type="button" onclick="document.getElementById('file-upload').click()">
                                            <i class="fas fa-image"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-secondary mb-0">
                                    Percakapan ini telah ditutup dan tidak dapat menerima pesan baru.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center p-5">
                            <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                            <h3>Tidak ada percakapan yang dipilih</h3>
                            <p class="text-muted">Pilih percakapan dari daftar atau mulai percakapan baru.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship - Portal Restoran</h5>
                    <p>Kelola restoran Anda dengan mudah.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/user-experience.js"></script>
    <script src="../js/chat.js"></script>
</body>
</html>
