<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);

// Get user name if logged in
$userName = '';
if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}

// Connect to database
$conn = connectDB();

// Get eco-friendly drivers count
$stmt = $conn->prepare("
    SELECT COUNT(*) as eco_drivers
    FROM drivers
    WHERE is_eco_friendly = 1
");
$stmt->execute();
$ecoDrivers = $stmt->fetch(PDO::FETCH_ASSOC)['eco_drivers'];

// Get carbon offset stats
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT o.order_id) as offset_orders,
        SUM(o.carbon_offset_amount) as total_offset_amount,
        SUM(o.estimated_emissions) as total_emissions
    FROM orders o
    WHERE o.carbon_offset = 1
");
$stmt->execute();
$carbonOffsets = $stmt->fetch(PDO::FETCH_ASSOC);

// Get carbon offset projects
$stmt = $conn->prepare("
    SELECT *
    FROM carbon_offset_projects
    WHERE is_active = 1
    ORDER BY name
");
$stmt->execute();
$offsetProjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get sustainability tips
$stmt = $conn->prepare("
    SELECT *
    FROM sustainability_tips
    WHERE category IN ('transportation', 'packaging')
    ORDER BY RAND()
    LIMIT 3
");
$stmt->execute();
$tips = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inisiatif Hijau - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
        .green-icon {
            font-size: 3rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .eco-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s;
            height: 100%;
        }
        
        .eco-card:hover {
            transform: translateY(-5px);
        }
        
        .hero-green {
            background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/green-delivery-hero.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 5rem 0;
            margin-bottom: 3rem;
        }
        
        .carbon-calculator {
            background-color: #f8f9fa;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .tip-card {
            border-left: 4px solid #28a745;
        }
        
        .project-progress {
            height: 10px;
            border-radius: 5px;
        }
        
        .eco-badge {
            background-color: #28a745;
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="driver/orders.php">Pengiriman</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="owner/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Masuk</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Daftar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-green">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Inisiatif Hijau</h1>
            <p class="lead mb-4">Mengurangi jejak karbon dari pengiriman makanan, satu pesanan pada satu waktu.</p>
            <a href="#carbon-calculator" class="btn btn-success btn-lg">
                <i class="fas fa-calculator me-2"></i>Hitung Jejak Karbon Anda
            </a>
        </div>
    </section>

    <!-- Content -->
    <div class="container py-5">
        <!-- Green Delivery -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Pengiriman Ramah Lingkungan</h2>
            <p class="text-center text-muted mb-5">Kami berkomitmen untuk mengurangi dampak lingkungan dari operasi pengiriman kami.</p>
            
            <div class="row mb-5">
                <div class="col-md-6">
                    <img src="images/eco-delivery-bike.jpg" alt="Pengiriman dengan Sepeda" class="img-fluid rounded">
                </div>
                <div class="col-md-6">
                    <h3><i class="fas fa-bicycle text-success me-2"></i>Pengiriman dengan Sepeda</h3>
                    <p>Di area pusat kota, kami menawarkan pengiriman dengan sepeda yang tidak menghasilkan emisi. Ini tidak hanya baik untuk lingkungan tetapi juga sering lebih cepat di jalan-jalan yang padat.</p>
                    
                    <div class="d-flex align-items-center mt-4">
                        <div class="flex-shrink-0">
                            <span class="display-4 text-success"><?= $ecoDrivers ?></span>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-0">Pengemudi sepeda aktif di platform kami</p>
                            <p class="text-muted">Membantu mengurangi emisi CO₂ setiap hari</p>
                        </div>
                    </div>
                    
                    <p class="mt-4">Saat memesan, cari badge <span class="eco-badge"><i class="fas fa-bicycle me-1"></i>Eco-Friendly</span> pada pengemudi untuk mendukung inisiatif ini.</p>
                </div>
            </div>
            
            <div class="row mb-5">
                <div class="col-md-6 order-md-2">
                    <img src="images/electric-scooter.jpg" alt="Kendaraan Listrik" class="img-fluid rounded">
                </div>
                <div class="col-md-6 order-md-1">
                    <h3><i class="fas fa-charging-station text-success me-2"></i>Kendaraan Listrik</h3>
                    <p>Untuk pengiriman jarak menengah, kami mendorong pengemudi kami untuk menggunakan skuter dan sepeda motor listrik. Kendaraan ini menghasilkan nol emisi langsung dan membantu mengurangi polusi udara di kota.</p>
                    
                    <div class="alert alert-success mt-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle fa-2x me-3"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Tahukah Anda?</h5>
                                <p class="mb-0">Sepeda motor listrik menghasilkan 75% lebih sedikit emisi CO₂ dibandingkan sepeda motor berbahan bakar bensin, bahkan memperhitungkan produksi listrik.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <img src="images/route-optimization.jpg" alt="Optimasi Rute" class="img-fluid rounded">
                </div>
                <div class="col-md-6">
                    <h3><i class="fas fa-route text-success me-2"></i>Optimasi Rute</h3>
                    <p>Algoritma cerdas kami menghitung rute paling efisien untuk pengemudi kami, mengurangi jarak tempuh dan konsumsi bahan bakar. Ini membantu mengurangi emisi CO₂ bahkan untuk pengiriman dengan kendaraan konvensional.</p>
                    
                    <div class="alert alert-light border mt-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lightbulb fa-2x me-3 text-warning"></i>
                            </div>
                            <div>
                                <h5>Optimasi Multi-Pesanan</h5>
                                <p class="mb-0">Pengemudi kami dapat mengambil beberapa pesanan dalam satu perjalanan jika restoran dan tujuan pengiriman berada di area yang sama, lebih lanjut mengurangi emisi per pesanan.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Carbon Calculator -->
        <section class="mb-5" id="carbon-calculator">
            <h2 class="text-center mb-4">Kalkulator Jejak Karbon</h2>
            <p class="text-center text-muted mb-5">Hitung dan imbangi jejak karbon dari pesanan Anda.</p>
            
            <div class="carbon-calculator">
                <div class="row">
                    <div class="col-md-6">
                        <h4 class="mb-4">Hitung Emisi CO₂ Anda</h4>
                        <form id="carbon-form">
                            <div class="mb-3">
                                <label for="distance" class="form-label">Jarak Pengiriman (km)</label>
                                <input type="number" class="form-control" id="distance" min="1" max="50" value="5">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Metode Pengiriman</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_method" id="bicycle" value="bicycle" checked>
                                    <label class="form-check-label" for="bicycle">
                                        <i class="fas fa-bicycle text-success me-2"></i>Sepeda (0 g CO₂/km)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_method" id="electric" value="electric">
                                    <label class="form-check-label" for="electric">
                                        <i class="fas fa-charging-station text-success me-2"></i>Kendaraan Listrik (12 g CO₂/km)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_method" id="motorcycle" value="motorcycle">
                                    <label class="form-check-label" for="motorcycle">
                                        <i class="fas fa-motorcycle text-warning me-2"></i>Sepeda Motor (103 g CO₂/km)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_method" id="car" value="car">
                                    <label class="form-check-label" for="car">
                                        <i class="fas fa-car text-danger me-2"></i>Mobil (192 g CO₂/km)
                                    </label>
                                </div>
                            </div>
                            
                            <button type="button" id="calculate-btn" class="btn btn-success">
                                <i class="fas fa-calculator me-2"></i>Hitung Emisi
                            </button>
                        </form>
                    </div>
                    
                    <div class="col-md-6">
                        <div id="result-container" class="d-none">
                            <h4 class="mb-4">Hasil Perhitungan</h4>
                            
                            <div class="card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Emisi CO₂ dari Pengiriman Anda</h5>
                                    <p class="display-4 text-center my-4" id="emission-result">0 g</p>
                                    <p class="card-text text-muted text-center" id="emission-context"></p>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Imbangi Jejak Karbon Anda</h5>
                                    <p class="card-text">Anda dapat mengimbangi emisi CO₂ dari pesanan Anda dengan donasi kecil ke proyek penanaman pohon.</p>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" id="offset-btn" class="btn btn-outline-success">
                                            <i class="fas fa-leaf me-2"></i>Imbangi untuk <span id="offset-amount">Rp 0</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="initial-content">
                            <h4 class="mb-4">Mengapa Ini Penting?</h4>
                            <p>Transportasi adalah salah satu kontributor utama emisi gas rumah kaca. Dengan menghitung dan mengimbangi jejak karbon dari pesanan makanan Anda, Anda dapat membantu memerangi perubahan iklim.</p>
                            
                            <div class="alert alert-success mt-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-tree fa-2x me-3"></i>
                                    </div>
                                    <div>
                                        <h5 class="alert-heading">Dampak Kolektif</h5>
                                        <p class="mb-0">Sejauh ini, pengguna KikaZen Ship telah mengimbangi <?= number_format($carbonOffsets['total_emissions'], 1) ?> kg CO₂ melalui program ini!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Carbon Offset Projects -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Proyek Offset Karbon Kami</h2>
            <p class="text-center text-muted mb-5">Donasi offset karbon Anda mendukung proyek-proyek ini.</p>
            
            <div class="row row-cols-1 row-cols-md-3 g-4">
                <?php foreach ($offsetProjects as $project): ?>
                    <div class="col">
                        <div class="card h-100 eco-card">
                            <img src="images/projects/<?= strtolower(str_replace(' ', '-', $project['project_type'])) ?>.jpg" class="card-img-top" alt="<?= $project['name'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $project['name'] ?></h5>
                                <p class="card-text"><?= $project['description'] ?></p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-2"></i><?= $project['location'] ?>
                                    </small>
                                </p>
                                
                                <?php 
                                    // Calculate progress percentage (for demo purposes)
                                    $targetFunding = 50000000; // 50 million IDR
                                    $progressPercent = min(100, ($project['total_funded'] / $targetFunding) * 100);
                                ?>
                                
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <small>Progress</small>
                                        <small><?= number_format($progressPercent, 0) ?>%</small>
                                    </div>
                                    <div class="progress project-progress">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $progressPercent ?>%" aria-valuenow="<?= $progressPercent ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted">Rp <?= number_format($project['total_funded']) ?> terkumpul</small>
                                </div>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="project_detail.php?id=<?= $project['project_id'] ?>" class="btn btn-outline-success w-100">
                                    <i class="fas fa-info-circle me-2"></i>Pelajari Lebih Lanjut
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Sustainability Tips -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Tips Keberlanjutan</h2>
            
            <div class="row">
                <?php foreach ($tips as $tip): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 tip-card">
                            <div class="card-body">
                                <h5 class="card-title"><?= $tip['title'] ?></h5>
                                <p class="card-text"><?= $tip['content'] ?></p>
                                <span class="badge bg-success"><?= ucfirst($tip['category']) ?></span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="sustainability_tips.php" class="btn btn-outline-success">
                    <i class="fas fa-lightbulb me-2"></i>Lihat Semua Tips
                </a>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat dan bertanggung jawab.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="sustainability.php" class="text-white">Keberlanjutan</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
                <p class="text-muted small">Dicetak di atas kertas digital untuk menyelamatkan pohon. 🌳</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Carbon Calculator JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculateBtn = document.getElementById('calculate-btn');
            const resultContainer = document.getElementById('result-container');
            const initialContent = document.getElementById('initial-content');
            const emissionResult = document.getElementById('emission-result');
            const emissionContext = document.getElementById('emission-context');
            const offsetAmount = document.getElementById('offset-amount');
            const offsetBtn = document.getElementById('offset-btn');
            
            // Emission factors in g CO2 per km
            const emissionFactors = {
                bicycle: 0,
                electric: 12,
                motorcycle: 103,
                car: 192
            };
            
            // Cost per kg of CO2 offset (in IDR)
            const offsetCostPerKg = 2000;
            
            calculateBtn.addEventListener('click', function() {
                // Get input values
                const distance = parseFloat(document.getElementById('distance').value);
                const deliveryMethod = document.querySelector('input[name="delivery_method"]:checked').value;
                
                // Calculate emissions
                const emissionFactor = emissionFactors[deliveryMethod];
                const emissions = distance * emissionFactor;
                
                // Calculate offset cost (minimum 2000 IDR)
                const offsetCost = Math.max(2000, Math.ceil(emissions / 1000) * offsetCostPerKg);
                
                // Update UI
                emissionResult.textContent = emissions.toFixed(0) + ' g';
                offsetAmount.textContent = 'Rp ' + offsetCost.toLocaleString('id-ID');
                
                // Add context
                if (emissions === 0) {
                    emissionContext.textContent = 'Pengiriman tanpa emisi! Terima kasih telah memilih opsi ramah lingkungan.';
                } else if (emissions < 100) {
                    emissionContext.textContent = 'Emisi sangat rendah. Setara dengan menghidupkan lampu LED 10W selama ' + Math.ceil(emissions / 10) + ' jam.';
                } else if (emissions < 500) {
                    emissionContext.textContent = 'Emisi rendah. Setara dengan mengemudi mobil bensin selama ' + (emissions / 192).toFixed(1) + ' km.';
                } else {
                    emissionContext.textContent = 'Emisi sedang. Setara dengan mengemudi mobil bensin selama ' + (emissions / 192).toFixed(1) + ' km.';
                }
                
                // Show result
                resultContainer.classList.remove('d-none');
                initialContent.classList.add('d-none');
            });
            
            offsetBtn.addEventListener('click', function() {
                alert('Fitur ini akan segera hadir! Anda akan dapat mengimbangi jejak karbon Anda saat checkout.');
            });
        });
    </script>
</body>
</html>
