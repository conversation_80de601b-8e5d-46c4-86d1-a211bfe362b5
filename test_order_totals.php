<?php
// Test file untuk memverifikasi konsistensi total pesanan
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Test Konsistensi Total Pesanan</h2>";

$conn = connectDB();

// Get sample orders
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, u.name as customer_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    ORDER BY o.created_at DESC
    LIMIT 10
");
$stmt->execute();
$orders = $stmt->fetchAll();

echo "<h3>Test Fungsi formatCurrency</h3>";
echo "<p>Fungsi formatCurrency sekarang memiliki parameter multiply untuk konsistensi:</p>";

$test_amounts = [5.07, 76.05, 900];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Amount (Unit)</th><th>formatCurrency(amount, true)</th><th>formatCurrency(amount, false)</th><th>Manual * 15000</th></tr>";

foreach ($test_amounts as $amount) {
    echo "<tr>";
    echo "<td>$amount</td>";
    echo "<td>" . formatCurrency($amount, true) . "</td>";
    echo "<td>" . formatCurrency($amount, false) . "</td>";
    echo "<td>Rp" . number_format($amount * 15000, 0, ',', '.') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Analisis Pesanan Aktual</h3>";

if (!empty($orders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<th>Order ID</th>";
    echo "<th>Restaurant</th>";
    echo "<th>Customer</th>";
    echo "<th>Total Amount (DB)</th>";
    echo "<th>Dashboard Display</th>";
    echo "<th>Detail Display</th>";
    echo "<th>Status</th>";
    echo "</tr>";
    
    foreach ($orders as $order) {
        echo "<tr>";
        echo "<td>#" . $order['order_id'] . "</td>";
        echo "<td>" . $order['restaurant_name'] . "</td>";
        echo "<td>" . $order['customer_name'] . "</td>";
        echo "<td>" . $order['total_amount'] . " units</td>";
        echo "<td>" . formatCurrency($order['total_amount']) . "</td>";
        echo "<td>" . formatCurrency($order['total_amount']) . "</td>";
        
        // Check if amounts match
        $dashboard_amount = $order['total_amount'] * 15000;
        $detail_amount = $order['total_amount'] * 15000;
        
        if ($dashboard_amount == $detail_amount) {
            echo "<td style='color: green;'>✓ Konsisten</td>";
        } else {
            echo "<td style='color: red;'>✗ Tidak Konsisten</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Tidak ada pesanan ditemukan.</p>";
}

// Test specific order details
echo "<h3>Test Detail Pesanan Spesifik</h3>";

if (!empty($orders)) {
    $test_order = $orders[0];
    $order_id = $test_order['order_id'];
    
    echo "<h4>Order #$order_id</h4>";
    
    // Get order items
    $stmt = $conn->prepare("
        SELECT oi.*, m.name as item_name, m.price as item_price
        FROM order_items oi
        JOIN menu_items m ON oi.item_id = m.item_id
        WHERE oi.order_id = :order_id
    ");
    $stmt->bindParam(':order_id', $order_id);
    $stmt->execute();
    $orderItems = $stmt->fetchAll();
    
    echo "<h5>Item Breakdown:</h5>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Item</th><th>Price (Unit)</th><th>Quantity</th><th>Subtotal (Unit)</th><th>Subtotal (Rupiah)</th></tr>";
    
    $calculated_subtotal = 0;
    foreach ($orderItems as $item) {
        $item_subtotal = $item['item_price'] * $item['quantity'];
        $calculated_subtotal += $item_subtotal;
        
        echo "<tr>";
        echo "<td>" . $item['item_name'] . "</td>";
        echo "<td>" . $item['item_price'] . "</td>";
        echo "<td>" . $item['quantity'] . "</td>";
        echo "<td>" . $item_subtotal . "</td>";
        echo "<td>" . formatCurrency($item_subtotal) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h5>Total Calculation:</h5>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Component</th><th>Value (Unit)</th><th>Value (Rupiah)</th></tr>";
    echo "<tr><td>Calculated Subtotal</td><td>$calculated_subtotal</td><td>" . formatCurrency($calculated_subtotal) . "</td></tr>";
    echo "<tr><td>DB Subtotal</td><td>" . $test_order['subtotal'] . "</td><td>" . formatCurrency($test_order['subtotal']) . "</td></tr>";
    echo "<tr><td>Delivery Fee</td><td>" . $test_order['delivery_fee'] . "</td><td>" . formatCurrency($test_order['delivery_fee']) . "</td></tr>";
    echo "<tr><td>DB Total Amount</td><td>" . $test_order['total_amount'] . "</td><td>" . formatCurrency($test_order['total_amount']) . "</td></tr>";
    
    $calculated_total = $calculated_subtotal + $test_order['delivery_fee'];
    echo "<tr><td>Calculated Total</td><td>$calculated_total</td><td>" . formatCurrency($calculated_total) . "</td></tr>";
    echo "</table>";
    
    // Verification
    echo "<h5>Verification:</h5>";
    echo "<ul>";
    
    if ($calculated_subtotal == $test_order['subtotal']) {
        echo "<li style='color: green;'>✓ Subtotal calculation matches database</li>";
    } else {
        echo "<li style='color: red;'>✗ Subtotal calculation mismatch: Calculated=$calculated_subtotal, DB=" . $test_order['subtotal'] . "</li>";
    }
    
    if ($calculated_total == $test_order['total_amount']) {
        echo "<li style='color: green;'>✓ Total calculation matches database</li>";
    } else {
        echo "<li style='color: red;'>✗ Total calculation mismatch: Calculated=$calculated_total, DB=" . $test_order['total_amount'] . "</li>";
    }
    
    echo "</ul>";
}

// Test different display methods
echo "<h3>Test Metode Display yang Berbeda</h3>";

if (!empty($orders)) {
    $sample_order = $orders[0];
    $amount = $sample_order['total_amount'];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Method</th><th>Result</th><th>Notes</th></tr>";
    
    echo "<tr>";
    echo "<td>formatCurrency($amount)</td>";
    echo "<td>" . formatCurrency($amount) . "</td>";
    echo "<td>Default (multiply = true)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>formatCurrency($amount, true)</td>";
    echo "<td>" . formatCurrency($amount, true) . "</td>";
    echo "<td>Explicitly multiply by 15000</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>formatCurrency($amount, false)</td>";
    echo "<td>" . formatCurrency($amount, false) . "</td>";
    echo "<td>No multiplication</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>number_format($amount * 15000)</td>";
    echo "<td>Rp" . number_format($amount * 15000, 0, ',', '.') . "</td>";
    echo "<td>Manual calculation (old method)</td>";
    echo "</tr>";
    
    echo "</table>";
}

echo "<h3>Rekomendasi</h3>";
echo "<ul>";
echo "<li><strong>Gunakan formatCurrency() konsisten</strong> di semua tempat untuk menampilkan mata uang</li>";
echo "<li><strong>Parameter multiply=true (default)</strong> untuk konversi dari unit ke Rupiah</li>";
echo "<li><strong>Parameter multiply=false</strong> jika nilai sudah dalam Rupiah</li>";
echo "<li><strong>Hindari manual multiplication</strong> dengan 15000 di template</li>";
echo "</ul>";

echo "<h3>Status Perbaikan</h3>";
echo "<ul>";
echo "<li>✓ Fungsi formatCurrency() diperbaiki dengan parameter multiply</li>";
echo "<li>✓ Dashboard owner (owner/orders.php) menggunakan formatCurrency()</li>";
echo "<li>✓ Detail pesanan (owner/order_detail.php) sudah menggunakan formatCurrency()</li>";
echo "<li>✓ Konsistensi tampilan total amount terjaga</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='owner/orders.php'>Test Dashboard Owner</a> | <a href='owner/order_detail.php?id=" . ($orders[0]['order_id'] ?? 1) . "'>Test Detail Pesanan</a></p>";
?>
