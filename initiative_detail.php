<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if initiative ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: sustainability.php');
    exit;
}

$initiativeId = $_GET['id'];

// Connect to database
$conn = connectDB();

// Get initiative details
$stmt = $conn->prepare("
    SELECT * FROM sustainability_initiatives 
    WHERE initiative_id = :initiative_id AND is_active = 1
");
$stmt->bindParam(':initiative_id', $initiativeId);
$stmt->execute();
$initiative = $stmt->fetch(PDO::FETCH_ASSOC);

// If initiative not found, redirect to sustainability page
if (!$initiative) {
    header('Location: sustainability.php');
    exit;
}

// Get related restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address
    FROM restaurants r
    JOIN restaurant_initiatives ri ON r.restaurant_id = ri.restaurant_id
    WHERE ri.initiative_id = :initiative_id AND r.is_active = 1
    LIMIT 4
");
$stmt->bindParam(':initiative_id', $initiativeId);
$stmt->execute();
$relatedRestaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get initiative updates
$stmt = $conn->prepare("
    SELECT * FROM initiative_updates
    WHERE initiative_id = :initiative_id
    ORDER BY update_date DESC
");
$stmt->bindParam(':initiative_id', $initiativeId);
$stmt->execute();
$updates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get initiative impact
$stmt = $conn->prepare("
    SELECT * FROM initiative_impact
    WHERE initiative_id = :initiative_id
");
$stmt->bindParam(':initiative_id', $initiativeId);
$stmt->execute();
$impact = $stmt->fetch(PDO::FETCH_ASSOC);

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);
$userName = '';

if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $initiative['title'] ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
        .initiative-header {
            background-color: #f8f9fa;
            padding: 40px 0;
            margin-bottom: 40px;
        }
        
        .initiative-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .impact-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            background-color: #f8f9fa;
        }
        
        .impact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .impact-card .impact-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #28a745;
        }
        
        .impact-card .impact-number {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .impact-card .impact-text {
            color: #6c757d;
        }
        
        .update-card {
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
        }
        
        .update-date {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .eco-restaurant-card {
            height: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .eco-restaurant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .eco-restaurant-card .card-img-top {
            height: 160px;
            object-fit: cover;
        }
        
        .share-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .share-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 1.2rem;
            transition: transform 0.3s, opacity 0.3s;
        }
        
        .share-button:hover {
            transform: translateY(-3px);
            opacity: 0.9;
        }
        
        .share-facebook {
            background-color: #3b5998;
        }
        
        .share-twitter {
            background-color: #1da1f2;
        }
        
        .share-whatsapp {
            background-color: #25d366;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="fas fa-shopping-cart me-2"></i>Keranjang
                                <span class="badge bg-danger rounded-pill">
                                    <?php
                                        $cartCount = 0;
                                        if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                                            foreach ($_SESSION['cart'] as $items) {
                                                $cartCount += count($items);
                                            }
                                        }
                                        echo $cartCount;
                                    ?>
                                </span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="driver/orders.php">Pengiriman</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="owner/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-2"></i>Masuk
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-2"></i>Daftar
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Initiative Header -->
    <section class="initiative-header">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="sustainability.php">Keberlanjutan</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= $initiative['title'] ?></li>
                </ol>
            </nav>
            <h1 class="mt-4"><?= $initiative['title'] ?></h1>
            <p class="lead"><?= $initiative['subtitle'] ?? 'Inisiatif keberlanjutan untuk masa depan yang lebih baik' ?></p>
        </div>
    </section>

    <!-- Content -->
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8">
                <img src="<?= $initiative['image_url'] ?>" class="initiative-image" alt="<?= $initiative['title'] ?>">
                
                <div class="mb-5">
                    <?= $initiative['content'] ?>
                </div>
                
                <?php if (!empty($impact)): ?>
                <div class="mb-5">
                    <h3 class="mb-4">Dampak Inisiatif</h3>
                    <div class="row row-cols-1 row-cols-md-3 g-4">
                        <?php if (!empty($impact['metric1_value']) && !empty($impact['metric1_label'])): ?>
                        <div class="col">
                            <div class="impact-card">
                                <div class="impact-icon">
                                    <i class="fas fa-leaf"></i>
                                </div>
                                <div class="impact-number"><?= $impact['metric1_value'] ?></div>
                                <div class="impact-text"><?= $impact['metric1_label'] ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($impact['metric2_value']) && !empty($impact['metric2_label'])): ?>
                        <div class="col">
                            <div class="impact-card">
                                <div class="impact-icon">
                                    <i class="fas fa-recycle"></i>
                                </div>
                                <div class="impact-number"><?= $impact['metric2_value'] ?></div>
                                <div class="impact-text"><?= $impact['metric2_label'] ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($impact['metric3_value']) && !empty($impact['metric3_label'])): ?>
                        <div class="col">
                            <div class="impact-card">
                                <div class="impact-icon">
                                    <i class="fas fa-hand-holding-heart"></i>
                                </div>
                                <div class="impact-number"><?= $impact['metric3_value'] ?></div>
                                <div class="impact-text"><?= $impact['metric3_label'] ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($updates)): ?>
                <div class="mb-5">
                    <h3 class="mb-4">Perkembangan Terbaru</h3>
                    <?php foreach ($updates as $update): ?>
                        <div class="update-card">
                            <div class="update-date">
                                <i class="fas fa-calendar-alt me-2"></i><?= date('d F Y', strtotime($update['update_date'])) ?>
                            </div>
                            <h5><?= $update['title'] ?></h5>
                            <p class="mb-0"><?= $update['content'] ?></p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <div class="mb-5">
                    <h3 class="mb-4">Bagikan Inisiatif Ini</h3>
                    <p>Bantu kami menyebarkan kesadaran tentang inisiatif keberlanjutan ini.</p>
                    
                    <div class="share-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>" 
                           target="_blank" class="share-button share-facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>&text=<?= urlencode($initiative['title']) ?>" 
                           target="_blank" class="share-button share-twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://api.whatsapp.com/send?text=<?= urlencode($initiative['title'] . ' - https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>" 
                           target="_blank" class="share-button share-whatsapp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Cara Anda Dapat Membantu</h5>
                    </div>
                    <div class="card-body">
                        <p>Bergabunglah dengan kami dalam inisiatif ini dengan cara:</p>
                        <ul>
                            <li>Pilih opsi kemasan ramah lingkungan saat memesan</li>
                            <li>Pilih untuk tidak menerima alat makan sekali pakai</li>
                            <li>Dukung restoran yang berpartisipasi dalam inisiatif ini</li>
                            <li>Bagikan informasi ini kepada teman dan keluarga</li>
                        </ul>
                        
                        <?php if (isset($initiative['action_url']) && !empty($initiative['action_url'])): ?>
                            <a href="<?= $initiative['action_url'] ?>" class="btn btn-success w-100">
                                <i class="fas fa-hands-helping me-2"></i><?= $initiative['action_text'] ?? 'Ambil Tindakan Sekarang' ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if (!empty($relatedRestaurants)): ?>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Restoran yang Berpartisipasi</h5>
                    </div>
                    <div class="card-body">
                        <div class="row row-cols-1 row-cols-md-2 g-3">
                            <?php foreach ($relatedRestaurants as $restaurant): ?>
                                <div class="col">
                                    <div class="card eco-restaurant-card h-100">
                                        <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" 
                                             class="card-img-top" alt="<?= $restaurant['name'] ?>">
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1"><?= $restaurant['name'] ?></h6>
                                            <p class="card-text small mb-1">
                                                <i class="fas fa-utensils me-1"></i><?= $restaurant['cuisine_type'] ?>
                                            </p>
                                            <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-sm btn-outline-success w-100 mt-2">
                                                Lihat Menu
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <a href="eco_restaurants.php?initiative=<?= $initiativeId ?>" class="btn btn-outline-success w-100 mt-3">
                            <i class="fas fa-list me-2"></i>Lihat Semua Restoran
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat dan ramah lingkungan.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="sustainability.php" class="text-white">Keberlanjutan</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
                <p class="small">
                    <i class="fas fa-leaf me-2"></i>Dicetak di atas kertas digital untuk menyelamatkan pohon.
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
