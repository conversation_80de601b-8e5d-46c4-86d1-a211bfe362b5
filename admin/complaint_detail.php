<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get admin information
$admin_id = $_SESSION['admin_id'];
$stmt = $conn->prepare("SELECT * FROM admins WHERE admin_id = :admin_id");
$stmt->bindParam(':admin_id', $admin_id);
$stmt->execute();
$admin = $stmt->fetch();

// Check if complaint ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: complaints.php');
    exit;
}

$complaint_id = $_GET['id'];

// Get complaint details
$stmt = $conn->prepare("
    SELECT c.*, 
           u.name AS user_name,
           u.email AS user_email,
           u.phone AS user_phone,
           o.order_id,
           o.total_amount,
           r.name AS restaurant_name,
           a.name AS admin_name
    FROM complaints c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN orders o ON c.order_id = o.order_id
    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
    WHERE c.complaint_id = :complaint_id
");
$stmt->bindParam(':complaint_id', $complaint_id);
$stmt->execute();
$complaint = $stmt->fetch();

if (!$complaint) {
    header('Location: complaints.php');
    exit;
}

// Get complaint updates
$stmt = $conn->prepare("
    SELECT cu.*, a.name AS admin_name
    FROM complaint_updates cu
    JOIN admins a ON cu.admin_id = a.admin_id
    WHERE cu.complaint_id = :complaint_id
    ORDER BY cu.created_at DESC
");
$stmt->bindParam(':complaint_id', $complaint_id);
$stmt->execute();
$updates = $stmt->fetchAll();

// Get complaint attachments
$stmt = $conn->prepare("
    SELECT *
    FROM complaint_attachments
    WHERE complaint_id = :complaint_id
    ORDER BY uploaded_at DESC
");
$stmt->bindParam(':complaint_id', $complaint_id);
$stmt->execute();
$attachments = $stmt->fetchAll();

// Get chat room ID for this complaint
$stmt = $conn->prepare("
    SELECT room_id
    FROM chat_rooms
    WHERE room_type = 'complaint' AND order_id = :complaint_id
");
$stmt->bindParam(':complaint_id', $complaint_id);
$stmt->execute();
$chat_room = $stmt->fetch();
$chat_room_id = $chat_room ? $chat_room['room_id'] : 0;

// Get all admins for assignment
$stmt = $conn->prepare("SELECT admin_id, name FROM admins ORDER BY name");
$stmt->execute();
$all_admins = $stmt->fetchAll();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update complaint status
    if (isset($_POST['update_status'])) {
        $new_status = $_POST['status'];
        $new_priority = $_POST['priority'];
        $assigned_admin_id = $_POST['assigned_admin_id'] ?: null;
        $update_notes = $_POST['update_notes'];
        
        try {
            $conn->beginTransaction();
            
            // Update complaint
            $stmt = $conn->prepare("
                UPDATE complaints
                SET status = :status,
                    priority = :priority,
                    assigned_admin_id = :assigned_admin_id,
                    updated_at = NOW()
                WHERE complaint_id = :complaint_id
            ");
            $stmt->bindParam(':status', $new_status);
            $stmt->bindParam(':priority', $new_priority);
            $stmt->bindParam(':assigned_admin_id', $assigned_admin_id);
            $stmt->bindParam(':complaint_id', $complaint_id);
            $stmt->execute();
            
            // Add update record
            if (!empty($update_notes)) {
                $stmt = $conn->prepare("
                    INSERT INTO complaint_updates (complaint_id, admin_id, update_text, created_at)
                    VALUES (:complaint_id, :admin_id, :update_text, NOW())
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->bindParam(':admin_id', $admin_id);
                $stmt->bindParam(':update_text', $update_notes);
                $stmt->execute();
            }
            
            // If status changed to resolved, set resolved_at timestamp
            if ($new_status === 'resolved' && $complaint['status'] !== 'resolved') {
                $stmt = $conn->prepare("
                    UPDATE complaints
                    SET resolved_at = NOW()
                    WHERE complaint_id = :complaint_id
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                
                // Add system message to chat if chat room exists
                if ($chat_room_id > 0) {
                    $system_message = "Keluhan telah ditandai sebagai terselesaikan oleh admin.";
                    $stmt = $conn->prepare("
                        INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type, created_at)
                        VALUES (:room_id, 'system', 0, :message_text, 'system', NOW())
                    ");
                    $stmt->bindParam(':room_id', $chat_room_id);
                    $stmt->bindParam(':message_text', $system_message);
                    $stmt->execute();
                    
                    // Update chat room's updated_at timestamp
                    $stmt = $conn->prepare("
                        UPDATE chat_rooms
                        SET updated_at = NOW()
                        WHERE room_id = :room_id
                    ");
                    $stmt->bindParam(':room_id', $chat_room_id);
                    $stmt->execute();
                }
            }
            
            // If status changed to closed, add system message to chat if chat room exists
            if ($new_status === 'closed' && $complaint['status'] !== 'closed') {
                if ($chat_room_id > 0) {
                    // Update chat room status to closed
                    $stmt = $conn->prepare("
                        UPDATE chat_rooms
                        SET status = 'closed', updated_at = NOW()
                        WHERE room_id = :room_id
                    ");
                    $stmt->bindParam(':room_id', $chat_room_id);
                    $stmt->execute();
                    
                    // Add system message
                    $system_message = "Keluhan telah ditutup oleh admin.";
                    $stmt = $conn->prepare("
                        INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type, created_at)
                        VALUES (:room_id, 'system', 0, :message_text, 'system', NOW())
                    ");
                    $stmt->bindParam(':room_id', $chat_room_id);
                    $stmt->bindParam(':message_text', $system_message);
                    $stmt->execute();
                }
            }
            
            $conn->commit();
            $success_message = 'Keluhan berhasil diperbarui.';
            
            // Refresh complaint data
            $stmt = $conn->prepare("
                SELECT c.*, 
                       u.name AS user_name,
                       u.email AS user_email,
                       u.phone AS user_phone,
                       o.order_id,
                       o.total_amount,
                       r.name AS restaurant_name,
                       a.name AS admin_name
                FROM complaints c
                LEFT JOIN users u ON c.user_id = u.user_id
                LEFT JOIN orders o ON c.order_id = o.order_id
                LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                WHERE c.complaint_id = :complaint_id
            ");
            $stmt->bindParam(':complaint_id', $complaint_id);
            $stmt->execute();
            $complaint = $stmt->fetch();
            
            // Refresh updates
            $stmt = $conn->prepare("
                SELECT cu.*, a.name AS admin_name
                FROM complaint_updates cu
                JOIN admins a ON cu.admin_id = a.admin_id
                WHERE cu.complaint_id = :complaint_id
                ORDER BY cu.created_at DESC
            ");
            $stmt->bindParam(':complaint_id', $complaint_id);
            $stmt->execute();
            $updates = $stmt->fetchAll();
        } catch (PDOException $e) {
            $conn->rollBack();
            $error_message = 'Terjadi kesalahan: ' . $e->getMessage();
        }
    }
    
    // Add resolution notes
    if (isset($_POST['add_resolution'])) {
        $resolution_notes = $_POST['resolution_notes'];
        
        try {
            $stmt = $conn->prepare("
                UPDATE complaints
                SET resolution_notes = :resolution_notes,
                    updated_at = NOW()
                WHERE complaint_id = :complaint_id
            ");
            $stmt->bindParam(':resolution_notes', $resolution_notes);
            $stmt->bindParam(':complaint_id', $complaint_id);
            $stmt->execute();
            
            $success_message = 'Catatan resolusi berhasil ditambahkan.';
            
            // Refresh complaint data
            $stmt = $conn->prepare("
                SELECT c.*, 
                       u.name AS user_name,
                       u.email AS user_email,
                       u.phone AS user_phone,
                       o.order_id,
                       o.total_amount,
                       r.name AS restaurant_name,
                       a.name AS admin_name
                FROM complaints c
                LEFT JOIN users u ON c.user_id = u.user_id
                LEFT JOIN orders o ON c.order_id = o.order_id
                LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                WHERE c.complaint_id = :complaint_id
            ");
            $stmt->bindParam(':complaint_id', $complaint_id);
            $stmt->execute();
            $complaint = $stmt->fetch();
        } catch (PDOException $e) {
            $error_message = 'Terjadi kesalahan: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Keluhan #<?= $complaint_id ?> - KikaZen Ship Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">Pengguna</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="drivers.php">Pengemudi</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">Pesanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="complaints.php">Keluhan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Laporan</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $admin['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="settings.php">Pengaturan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="h3 mb-0">Detail Keluhan #<?= $complaint_id ?></h1>
                <p class="text-muted">Kelola dan tanggapi keluhan pelanggan</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="complaints.php" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Kembali
                </a>
                <?php if ($chat_room_id > 0): ?>
                    <a href="complaint_chat.php?id=<?= $complaint_id ?>" class="btn btn-primary">
                        <i class="fas fa-comments me-2"></i>Chat Keluhan
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Complaint Details -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Informasi Keluhan</h5>
                        <div>
                            <?php
                            $status_badge = '';
                            switch ($complaint['status']) {
                                case 'pending':
                                    $status_badge = '<span class="badge bg-warning">Tertunda</span>';
                                    break;
                                case 'in_progress':
                                    $status_badge = '<span class="badge bg-primary">Dalam Proses</span>';
                                    break;
                                case 'resolved':
                                    $status_badge = '<span class="badge bg-success">Terselesaikan</span>';
                                    break;
                                case 'closed':
                                    $status_badge = '<span class="badge bg-secondary">Ditutup</span>';
                                    break;
                            }
                            
                            $priority_badge = '';
                            switch ($complaint['priority']) {
                                case 'urgent':
                                    $priority_badge = '<span class="badge bg-danger">Mendesak</span>';
                                    break;
                                case 'high':
                                    $priority_badge = '<span class="badge bg-warning text-dark">Tinggi</span>';
                                    break;
                                case 'medium':
                                    $priority_badge = '<span class="badge bg-info text-dark">Sedang</span>';
                                    break;
                                case 'low':
                                    $priority_badge = '<span class="badge bg-success">Rendah</span>';
                                    break;
                            }
                            
                            echo $status_badge . ' ' . $priority_badge;
                            ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($complaint['subject']) ?></h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Tipe Keluhan:</strong> 
                                    <?php
                                    $complaint_type = '';
                                    switch ($complaint['complaint_type']) {
                                        case 'order':
                                            $complaint_type = 'Pesanan';
                                            break;
                                        case 'delivery':
                                            $complaint_type = 'Pengiriman';
                                            break;
                                        case 'restaurant':
                                            $complaint_type = 'Restoran';
                                            break;
                                        case 'app':
                                            $complaint_type = 'Aplikasi';
                                            break;
                                        case 'other':
                                            $complaint_type = 'Lainnya';
                                            break;
                                    }
                                    echo $complaint_type;
                                    ?>
                                </p>
                                <p><strong>Tanggal Dibuat:</strong> <?= date('d/m/Y H:i', strtotime($complaint['created_at'])) ?></p>
                                <p><strong>Terakhir Diperbarui:</strong> <?= date('d/m/Y H:i', strtotime($complaint['updated_at'])) ?></p>
                                <?php if ($complaint['resolved_at']): ?>
                                    <p><strong>Tanggal Diselesaikan:</strong> <?= date('d/m/Y H:i', strtotime($complaint['resolved_at'])) ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Pelanggan:</strong> <?= htmlspecialchars($complaint['user_name']) ?></p>
                                <p><strong>Email:</strong> <?= htmlspecialchars($complaint['user_email']) ?></p>
                                <p><strong>Telepon:</strong> <?= htmlspecialchars($complaint['user_phone']) ?></p>
                                <?php if ($complaint['order_id']): ?>
                                    <p><strong>Pesanan Terkait:</strong> <a href="order_detail.php?id=<?= $complaint['order_id'] ?>">#<?= $complaint['order_id'] ?></a></p>
                                    <?php if ($complaint['restaurant_name']): ?>
                                        <p><strong>Restoran:</strong> <?= htmlspecialchars($complaint['restaurant_name']) ?></p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6>Deskripsi Keluhan:</h6>
                            <div class="p-3 bg-light rounded">
                                <?= nl2br(htmlspecialchars($complaint['description'])) ?>
                            </div>
                        </div>
                        
                        <?php if ($attachments): ?>
                            <div class="mb-4">
                                <h6>Lampiran:</h6>
                                <div class="row">
                                    <?php foreach ($attachments as $attachment): ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <?php if (in_array(strtolower(pathinfo($attachment['file_name'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                    <img src="<?= $attachment['file_path'] ?>" class="card-img-top" alt="<?= $attachment['file_name'] ?>">
                                                <?php else: ?>
                                                    <div class="card-body text-center">
                                                        <i class="fas fa-file fa-3x text-primary"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="card-footer">
                                                    <a href="<?= $attachment['file_path'] ?>" target="_blank" class="btn btn-sm btn-primary w-100">
                                                        <i class="fas fa-download me-2"></i>Unduh
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($complaint['resolution_notes']): ?>
                            <div class="mb-4">
                                <h6>Catatan Resolusi:</h6>
                                <div class="p-3 bg-light rounded">
                                    <?= nl2br(htmlspecialchars($complaint['resolution_notes'])) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Complaint Updates -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Riwayat Pembaruan</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($updates) === 0): ?>
                            <p class="text-muted">Belum ada pembaruan untuk keluhan ini.</p>
                        <?php else: ?>
                            <div class="timeline">
                                <?php foreach ($updates as $update): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-item-marker">
                                            <div class="timeline-item-marker-text"><?= date('d/m/Y', strtotime($update['created_at'])) ?></div>
                                            <div class="timeline-item-marker-indicator bg-primary"></div>
                                        </div>
                                        <div class="timeline-item-content">
                                            <p class="fw-bold mb-2">
                                                <?= htmlspecialchars($update['admin_name']) ?>
                                                <span class="text-muted small">
                                                    <?= date('H:i', strtotime($update['created_at'])) ?>
                                                </span>
                                            </p>
                                            <p><?= nl2br(htmlspecialchars($update['update_text'])) ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Update Status Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Perbarui Status</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="pending" <?= $complaint['status'] === 'pending' ? 'selected' : '' ?>>Tertunda</option>
                                    <option value="in_progress" <?= $complaint['status'] === 'in_progress' ? 'selected' : '' ?>>Dalam Proses</option>
                                    <option value="resolved" <?= $complaint['status'] === 'resolved' ? 'selected' : '' ?>>Terselesaikan</option>
                                    <option value="closed" <?= $complaint['status'] === 'closed' ? 'selected' : '' ?>>Ditutup</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="priority" class="form-label">Prioritas</label>
                                <select class="form-select" id="priority" name="priority" required>
                                    <option value="low" <?= $complaint['priority'] === 'low' ? 'selected' : '' ?>>Rendah</option>
                                    <option value="medium" <?= $complaint['priority'] === 'medium' ? 'selected' : '' ?>>Sedang</option>
                                    <option value="high" <?= $complaint['priority'] === 'high' ? 'selected' : '' ?>>Tinggi</option>
                                    <option value="urgent" <?= $complaint['priority'] === 'urgent' ? 'selected' : '' ?>>Mendesak</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="assigned_admin_id" class="form-label">Ditugaskan Kepada</label>
                                <select class="form-select" id="assigned_admin_id" name="assigned_admin_id">
                                    <option value="">-- Pilih Admin --</option>
                                    <?php foreach ($all_admins as $a): ?>
                                        <option value="<?= $a['admin_id'] ?>" <?= $complaint['assigned_admin_id'] == $a['admin_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($a['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="update_notes" class="form-label">Catatan Pembaruan</label>
                                <textarea class="form-control" id="update_notes" name="update_notes" rows="3" placeholder="Tambahkan catatan tentang pembaruan ini..."></textarea>
                            </div>
                            <button type="submit" name="update_status" class="btn btn-primary">Perbarui Status</button>
                        </form>
                    </div>
                </div>
                
                <!-- Resolution Notes Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Catatan Resolusi</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label for="resolution_notes" class="form-label">Catatan Resolusi</label>
                                <textarea class="form-control" id="resolution_notes" name="resolution_notes" rows="5" placeholder="Tambahkan catatan tentang bagaimana keluhan ini diselesaikan..."><?= htmlspecialchars($complaint['resolution_notes'] ?? '') ?></textarea>
                            </div>
                            <button type="submit" name="add_resolution" class="btn btn-success">Simpan Catatan Resolusi</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-4 bg-dark mt-auto">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between small">
                <div class="text-muted">Hak Cipta &copy; KikaZen Ship <?= date('Y') ?></div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/admin.js"></script>
</body>
</html>
