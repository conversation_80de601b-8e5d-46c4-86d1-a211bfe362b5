<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Search
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = "WHERE o.order_id LIKE ? OR r.name LIKE ? OR u.name LIKE ? OR u.phone LIKE ?";
    $searchParam = "%$search%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
}

// Filter by status
$status = isset($_GET['status']) ? $_GET['status'] : '';
if (!empty($status)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.order_status = ?";
    } else {
        $searchCondition .= " AND o.order_status = ?";
    }
    $params[] = $status;
}

// Filter by user
$userId = isset($_GET['user_id']) ? $_GET['user_id'] : '';
if (!empty($userId)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.user_id = ?";
    } else {
        $searchCondition .= " AND o.user_id = ?";
    }
    $params[] = $userId;
}

// Filter by driver
$driverId = isset($_GET['driver_id']) ? $_GET['driver_id'] : '';
if (!empty($driverId)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.driver_id = ?";
    } else {
        $searchCondition .= " AND o.driver_id = ?";
    }
    $params[] = $driverId;
}

// Filter by restaurant
$restaurantId = isset($_GET['restaurant_id']) ? $_GET['restaurant_id'] : '';
if (!empty($restaurantId)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.restaurant_id = ?";
    } else {
        $searchCondition .= " AND o.restaurant_id = ?";
    }
    $params[] = $restaurantId;
}

// Filter by date range
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';

if (!empty($startDate)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.created_at >= ?";
    } else {
        $searchCondition .= " AND o.created_at >= ?";
    }
    $params[] = $startDate . ' 00:00:00';
}

if (!empty($endDate)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE o.created_at <= ?";
    } else {
        $searchCondition .= " AND o.created_at <= ?";
    }
    $params[] = $endDate . ' 23:59:59';
}

// Get total orders count
$stmt = $conn->prepare("
    SELECT COUNT(*) as total
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    $searchCondition
");
if (!empty($params)) {
    $stmt->execute($params);
} else {
    $stmt->execute();
}
$totalOrders = $stmt->fetch()['total'];
$totalPages = ceil($totalOrders / $limit);

// Get orders with pagination
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, u.name as user_name, u.phone as user_phone,
           d.name as driver_name, d.phone as driver_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    $searchCondition
    ORDER BY o.created_at DESC
    LIMIT :limit OFFSET :offset
");
if (!empty($params)) {
    foreach ($params as $key => $param) {
        $stmt->bindValue($key + 1, $param);
    }
}
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$orders = $stmt->fetchAll();

// Get order statuses for filter
$orderStatuses = [
    'pending' => 'Menunggu',
    'confirmed' => 'Dikonfirmasi',
    'preparing' => 'Sedang Dipersiapkan',
    'ready_for_pickup' => 'Siap Diambil',
    'picked_up' => 'Diambil',
    'on_the_way' => 'Dalam Perjalanan',
    'delivered' => 'Terkirim',
    'cancelled' => 'Dibatalkan'
];

// Handle order status update
if (isset($_POST['action']) && isset($_POST['order_id'])) {
    $orderId = $_POST['order_id'];
    $action = $_POST['action'];

    if ($action === 'cancel') {
        $stmt = $conn->prepare("UPDATE orders SET order_status = 'cancelled' WHERE order_id = ?");
        $stmt->execute([$orderId]);
        header('Location: orders.php?page=' . $page . '&success=Order cancelled successfully');
        exit;
    }
}

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen Pesanan - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Orders Management Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="mb-0">Manajemen Pesanan</h1>
                <p class="text-muted">Kelola semua pesanan aplikasi</p>
            </div>
            <div class="col-md-6">
                <form action="orders.php" method="get" class="row g-2">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Cari pesanan..." value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">Semua Status</option>
                            <?php foreach ($orderStatuses as $key => $value): ?>
                                <option value="<?= $key ?>" <?= $status === $key ? 'selected' : '' ?>><?= $value ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="start_date" class="form-control" placeholder="Dari" value="<?= htmlspecialchars($startDate) ?>">
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="end_date" class="form-control" placeholder="Sampai" value="<?= htmlspecialchars($endDate) ?>">
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Daftar Pesanan</h5>
            </div>
            <div class="card-body">
                <?php if (empty($orders)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h4>Tidak ada pesanan ditemukan</h4>
                        <p class="text-muted">Tidak ada pesanan yang sesuai dengan kriteria pencarian Anda.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Pelanggan</th>
                                    <th>Restoran</th>
                                    <th>Pengemudi</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>#<?= $order['order_id'] ?></td>
                                        <td>
                                            <?= $order['user_name'] ?>
                                            <small class="d-block text-muted"><?= $order['user_phone'] ?></small>
                                        </td>
                                        <td><?= $order['restaurant_name'] ?></td>
                                        <td>
                                            <?php if ($order['driver_name']): ?>
                                                <?= $order['driver_name'] ?>
                                                <small class="d-block text-muted"><?= $order['driver_phone'] ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">Belum ditugaskan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                        <td>
                                            <span class="badge <?php
                                                switch ($order['order_status']) {
                                                    case 'pending': echo 'bg-warning text-dark'; break;
                                                    case 'confirmed': echo 'bg-info text-dark'; break;
                                                    case 'preparing': echo 'bg-primary'; break;
                                                    case 'ready_for_pickup': echo 'bg-primary'; break;
                                                    case 'picked_up': echo 'bg-info'; break;
                                                    case 'on_the_way': echo 'bg-info'; break;
                                                    case 'delivered': echo 'bg-success'; break;
                                                    case 'cancelled': echo 'bg-danger'; break;
                                                    default: echo 'bg-secondary';
                                                }
                                            ?>">
                                                <?php
                                                    switch ($order['order_status']) {
                                                        case 'pending': echo 'Menunggu'; break;
                                                        case 'confirmed': echo 'Dikonfirmasi'; break;
                                                        case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                                        case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                                        case 'picked_up': echo 'Diambil'; break;
                                                        case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                                        case 'delivered': echo 'Terkirim'; break;
                                                        case 'cancelled': echo 'Dibatalkan'; break;
                                                        default: echo $order['order_status'];
                                                    }
                                                ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Aksi
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="view_order.php?id=<?= $order['order_id'] ?>">
                                                            <i class="fas fa-eye me-2"></i> Lihat Detail
                                                        </a>
                                                    </li>
                                                    <?php if ($order['order_status'] !== 'delivered' && $order['order_status'] !== 'cancelled'): ?>
                                                        <li>
                                                            <form action="orders.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin membatalkan pesanan ini?')">
                                                                <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                                                                <input type="hidden" name="action" value="cancel">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-times-circle me-2"></i> Batalkan Pesanan
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php
                                // Build query string for pagination
                                $queryParams = [];
                                if (!empty($search)) $queryParams['search'] = urlencode($search);
                                if (!empty($status)) $queryParams['status'] = urlencode($status);
                                if (!empty($startDate)) $queryParams['start_date'] = urlencode($startDate);
                                if (!empty($endDate)) $queryParams['end_date'] = urlencode($endDate);
                                if (!empty($userId)) $queryParams['user_id'] = urlencode($userId);
                                if (!empty($driverId)) $queryParams['driver_id'] = urlencode($driverId);
                                if (!empty($restaurantId)) $queryParams['restaurant_id'] = urlencode($restaurantId);

                                $queryString = '';
                                foreach ($queryParams as $key => $value) {
                                    $queryString .= "&{$key}={$value}";
                                }
                                ?>
                                <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page - 1 ?><?= $queryString ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?><?= $queryString ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page + 1 ?><?= $queryString ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
