<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Search
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = "WHERE r.name LIKE ? OR r.address LIKE ? OR r.description LIKE ?";
    $searchParam = "%$search%";
    $params = [$searchParam, $searchParam, $searchParam];
}

// Filter by category
$category = isset($_GET['category']) ? $_GET['category'] : '';
if (!empty($category)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE r.category_id = ?";
    } else {
        $searchCondition .= " AND r.category_id = ?";
    }
    $params[] = $category;
}

// Get total restaurants count
$stmt = $conn->prepare("
    SELECT COUNT(*) as total 
    FROM restaurants r
    $searchCondition
");
if (!empty($params)) {
    $stmt->execute($params);
} else {
    $stmt->execute();
}
$totalRestaurants = $stmt->fetch()['total'];
$totalPages = ceil($totalRestaurants / $limit);

// Get restaurants with pagination
$stmt = $conn->prepare("
    SELECT r.*, ro.name as owner_name, ro.email as owner_email, 
           (SELECT COUNT(*) FROM menu_items WHERE restaurant_id = r.restaurant_id) as menu_count
    FROM restaurants r
    LEFT JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
    $searchCondition
    ORDER BY r.created_at DESC
    LIMIT :limit OFFSET :offset
");
if (!empty($params)) {
    foreach ($params as $key => $param) {
        $stmt->bindValue($key + 1, $param);
    }
}
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$restaurants = $stmt->fetchAll();

// Get categories for filter
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();

// Handle restaurant status update
if (isset($_POST['action']) && isset($_POST['restaurant_id'])) {
    $restaurantId = $_POST['restaurant_id'];
    $action = $_POST['action'];
    
    if ($action === 'activate') {
        $stmt = $conn->prepare("UPDATE restaurants SET is_active = 1 WHERE restaurant_id = ?");
        $stmt->execute([$restaurantId]);
        header('Location: restaurants.php?page=' . $page . '&success=Restaurant activated successfully');
        exit;
    } elseif ($action === 'deactivate') {
        $stmt = $conn->prepare("UPDATE restaurants SET is_active = 0 WHERE restaurant_id = ?");
        $stmt->execute([$restaurantId]);
        header('Location: restaurants.php?page=' . $page . '&success=Restaurant deactivated successfully');
        exit;
    } elseif ($action === 'delete') {
        // Check if restaurant has orders
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE restaurant_id = ?");
        $stmt->execute([$restaurantId]);
        $orderCount = $stmt->fetch()['count'];
        
        if ($orderCount > 0) {
            header('Location: restaurants.php?page=' . $page . '&error=Cannot delete restaurant with orders');
            exit;
        }
        
        // Delete menu items first
        $stmt = $conn->prepare("DELETE FROM menu_items WHERE restaurant_id = ?");
        $stmt->execute([$restaurantId]);
        
        // Then delete restaurant
        $stmt = $conn->prepare("DELETE FROM restaurants WHERE restaurant_id = ?");
        $stmt->execute([$restaurantId]);
        
        header('Location: restaurants.php?page=' . $page . '&success=Restaurant deleted successfully');
        exit;
    }
}

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Restaurants Management Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="mb-0">Manajemen Restoran</h1>
                <p class="text-muted">Kelola semua restoran aplikasi</p>
            </div>
            <div class="col-md-6">
                <form action="restaurants.php" method="get" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" placeholder="Cari restoran..." value="<?= htmlspecialchars($search) ?>">
                    <select name="category" class="form-select me-2" style="max-width: 150px;">
                        <option value="">Semua Kategori</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= $cat['category_id'] ?>" <?= $category == $cat['category_id'] ? 'selected' : '' ?>><?= $cat['name'] ?></option>
                        <?php endforeach; ?>
                    </select>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Daftar Restoran</h5>
            </div>
            <div class="card-body">
                <?php if (empty($restaurants)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-store fa-3x text-muted mb-3"></i>
                        <h4>Tidak ada restoran ditemukan</h4>
                        <p class="text-muted">Tidak ada restoran yang sesuai dengan kriteria pencarian Anda.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama</th>
                                    <th>Pemilik</th>
                                    <th>Menu</th>
                                    <th>Rating</th>
                                    <th>Status</th>
                                    <th>Tanggal Daftar</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($restaurants as $restaurant): ?>
                                    <tr>
                                        <td><?= $restaurant['restaurant_id'] ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px; font-weight: bold;">
                                                    <?= substr($restaurant['name'], 0, 1) ?>
                                                </div>
                                                <div>
                                                    <?= $restaurant['name'] ?>
                                                    <small class="d-block text-muted"><?= substr($restaurant['address'], 0, 30) ?>...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($restaurant['owner_name']): ?>
                                                <?= $restaurant['owner_name'] ?>
                                                <small class="d-block text-muted"><?= $restaurant['owner_email'] ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">Tidak ada pemilik</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= $restaurant['menu_count'] ?> item</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-star text-warning me-1"></i>
                                                <?= number_format($restaurant['rating'], 1) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge <?= $restaurant['is_active'] ? 'bg-success' : 'bg-danger' ?>">
                                                <?= $restaurant['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y H:i', strtotime($restaurant['created_at'])) ?></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Aksi
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="view_restaurant.php?id=<?= $restaurant['restaurant_id'] ?>">
                                                            <i class="fas fa-eye me-2"></i> Lihat Detail
                                                        </a>
                                                    </li>
                                                    <?php if ($restaurant['is_active']): ?>
                                                        <li>
                                                            <form action="restaurants.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menonaktifkan restoran ini?')">
                                                                <input type="hidden" name="restaurant_id" value="<?= $restaurant['restaurant_id'] ?>">
                                                                <input type="hidden" name="action" value="deactivate">
                                                                <button type="submit" class="dropdown-item text-warning">
                                                                    <i class="fas fa-ban me-2"></i> Nonaktifkan
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php else: ?>
                                                        <li>
                                                            <form action="restaurants.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin mengaktifkan restoran ini?')">
                                                                <input type="hidden" name="restaurant_id" value="<?= $restaurant['restaurant_id'] ?>">
                                                                <input type="hidden" name="action" value="activate">
                                                                <button type="submit" class="dropdown-item text-success">
                                                                    <i class="fas fa-check-circle me-2"></i> Aktifkan
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item" href="restaurant_menu.php?id=<?= $restaurant['restaurant_id'] ?>">
                                                            <i class="fas fa-utensils me-2"></i> Kelola Menu
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form action="restaurants.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menghapus restoran ini? Tindakan ini tidak dapat dibatalkan.')">
                                                            <input type="hidden" name="restaurant_id" value="<?= $restaurant['restaurant_id'] ?>">
                                                            <input type="hidden" name="action" value="delete">
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i class="fas fa-trash-alt me-2"></i> Hapus
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
