<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Check if driver ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: drivers.php');
    exit;
}

$driver_id = $_GET['id'];

// Get driver details
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = ?");
$stmt->execute([$driver_id]);

if ($stmt->rowCount() === 0) {
    header('Location: drivers.php');
    exit;
}

$driver = $stmt->fetch();

// Get driver's orders
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, u.name as user_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.driver_id = ?
    ORDER BY o.created_at DESC
    LIMIT 10
");
$stmt->execute([$driver_id]);
$orders = $stmt->fetchAll();

// Get driver's order count
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE driver_id = ?");
$stmt->execute([$driver_id]);
$orderCount = $stmt->fetch()['count'];

// Get driver's completed order count
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE driver_id = ? AND order_status = 'delivered'");
$stmt->execute([$driver_id]);
$completedOrderCount = $stmt->fetch()['count'];

// Get driver's earnings
$stmt = $conn->prepare("SELECT SUM(delivery_fee) as total FROM orders WHERE driver_id = ? AND order_status = 'delivered'");
$stmt->execute([$driver_id]);
$totalEarnings = $stmt->fetch()['total'] ?? 0;

// Handle driver status update
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_status') {
        $newStatus = $_POST['status'] ?? '';
        
        if (empty($newStatus)) {
            $error = 'Silakan pilih status baru';
        } else {
            $stmt = $conn->prepare("UPDATE drivers SET status = ? WHERE driver_id = ?");
            
            if ($stmt->execute([$newStatus, $driver_id])) {
                $success = 'Status pengemudi berhasil diperbarui';
                
                // Refresh driver data
                $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = ?");
                $stmt->execute([$driver_id]);
                $driver = $stmt->fetch();
            } else {
                $error = 'Gagal memperbarui status pengemudi';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Driver Details Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Detail Pengemudi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="drivers.php">Pengemudi</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Detail Pengemudi</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="drivers.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar Pengemudi
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <div class="rounded-circle bg-warning text-dark d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                <?= substr($driver['name'], 0, 1) ?>
                            </div>
                        </div>
                        <h4><?= $driver['name'] ?></h4>
                        <p class="text-muted"><?= $driver['email'] ?></p>
                        <p>
                            <span class="badge <?php
                                switch ($driver['status']) {
                                    case 'pending': echo 'bg-warning text-dark'; break;
                                    case 'active': echo 'bg-success'; break;
                                    case 'suspended': echo 'bg-danger'; break;
                                    default: echo 'bg-secondary';
                                }
                            ?>">
                                <?php
                                    switch ($driver['status']) {
                                        case 'pending': echo 'Menunggu'; break;
                                        case 'active': echo 'Aktif'; break;
                                        case 'suspended': echo 'Ditangguhkan'; break;
                                        default: echo $driver['status'];
                                    }
                                ?>
                            </span>
                        </p>
                        <p class="text-muted small">
                            <i class="fas fa-clock me-1"></i> Bergabung pada <?= date('d F Y', strtotime($driver['created_at'])) ?>
                        </p>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Kontak</h5>
                    </div>
                    <div class="card-body p-4">
                        <p>
                            <i class="fas fa-envelope me-2 text-muted"></i> <?= $driver['email'] ?>
                        </p>
                        <p>
                            <i class="fas fa-phone me-2 text-muted"></i> <?= $driver['phone'] ?>
                        </p>
                        <?php if (!empty($driver['address'])): ?>
                            <p>
                                <i class="fas fa-map-marker-alt me-2 text-muted"></i> <?= $driver['address'] ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Kendaraan</h5>
                    </div>
                    <div class="card-body p-4">
                        <p>
                            <i class="fas <?php
                                switch ($driver['vehicle_type']) {
                                    case 'motorcycle': echo 'fa-motorcycle'; break;
                                    case 'car': echo 'fa-car'; break;
                                    case 'bicycle': echo 'fa-bicycle'; break;
                                    default: echo 'fa-truck';
                                }
                            ?> me-2 text-muted"></i>
                            <strong>Jenis Kendaraan:</strong>
                            <?php
                                switch ($driver['vehicle_type']) {
                                    case 'motorcycle': echo 'Motor'; break;
                                    case 'car': echo 'Mobil'; break;
                                    case 'bicycle': echo 'Sepeda'; break;
                                    default: echo $driver['vehicle_type'];
                                }
                            ?>
                        </p>
                        <?php if (!empty($driver['license_plate'])): ?>
                            <p>
                                <i class="fas fa-id-card me-2 text-muted"></i>
                                <strong>Plat Nomor:</strong> <?= $driver['license_plate'] ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Statistik Pengemudi</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0"><?= $orderCount ?></h3>
                                    <p class="text-muted mb-0">Total Pesanan</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0"><?= $completedOrderCount ?></h3>
                                    <p class="text-muted mb-0">Pesanan Selesai</p>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0">Rp<?= number_format($totalEarnings * 15000, 0, ',', '.') ?></h3>
                                    <p class="text-muted mb-0">Total Pendapatan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ubah Status</h5>
                    </div>
                    <div class="card-body p-4">
                        <form action="view_driver.php?id=<?= $driver_id ?>" method="post">
                            <input type="hidden" name="action" value="update_status">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status Baru</label>
                                <select name="status" id="status" class="form-select" required>
                                    <option value="">Pilih Status</option>
                                    <option value="pending" <?= $driver['status'] === 'pending' ? 'selected' : '' ?>>Menunggu</option>
                                    <option value="active" <?= $driver['status'] === 'active' ? 'selected' : '' ?>>Aktif</option>
                                    <option value="suspended" <?= $driver['status'] === 'suspended' ? 'selected' : '' ?>>Ditangguhkan</option>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Perbarui Status</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h4>Belum ada pesanan</h4>
                                <p class="text-muted">Pengemudi ini belum menangani pesanan.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Pelanggan</th>
                                            <th>Restoran</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['user_name'] ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td>Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>
                                                    <span class="badge <?php
                                                        switch ($order['order_status']) {
                                                            case 'pending': echo 'bg-warning text-dark'; break;
                                                            case 'confirmed': echo 'bg-info text-dark'; break;
                                                            case 'preparing': echo 'bg-primary'; break;
                                                            case 'ready_for_pickup': echo 'bg-primary'; break;
                                                            case 'picked_up': echo 'bg-info'; break;
                                                            case 'on_the_way': echo 'bg-info'; break;
                                                            case 'delivered': echo 'bg-success'; break;
                                                            case 'cancelled': echo 'bg-danger'; break;
                                                            default: echo 'bg-secondary';
                                                        }
                                                    ?>">
                                                        <?php
                                                            switch ($order['order_status']) {
                                                                case 'pending': echo 'Menunggu'; break;
                                                                case 'confirmed': echo 'Dikonfirmasi'; break;
                                                                case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                                                case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                                                case 'picked_up': echo 'Diambil'; break;
                                                                case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                                                case 'delivered': echo 'Terkirim'; break;
                                                                case 'cancelled': echo 'Dibatalkan'; break;
                                                                default: echo $order['order_status'];
                                                            }
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                <td>
                                                    <a href="view_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> Detail
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if ($orderCount > 10): ?>
                                <div class="text-center mt-3">
                                    <a href="orders.php?driver_id=<?= $driver_id ?>" class="btn btn-outline-primary">
                                        Lihat Semua Pesanan
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
