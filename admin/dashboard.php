<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get admin information
$admin_id = $_SESSION['admin_id'];
$stmt = $conn->prepare("SELECT * FROM admins WHERE admin_id = :admin_id");
$stmt->bindParam(':admin_id', $admin_id);
$stmt->execute();
$admin = $stmt->fetch();

// Get statistics
// Count users
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users");
$stmt->execute();
$userCount = $stmt->fetch()['total'];

// Count drivers
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM drivers");
$stmt->execute();
$driverCount = $stmt->fetch()['total'];

// Count restaurants
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM restaurants");
$stmt->execute();
$restaurantCount = $stmt->fetch()['total'];

// Count orders
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM orders");
$stmt->execute();
$orderCount = $stmt->fetch()['total'];

// Get pending restaurant owners
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE status = 'pending' ORDER BY created_at DESC");
$stmt->execute();
$pendingOwners = $stmt->fetchAll();

// Get recent orders
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, u.name as user_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    ORDER BY o.created_at DESC
    LIMIT 10
");
$stmt->execute();
$recentOrders = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dasbor Admin - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Dasbor Admin</h1>
                <p class="text-muted">Selamat datang, <?= $admin['name'] ?> (<?= $admin['role'] ?>)</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group">
                    <a href="users.php" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>Pengguna
                    </a>
                    <a href="drivers.php" class="btn btn-outline-warning">
                        <i class="fas fa-motorcycle me-2"></i>Pengemudi
                    </a>
                    <a href="restaurant_owners.php" class="btn btn-outline-info">
                        <i class="fas fa-user-tie me-2"></i>Pemilik
                    </a>
                    <a href="restaurants.php" class="btn btn-outline-success">
                        <i class="fas fa-store me-2"></i>Restoran
                    </a>
                    <a href="orders.php" class="btn btn-outline-danger">
                        <i class="fas fa-shopping-cart me-2"></i>Pesanan
                    </a>
                    <a href="complaints.php" class="btn btn-outline-secondary">
                        <i class="fas fa-exclamation-circle me-2"></i>Keluhan
                    </a>
                    <a href="complaint_chat.php" class="btn btn-outline-dark">
                        <i class="fas fa-comments me-2"></i>Chat
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pengguna</h6>
                                <h2 class="mb-0"><?= $userCount ?></h2>
                            </div>
                            <i class="fas fa-users fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="users.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Restoran</h6>
                                <h2 class="mb-0"><?= $restaurantCount ?></h2>
                            </div>
                            <i class="fas fa-store fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="restaurants.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pengemudi</h6>
                                <h2 class="mb-0"><?= $driverCount ?></h2>
                            </div>
                            <i class="fas fa-motorcycle fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="drivers.php" class="text-dark text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-dark"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pesanan</h6>
                                <h2 class="mb-0"><?= $orderCount ?></h2>
                            </div>
                            <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="orders.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Restaurant Owners -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pemilik Restoran yang Menunggu Persetujuan</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pendingOwners)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                <h4>Tidak ada pemilik restoran yang menunggu persetujuan</h4>
                                <p class="text-muted">Semua pemilik restoran telah disetujui.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Nama</th>
                                            <th>Email</th>
                                            <th>Telepon</th>
                                            <th>Tanggal Daftar</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pendingOwners as $owner): ?>
                                            <tr>
                                                <td><?= $owner['owner_id'] ?></td>
                                                <td><?= $owner['name'] ?></td>
                                                <td><?= $owner['email'] ?></td>
                                                <td><?= $owner['phone'] ?></td>
                                                <td><?= date('d/m/Y H:i', strtotime($owner['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="approve_owner.php?id=<?= $owner['owner_id'] ?>" class="btn btn-success">
                                                            <i class="fas fa-check"></i> Setujui
                                                        </a>
                                                        <a href="reject_owner.php?id=<?= $owner['owner_id'] ?>" class="btn btn-danger">
                                                            <i class="fas fa-times"></i> Tolak
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentOrders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h4>Belum ada pesanan</h4>
                                <p class="text-muted">Pesanan akan muncul di sini saat pelanggan mulai memesan.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Pelanggan</th>
                                            <th>Restoran</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentOrders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['user_name'] ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td>Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>
                                                    <span class="badge <?php
                                                        switch ($order['order_status']) {
                                                            case 'pending': echo 'bg-warning text-dark'; break;
                                                            case 'confirmed': echo 'bg-info text-dark'; break;
                                                            case 'preparing': echo 'bg-primary'; break;
                                                            case 'ready_for_pickup': echo 'bg-primary'; break;
                                                            case 'picked_up': echo 'bg-info'; break;
                                                            case 'on_the_way': echo 'bg-info'; break;
                                                            case 'delivered': echo 'bg-success'; break;
                                                            case 'cancelled': echo 'bg-danger'; break;
                                                            default: echo 'bg-secondary';
                                                        }
                                                    ?>">
                                                        <?php
                                                            switch ($order['order_status']) {
                                                                case 'pending': echo 'Menunggu'; break;
                                                                case 'confirmed': echo 'Dikonfirmasi'; break;
                                                                case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                                                case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                                                case 'picked_up': echo 'Diambil'; break;
                                                                case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                                                case 'delivered': echo 'Terkirim'; break;
                                                                case 'cancelled': echo 'Dibatalkan'; break;
                                                                default: echo $order['order_status'];
                                                            }
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                <td>
                                                    <a href="view_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> Detail
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-3">
                                <a href="orders.php" class="btn btn-outline-primary">Lihat Semua Pesanan</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
