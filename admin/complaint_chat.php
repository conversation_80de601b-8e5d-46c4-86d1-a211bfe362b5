<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get admin information
$admin_id = $_SESSION['admin_id'];
$stmt = $conn->prepare("SELECT * FROM admins WHERE admin_id = :admin_id");
$stmt->bindParam(':admin_id', $admin_id);
$stmt->execute();
$admin = $stmt->fetch();

// Check if complaint ID is provided
$complaint_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Get active room ID from URL if present
$active_room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;

// If complaint ID is provided, get its chat room
if ($complaint_id > 0) {
    $stmt = $conn->prepare("
        SELECT room_id
        FROM chat_rooms
        WHERE room_type = 'complaint' AND order_id = :complaint_id
    ");
    $stmt->bindParam(':complaint_id', $complaint_id);
    $stmt->execute();
    $room = $stmt->fetch();
    
    if ($room) {
        $active_room_id = $room['room_id'];
    }
}

// Check if the room exists and admin has access
$has_access = false;
if ($active_room_id > 0) {
    // Check if admin is already a participant
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM chat_participants 
        WHERE room_id = :room_id AND user_type = 'admin' AND user_id = :admin_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':admin_id', $admin_id);
    $stmt->execute();
    $is_participant = ($stmt->fetchColumn() > 0);
    
    if (!$is_participant) {
        // Add admin as participant
        $stmt = $conn->prepare("
            INSERT INTO chat_participants (room_id, user_type, user_id)
            VALUES (:room_id, 'admin', :admin_id)
        ");
        $stmt->bindParam(':room_id', $active_room_id);
        $stmt->bindParam(':admin_id', $admin_id);
        $stmt->execute();
    }
    
    $has_access = true;
}

// Get chat rooms for complaints
$stmt = $conn->prepare("
    SELECT cr.*, 
           c.complaint_id,
           c.subject AS complaint_subject,
           c.status AS complaint_status,
           c.priority AS complaint_priority,
           u.name AS customer_name,
           (SELECT COUNT(*) FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            AND cm.is_read = 0
            AND cm.sender_type != 'admin'
            AND cm.sender_id != :admin_id) AS unread_count,
           (SELECT cm.message_text FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message,
           (SELECT cm.created_at FROM chat_messages cm 
            WHERE cm.room_id = cr.room_id 
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message_time
    FROM chat_rooms cr
    JOIN complaints c ON cr.order_id = c.complaint_id AND cr.room_type = 'complaint'
    JOIN users u ON c.user_id = u.user_id
    ORDER BY 
        CASE c.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        cr.updated_at DESC
");
$stmt->bindParam(':admin_id', $admin_id);
$stmt->execute();
$chat_rooms = $stmt->fetchAll();

// If no active room but rooms exist, select the first one
if ($active_room_id == 0 && count($chat_rooms) > 0) {
    $active_room_id = $chat_rooms[0]['room_id'];
    $has_access = true;
}

// Get room details and participants if active room exists
$room_details = null;
$participants = [];
if ($active_room_id > 0) {
    // Get room details
    $stmt = $conn->prepare("
        SELECT cr.*, 
               c.complaint_id,
               c.subject AS complaint_subject,
               c.status AS complaint_status,
               c.priority AS complaint_priority,
               c.description AS complaint_description,
               u.name AS customer_name,
               u.user_id
        FROM chat_rooms cr
        JOIN complaints c ON cr.order_id = c.complaint_id AND cr.room_type = 'complaint'
        JOIN users u ON c.user_id = u.user_id
        WHERE cr.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $room_details = $stmt->fetch();
    
    // Get participants
    $stmt = $conn->prepare("
        SELECT cp.*, 
               u.name AS customer_name,
               ro.name AS restaurant_owner_name,
               d.name AS driver_name,
               a.name AS admin_name
        FROM chat_participants cp
        LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
        LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
        LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
        LEFT JOIN admins a ON cp.user_type = 'admin' AND cp.user_id = a.admin_id
        WHERE cp.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $participants = $stmt->fetchAll();
    
    // Mark messages as read
    $stmt = $conn->prepare("
        UPDATE chat_messages 
        SET is_read = 1 
        WHERE room_id = :room_id 
        AND sender_type != 'admin' 
        AND sender_id != :admin_id
        AND is_read = 0
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':admin_id', $admin_id);
    $stmt->execute();
    
    // Update last read timestamp
    $stmt = $conn->prepare("
        UPDATE chat_participants 
        SET last_read_at = NOW() 
        WHERE room_id = :room_id 
        AND user_type = 'admin' 
        AND user_id = :admin_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':admin_id', $admin_id);
    $stmt->execute();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Keluhan - KikaZen Ship Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="../css/chat.css">
</head>
<body data-user-type="admin" data-user-id="<?= $admin_id ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">Pengguna</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="drivers.php">Pengemudi</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">Pesanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="complaints.php">Keluhan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Laporan</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $admin['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="settings.php">Pengaturan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="h3 mb-0">Chat Keluhan</h1>
                <p class="text-muted">Komunikasi dengan pelanggan tentang keluhan mereka</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="complaints.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar Keluhan
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Chat Rooms List -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">Daftar Keluhan</h5>
                    </div>
                    <div class="list-group list-group-flush" id="chat-rooms-list">
                        <?php if (count($chat_rooms) === 0): ?>
                            <div class="text-center p-3">Tidak ada keluhan dengan chat yang aktif</div>
                        <?php else: ?>
                            <?php foreach ($chat_rooms as $room): ?>
                                <?php
                                $unreadBadge = $room['unread_count'] > 0 ? '<span class="badge bg-danger rounded-pill">' . $room['unread_count'] . '</span>' : '';
                                $lastMessageTime = $room['last_message_time'] ? date('H:i', strtotime($room['last_message_time'])) : '';
                                
                                // Priority badge
                                $priorityBadge = '';
                                switch ($room['complaint_priority']) {
                                    case 'urgent':
                                        $priorityBadge = '<span class="badge bg-danger me-1">Mendesak</span>';
                                        break;
                                    case 'high':
                                        $priorityBadge = '<span class="badge bg-warning text-dark me-1">Tinggi</span>';
                                        break;
                                    case 'medium':
                                        $priorityBadge = '<span class="badge bg-info text-dark me-1">Sedang</span>';
                                        break;
                                    case 'low':
                                        $priorityBadge = '<span class="badge bg-success me-1">Rendah</span>';
                                        break;
                                }
                                
                                // Status badge
                                $statusBadge = '';
                                switch ($room['complaint_status']) {
                                    case 'pending':
                                        $statusBadge = '<span class="badge bg-warning text-dark me-1">Tertunda</span>';
                                        break;
                                    case 'in_progress':
                                        $statusBadge = '<span class="badge bg-primary me-1">Dalam Proses</span>';
                                        break;
                                    case 'resolved':
                                        $statusBadge = '<span class="badge bg-success me-1">Terselesaikan</span>';
                                        break;
                                    case 'closed':
                                        $statusBadge = '<span class="badge bg-secondary me-1">Ditutup</span>';
                                        break;
                                }
                                ?>
                                <a href="complaint_chat.php?room_id=<?= $room['room_id'] ?>" class="chat-room-item list-group-item list-group-item-action d-flex justify-content-between align-items-start <?= $room['room_id'] == $active_room_id ? 'active' : '' ?> <?= $room['status'] === 'closed' ? 'bg-light' : '' ?>" data-room-id="<?= $room['room_id'] ?>">
                                    <div class="ms-2 me-auto">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Keluhan #<?= $room['complaint_id'] ?></h6>
                                            <small><?= $lastMessageTime ?></small>
                                        </div>
                                        <div class="small mb-1">
                                            <?= $priorityBadge ?> <?= $statusBadge ?>
                                        </div>
                                        <div class="small text-muted">Pelanggan: <?= $room['customer_name'] ?></div>
                                        <p class="mb-1 text-truncate"><?= $room['last_message'] ?: 'Belum ada pesan' ?></p>
                                    </div>
                                    <?= $unreadBadge ?>
                                </a>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div class="col-md-8">
                <?php if ($active_room_id > 0 && $has_access): ?>
                    <div class="card chat-card">
                        <div class="card-header bg-dark text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title mb-0">Keluhan #<?= $room_details['complaint_id'] ?></h5>
                                    <p class="mb-0 small"><?= htmlspecialchars($room_details['complaint_subject']) ?></p>
                                </div>
                                <div>
                                    <a href="complaint_detail.php?id=<?= $room_details['complaint_id'] ?>" class="btn btn-sm btn-outline-light">
                                        <i class="fas fa-info-circle me-1"></i>Detail Keluhan
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="chat-messages" id="chat-messages" data-room-id="<?= $active_room_id ?>">
                                <div class="text-center p-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Memuat...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if ($room_details['status'] === 'active'): ?>
                                <form id="message-form" class="d-flex">
                                    <input type="text" id="message-input" class="form-control me-2" placeholder="Ketik pesan..." required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </form>
                                <div class="mt-2 d-flex">
                                    <button id="emoji-button" class="btn btn-sm btn-outline-secondary me-2" type="button">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <div class="position-relative">
                                        <input type="file" id="file-upload" class="d-none" accept="image/*">
                                        <button id="upload-button" class="btn btn-sm btn-outline-secondary" type="button" onclick="document.getElementById('file-upload').click()">
                                            <i class="fas fa-image"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-secondary mb-0">
                                    Percakapan ini telah ditutup dan tidak dapat menerima pesan baru.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center p-5">
                            <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                            <h3>Tidak ada percakapan yang dipilih</h3>
                            <p class="text-muted">Pilih keluhan dari daftar untuk melihat percakapan.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-4 bg-dark mt-auto">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between small">
                <div class="text-muted">Hak Cipta &copy; KikaZen Ship <?= date('Y') ?></div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/admin.js"></script>
    <script src="../js/chat.js"></script>
</body>
</html>
