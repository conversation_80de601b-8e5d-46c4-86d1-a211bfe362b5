<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Check if owner ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: dashboard.php');
    exit;
}

$owner_id = $_GET['id'];
$success = '';
$error = '';

// Get owner information
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    $error = 'Pemilik restoran tidak ditemukan';
} else {
    $owner = $stmt->fetch();
    
    // Check if owner is already approved
    if ($owner['status'] === 'active') {
        $error = 'Pemilik restoran sudah disetujui sebelumnya';
    } else {
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Update owner status to active
                $stmt = $conn->prepare("UPDATE restaurant_owners SET status = 'active' WHERE owner_id = :owner_id");
                $stmt->bindParam(':owner_id', $owner_id);
                
                if ($stmt->execute()) {
                    $success = 'Pemilik restoran berhasil disetujui';
                    
                    // Redirect to dashboard after 2 seconds
                    header('refresh:2;url=dashboard.php');
                } else {
                    $error = 'Gagal menyetujui pemilik restoran';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setujui Pemilik Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Approve Owner Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Setujui Pemilik Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Setujui Pemilik Restoran</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
            <div class="text-center mt-3">
                <a href="dashboard.php" class="btn btn-primary">Kembali ke Dasbor</a>
            </div>
        <?php elseif ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
            <div class="text-center mt-3">
                <a href="dashboard.php" class="btn btn-primary">Kembali ke Dasbor</a>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <h5 class="card-title mb-4">Detail Pemilik Restoran</h5>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">ID</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= $owner['owner_id'] ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Nama</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= $owner['name'] ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Email</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= $owner['email'] ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Telepon</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= $owner['phone'] ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Alamat</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= $owner['address'] ?: 'Tidak ada' ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Status</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">
                                        <span class="badge bg-warning text-dark">Menunggu Persetujuan</span>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mb-3 row">
                                <label class="col-sm-3 col-form-label fw-bold">Tanggal Daftar</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext"><?= date('d/m/Y H:i', strtotime($owner['created_at'])) ?></p>
                                </div>
                            </div>
                            
                            <form action="approve_owner.php?id=<?= $owner_id ?>" method="post">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Dengan menyetujui pemilik restoran ini, mereka akan dapat menambahkan dan mengelola restoran mereka di platform KikaZen Ship.
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <a href="dashboard.php" class="btn btn-outline-secondary me-md-2">Batal</a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-check me-2"></i>Setujui Pemilik Restoran
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Informasi</h5>
                        </div>
                        <div class="card-body">
                            <p>Setelah menyetujui pemilik restoran:</p>
                            <ul>
                                <li>Mereka akan dapat masuk ke akun mereka</li>
                                <li>Mereka dapat menambahkan restoran baru</li>
                                <li>Mereka dapat mengelola menu restoran</li>
                                <li>Mereka dapat menerima dan memproses pesanan</li>
                            </ul>
                            <p>Anda dapat menangguhkan akun pemilik restoran kapan saja jika diperlukan.</p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
