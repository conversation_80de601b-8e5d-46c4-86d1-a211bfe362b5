<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get admin information
$admin_id = $_SESSION['admin_id'];
$stmt = $conn->prepare("SELECT * FROM admins WHERE admin_id = :admin_id");
$stmt->bindParam(':admin_id', $admin_id);
$stmt->execute();
$admin = $stmt->fetch();

// Handle status filter
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$status_condition = '';
$status_params = [];

if ($status_filter === 'pending') {
    $status_condition = "WHERE c.status = 'pending'";
} elseif ($status_filter === 'in_progress') {
    $status_condition = "WHERE c.status = 'in_progress'";
} elseif ($status_filter === 'resolved') {
    $status_condition = "WHERE c.status = 'resolved'";
} elseif ($status_filter === 'closed') {
    $status_condition = "WHERE c.status = 'closed'";
} elseif ($status_filter === 'active') {
    $status_condition = "WHERE c.status IN ('pending', 'in_progress')";
}

// Handle priority filter
$priority_filter = isset($_GET['priority']) ? $_GET['priority'] : 'all';
if ($priority_filter !== 'all') {
    if (empty($status_condition)) {
        $status_condition = "WHERE c.priority = :priority";
    } else {
        $status_condition .= " AND c.priority = :priority";
    }
    $status_params[':priority'] = $priority_filter;
}

// Handle search
$search = isset($_GET['search']) ? $_GET['search'] : '';
if (!empty($search)) {
    if (empty($status_condition)) {
        $status_condition = "WHERE (c.subject LIKE :search OR c.description LIKE :search OR u.name LIKE :search)";
    } else {
        $status_condition .= " AND (c.subject LIKE :search OR c.description LIKE :search OR u.name LIKE :search)";
    }
    $status_params[':search'] = "%$search%";
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Get total complaints count
$count_query = "
    SELECT COUNT(*) 
    FROM complaints c
    LEFT JOIN users u ON c.user_id = u.user_id
    $status_condition
";
$stmt = $conn->prepare($count_query);
foreach ($status_params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_complaints = $stmt->fetchColumn();
$total_pages = ceil($total_complaints / $limit);

// Get complaints
$query = "
    SELECT c.*, 
           u.name AS user_name,
           u.email AS user_email,
           o.order_id,
           r.name AS restaurant_name,
           a.name AS admin_name,
           (SELECT COUNT(*) FROM complaint_updates cu WHERE cu.complaint_id = c.complaint_id) AS update_count,
           (SELECT COUNT(*) FROM chat_messages cm 
            JOIN chat_rooms cr ON cm.room_id = cr.room_id 
            WHERE cr.room_type = 'complaint' AND cr.order_id = c.complaint_id AND cm.is_read = 0 AND cm.sender_type != 'admin') AS unread_count
    FROM complaints c
    LEFT JOIN users u ON c.user_id = u.user_id
    LEFT JOIN orders o ON c.order_id = o.order_id
    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
    $status_condition
    ORDER BY 
        CASE c.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        c.created_at DESC
    LIMIT :limit OFFSET :offset
";

$stmt = $conn->prepare($query);
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
foreach ($status_params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$complaints = $stmt->fetchAll();

// Get complaint statistics
$stats_query = "
    SELECT 
        COUNT(CASE WHEN status = 'pending' THEN 1 END) AS pending_count,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) AS in_progress_count,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) AS resolved_count,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) AS closed_count,
        COUNT(CASE WHEN priority = 'urgent' THEN 1 END) AS urgent_count,
        COUNT(CASE WHEN priority = 'high' THEN 1 END) AS high_count
    FROM complaints
";
$stmt = $conn->prepare($stats_query);
$stmt->execute();
$stats = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keluhan - KikaZen Ship Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">Pengguna</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="drivers.php">Pengemudi</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">Pesanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="complaints.php">Keluhan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Laporan</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $admin['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="settings.php">Pengaturan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="h3 mb-0">Manajemen Keluhan</h1>
                <p class="text-muted">Kelola dan tanggapi keluhan pelanggan</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="complaint_chat.php" class="btn btn-primary">
                    <i class="fas fa-comments me-2"></i>Chat Keluhan
                    <?php if (($stats['pending_count'] + $stats['in_progress_count']) > 0): ?>
                        <span class="badge bg-danger ms-2"><?= $stats['pending_count'] + $stats['in_progress_count'] ?></span>
                    <?php endif; ?>
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Keluhan Tertunda</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['pending_count'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Dalam Proses</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['in_progress_count'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-spinner fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Terselesaikan</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['resolved_count'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    Prioritas Tinggi</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['urgent_count'] + $stats['high_count'] ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="all" <?= $status_filter === 'all' ? 'selected' : '' ?>>Semua Status</option>
                            <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Tertunda</option>
                            <option value="in_progress" <?= $status_filter === 'in_progress' ? 'selected' : '' ?>>Dalam Proses</option>
                            <option value="resolved" <?= $status_filter === 'resolved' ? 'selected' : '' ?>>Terselesaikan</option>
                            <option value="closed" <?= $status_filter === 'closed' ? 'selected' : '' ?>>Ditutup</option>
                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Aktif (Tertunda & Dalam Proses)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="priority" class="form-label">Prioritas</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="all" <?= $priority_filter === 'all' ? 'selected' : '' ?>>Semua Prioritas</option>
                            <option value="urgent" <?= $priority_filter === 'urgent' ? 'selected' : '' ?>>Mendesak</option>
                            <option value="high" <?= $priority_filter === 'high' ? 'selected' : '' ?>>Tinggi</option>
                            <option value="medium" <?= $priority_filter === 'medium' ? 'selected' : '' ?>>Sedang</option>
                            <option value="low" <?= $priority_filter === 'low' ? 'selected' : '' ?>>Rendah</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label">Cari</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Cari subjek, deskripsi, atau nama pengguna" value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Complaints Table -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-table me-1"></i>
                Daftar Keluhan
            </div>
            <div class="card-body">
                <?php if (count($complaints) === 0): ?>
                    <div class="alert alert-info">Tidak ada keluhan yang ditemukan.</div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Subjek</th>
                                    <th>Pelanggan</th>
                                    <th>Tipe</th>
                                    <th>Status</th>
                                    <th>Prioritas</th>
                                    <th>Ditugaskan Kepada</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($complaints as $complaint): ?>
                                    <tr>
                                        <td><?= $complaint['complaint_id'] ?></td>
                                        <td>
                                            <?= htmlspecialchars($complaint['subject']) ?>
                                            <?php if ($complaint['unread_count'] > 0): ?>
                                                <span class="badge bg-danger ms-2"><?= $complaint['unread_count'] ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($complaint['user_name']) ?></td>
                                        <td>
                                            <?php
                                            $complaint_type = '';
                                            switch ($complaint['complaint_type']) {
                                                case 'order':
                                                    $complaint_type = 'Pesanan';
                                                    break;
                                                case 'delivery':
                                                    $complaint_type = 'Pengiriman';
                                                    break;
                                                case 'restaurant':
                                                    $complaint_type = 'Restoran';
                                                    break;
                                                case 'app':
                                                    $complaint_type = 'Aplikasi';
                                                    break;
                                                case 'other':
                                                    $complaint_type = 'Lainnya';
                                                    break;
                                            }
                                            echo $complaint_type;
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_badge = '';
                                            switch ($complaint['status']) {
                                                case 'pending':
                                                    $status_badge = '<span class="badge bg-warning">Tertunda</span>';
                                                    break;
                                                case 'in_progress':
                                                    $status_badge = '<span class="badge bg-primary">Dalam Proses</span>';
                                                    break;
                                                case 'resolved':
                                                    $status_badge = '<span class="badge bg-success">Terselesaikan</span>';
                                                    break;
                                                case 'closed':
                                                    $status_badge = '<span class="badge bg-secondary">Ditutup</span>';
                                                    break;
                                            }
                                            echo $status_badge;
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $priority_badge = '';
                                            switch ($complaint['priority']) {
                                                case 'urgent':
                                                    $priority_badge = '<span class="badge bg-danger">Mendesak</span>';
                                                    break;
                                                case 'high':
                                                    $priority_badge = '<span class="badge bg-warning text-dark">Tinggi</span>';
                                                    break;
                                                case 'medium':
                                                    $priority_badge = '<span class="badge bg-info text-dark">Sedang</span>';
                                                    break;
                                                case 'low':
                                                    $priority_badge = '<span class="badge bg-success">Rendah</span>';
                                                    break;
                                            }
                                            echo $priority_badge;
                                            ?>
                                        </td>
                                        <td><?= $complaint['admin_name'] ?: '<span class="text-muted">Belum ditugaskan</span>' ?></td>
                                        <td><?= date('d/m/Y H:i', strtotime($complaint['created_at'])) ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="complaint_detail.php?id=<?= $complaint['complaint_id'] ?>" class="btn btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="complaint_chat.php?id=<?= $complaint['complaint_id'] ?>" class="btn btn-primary">
                                                    <i class="fas fa-comments"></i>
                                                    <?php if ($complaint['unread_count'] > 0): ?>
                                                        <span class="badge bg-danger"><?= $complaint['unread_count'] ?></span>
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mt-4">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $page - 1 ?>&status=<?= $status_filter ?>&priority=<?= $priority_filter ?>&search=<?= urlencode($search) ?>">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link"><span aria-hidden="true">&laquo;</span></span>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&status=<?= $status_filter ?>&priority=<?= $priority_filter ?>&search=<?= urlencode($search) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $page + 1 ?>&status=<?= $status_filter ?>&priority=<?= $priority_filter ?>&search=<?= urlencode($search) ?>">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link"><span aria-hidden="true">&raquo;</span></span>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-4 bg-dark mt-auto">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between small">
                <div class="text-muted">Hak Cipta &copy; KikaZen Ship <?= date('Y') ?></div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/admin.js"></script>
</body>
</html>
