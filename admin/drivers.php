<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Search
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = "WHERE name LIKE ? OR email LIKE ? OR phone LIKE ? OR vehicle_type LIKE ? OR license_plate LIKE ?";
    $searchParam = "%$search%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam];
}

// Filter by status
$status = isset($_GET['status']) ? $_GET['status'] : '';
if (!empty($status)) {
    if (empty($searchCondition)) {
        $searchCondition = "WHERE status = ?";
    } else {
        $searchCondition .= " AND status = ?";
    }
    $params[] = $status;
}

// Get total drivers count
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM drivers $searchCondition");
if (!empty($params)) {
    $stmt->execute($params);
} else {
    $stmt->execute();
}
$totalDrivers = $stmt->fetch()['total'];
$totalPages = ceil($totalDrivers / $limit);

// Get drivers with pagination
$stmt = $conn->prepare("SELECT * FROM drivers $searchCondition ORDER BY created_at DESC LIMIT :limit OFFSET :offset");
if (!empty($params)) {
    foreach ($params as $key => $param) {
        $stmt->bindValue($key + 1, $param);
    }
}
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$drivers = $stmt->fetchAll();

// Handle driver status update
if (isset($_POST['action']) && isset($_POST['driver_id'])) {
    $driverId = $_POST['driver_id'];
    $action = $_POST['action'];
    
    if ($action === 'approve') {
        $stmt = $conn->prepare("UPDATE drivers SET status = 'active' WHERE driver_id = ?");
        $stmt->execute([$driverId]);
        header('Location: drivers.php?page=' . $page . '&success=Driver approved successfully');
        exit;
    } elseif ($action === 'suspend') {
        $stmt = $conn->prepare("UPDATE drivers SET status = 'suspended' WHERE driver_id = ?");
        $stmt->execute([$driverId]);
        header('Location: drivers.php?page=' . $page . '&success=Driver suspended successfully');
        exit;
    } elseif ($action === 'activate') {
        $stmt = $conn->prepare("UPDATE drivers SET status = 'active' WHERE driver_id = ?");
        $stmt->execute([$driverId]);
        header('Location: drivers.php?page=' . $page . '&success=Driver activated successfully');
        exit;
    } elseif ($action === 'delete') {
        // Check if driver has orders
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE driver_id = ?");
        $stmt->execute([$driverId]);
        $orderCount = $stmt->fetch()['count'];
        
        if ($orderCount > 0) {
            header('Location: drivers.php?page=' . $page . '&error=Cannot delete driver with orders');
            exit;
        }
        
        $stmt = $conn->prepare("DELETE FROM drivers WHERE driver_id = ?");
        $stmt->execute([$driverId]);
        header('Location: drivers.php?page=' . $page . '&success=Driver deleted successfully');
        exit;
    }
}

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Drivers Management Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="mb-0">Manajemen Pengemudi</h1>
                <p class="text-muted">Kelola semua pengemudi aplikasi</p>
            </div>
            <div class="col-md-6">
                <form action="drivers.php" method="get" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" placeholder="Cari pengemudi..." value="<?= htmlspecialchars($search) ?>">
                    <select name="status" class="form-select me-2" style="max-width: 150px;">
                        <option value="">Semua Status</option>
                        <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Menunggu</option>
                        <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Aktif</option>
                        <option value="suspended" <?= $status === 'suspended' ? 'selected' : '' ?>>Ditangguhkan</option>
                    </select>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Daftar Pengemudi</h5>
            </div>
            <div class="card-body">
                <?php if (empty($drivers)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-motorcycle fa-3x text-muted mb-3"></i>
                        <h4>Tidak ada pengemudi ditemukan</h4>
                        <p class="text-muted">Tidak ada pengemudi yang sesuai dengan kriteria pencarian Anda.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>Telepon</th>
                                    <th>Kendaraan</th>
                                    <th>Status</th>
                                    <th>Tanggal Daftar</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($drivers as $driver): ?>
                                    <tr>
                                        <td><?= $driver['driver_id'] ?></td>
                                        <td><?= $driver['name'] ?></td>
                                        <td><?= $driver['email'] ?></td>
                                        <td><?= $driver['phone'] ?></td>
                                        <td>
                                            <?php
                                                switch ($driver['vehicle_type']) {
                                                    case 'motorcycle':
                                                        echo '<i class="fas fa-motorcycle me-1"></i> Motor';
                                                        break;
                                                    case 'car':
                                                        echo '<i class="fas fa-car me-1"></i> Mobil';
                                                        break;
                                                    case 'bicycle':
                                                        echo '<i class="fas fa-bicycle me-1"></i> Sepeda';
                                                        break;
                                                    default:
                                                        echo $driver['vehicle_type'];
                                                }
                                            ?>
                                            <?php if (!empty($driver['license_plate'])): ?>
                                                <small class="text-muted d-block"><?= $driver['license_plate'] ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php
                                                switch ($driver['status']) {
                                                    case 'pending': echo 'bg-warning text-dark'; break;
                                                    case 'active': echo 'bg-success'; break;
                                                    case 'suspended': echo 'bg-danger'; break;
                                                    default: echo 'bg-secondary';
                                                }
                                            ?>">
                                                <?php
                                                    switch ($driver['status']) {
                                                        case 'pending': echo 'Menunggu'; break;
                                                        case 'active': echo 'Aktif'; break;
                                                        case 'suspended': echo 'Ditangguhkan'; break;
                                                        default: echo $driver['status'];
                                                    }
                                                ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y H:i', strtotime($driver['created_at'])) ?></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Aksi
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="view_driver.php?id=<?= $driver['driver_id'] ?>">
                                                            <i class="fas fa-eye me-2"></i> Lihat Detail
                                                        </a>
                                                    </li>
                                                    <?php if ($driver['status'] === 'pending'): ?>
                                                        <li>
                                                            <form action="drivers.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menyetujui pengemudi ini?')">
                                                                <input type="hidden" name="driver_id" value="<?= $driver['driver_id'] ?>">
                                                                <input type="hidden" name="action" value="approve">
                                                                <button type="submit" class="dropdown-item text-success">
                                                                    <i class="fas fa-check-circle me-2"></i> Setujui
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php elseif ($driver['status'] === 'active'): ?>
                                                        <li>
                                                            <form action="drivers.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menangguhkan pengemudi ini?')">
                                                                <input type="hidden" name="driver_id" value="<?= $driver['driver_id'] ?>">
                                                                <input type="hidden" name="action" value="suspend">
                                                                <button type="submit" class="dropdown-item text-warning">
                                                                    <i class="fas fa-ban me-2"></i> Tangguhkan
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php elseif ($driver['status'] === 'suspended'): ?>
                                                        <li>
                                                            <form action="drivers.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin mengaktifkan kembali pengemudi ini?')">
                                                                <input type="hidden" name="driver_id" value="<?= $driver['driver_id'] ?>">
                                                                <input type="hidden" name="action" value="activate">
                                                                <button type="submit" class="dropdown-item text-success">
                                                                    <i class="fas fa-check-circle me-2"></i> Aktifkan
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form action="drivers.php?page=<?= $page ?>" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menghapus pengemudi ini? Tindakan ini tidak dapat dibatalkan.')">
                                                            <input type="hidden" name="driver_id" value="<?= $driver['driver_id'] ?>">
                                                            <input type="hidden" name="action" value="delete">
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i class="fas fa-trash-alt me-2"></i> Hapus
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($status) ? '&status=' . urlencode($status) : '' ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($status) ? '&status=' . urlencode($status) : '' ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($status) ? '&status=' . urlencode($status) : '' ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
