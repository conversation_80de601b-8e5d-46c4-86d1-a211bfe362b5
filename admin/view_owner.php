<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Check if owner ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: restaurant_owners.php');
    exit;
}

$owner_id = $_GET['id'];

// Get owner details
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = ?");
$stmt->execute([$owner_id]);

if ($stmt->rowCount() === 0) {
    header('Location: restaurant_owners.php');
    exit;
}

$owner = $stmt->fetch();

// Get owner's restaurants
$stmt = $conn->prepare("
    SELECT r.*, 
           (SELECT COUNT(*) FROM menu_items WHERE restaurant_id = r.restaurant_id) as menu_count,
           (SELECT COUNT(*) FROM orders WHERE restaurant_id = r.restaurant_id) as order_count
    FROM restaurants r
    WHERE r.owner_id = ?
    ORDER BY r.created_at DESC
");
$stmt->execute([$owner_id]);
$restaurants = $stmt->fetchAll();

// Handle owner status update
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_status') {
        $newStatus = $_POST['status'] ?? '';
        
        if (empty($newStatus)) {
            $error = 'Silakan pilih status baru';
        } else {
            $stmt = $conn->prepare("UPDATE restaurant_owners SET status = ? WHERE owner_id = ?");
            
            if ($stmt->execute([$newStatus, $owner_id])) {
                $success = 'Status pemilik restoran berhasil diperbarui';
                
                // Refresh owner data
                $stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = ?");
                $stmt->execute([$owner_id]);
                $owner = $stmt->fetch();
            } else {
                $error = 'Gagal memperbarui status pemilik restoran';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Pemilik Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Owner Details Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Detail Pemilik Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="restaurant_owners.php">Pemilik Restoran</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Detail Pemilik</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="restaurant_owners.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar Pemilik
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <div class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                <?= substr($owner['name'], 0, 1) ?>
                            </div>
                        </div>
                        <h4><?= $owner['name'] ?></h4>
                        <p class="text-muted"><?= $owner['email'] ?></p>
                        <p>
                            <span class="badge <?php
                                switch ($owner['status']) {
                                    case 'pending': echo 'bg-warning text-dark'; break;
                                    case 'active': echo 'bg-success'; break;
                                    case 'suspended': echo 'bg-danger'; break;
                                    default: echo 'bg-secondary';
                                }
                            ?>">
                                <?php
                                    switch ($owner['status']) {
                                        case 'pending': echo 'Menunggu'; break;
                                        case 'active': echo 'Aktif'; break;
                                        case 'suspended': echo 'Ditangguhkan'; break;
                                        default: echo $owner['status'];
                                    }
                                ?>
                            </span>
                        </p>
                        <p class="text-muted small">
                            <i class="fas fa-clock me-1"></i> Bergabung pada <?= date('d F Y', strtotime($owner['created_at'])) ?>
                        </p>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Kontak</h5>
                    </div>
                    <div class="card-body p-4">
                        <p>
                            <i class="fas fa-envelope me-2 text-muted"></i> <?= $owner['email'] ?>
                        </p>
                        <p>
                            <i class="fas fa-phone me-2 text-muted"></i> <?= $owner['phone'] ?>
                        </p>
                        <?php if (!empty($owner['address'])): ?>
                            <p>
                                <i class="fas fa-map-marker-alt me-2 text-muted"></i> <?= $owner['address'] ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($owner['status'] !== 'deleted'): ?>
                    <div class="card shadow-sm mt-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Ubah Status</h5>
                        </div>
                        <div class="card-body p-4">
                            <form action="view_owner.php?id=<?= $owner_id ?>" method="post">
                                <input type="hidden" name="action" value="update_status">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status Baru</label>
                                    <select name="status" id="status" class="form-select" required>
                                        <option value="">Pilih Status</option>
                                        <option value="pending" <?= $owner['status'] === 'pending' ? 'selected' : '' ?>>Menunggu</option>
                                        <option value="active" <?= $owner['status'] === 'active' ? 'selected' : '' ?>>Aktif</option>
                                        <option value="suspended" <?= $owner['status'] === 'suspended' ? 'selected' : '' ?>>Ditangguhkan</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Perbarui Status</button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Restoran yang Dimiliki</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($restaurants)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <h4>Belum ada restoran</h4>
                                <p class="text-muted">Pemilik ini belum memiliki restoran yang terdaftar.</p>
                            </div>
                        <?php else: ?>
                            <div class="row row-cols-1 row-cols-md-2 g-4">
                                <?php foreach ($restaurants as $restaurant): ?>
                                    <div class="col">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">
                                                        <?= substr($restaurant['name'], 0, 1) ?>
                                                    </div>
                                                    <div>
                                                        <h5 class="card-title mb-0"><?= $restaurant['name'] ?></h5>
                                                        <p class="text-muted small mb-0">
                                                            <i class="fas fa-map-marker-alt me-1"></i> <?= substr($restaurant['address'], 0, 30) ?>...
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="row text-center mb-3">
                                                    <div class="col">
                                                        <div class="border rounded p-2">
                                                            <div class="fw-bold"><?= $restaurant['menu_count'] ?></div>
                                                            <small class="text-muted">Menu</small>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="border rounded p-2">
                                                            <div class="fw-bold"><?= $restaurant['order_count'] ?></div>
                                                            <small class="text-muted">Pesanan</small>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="border rounded p-2">
                                                            <div class="fw-bold"><?= number_format($restaurant['rating'], 1) ?></div>
                                                            <small class="text-muted">Rating</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span class="badge <?= $restaurant['is_active'] ? 'bg-success' : 'bg-danger' ?>">
                                                        <?= $restaurant['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                                    </span>
                                                    <a href="view_restaurant.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i> Detail
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
