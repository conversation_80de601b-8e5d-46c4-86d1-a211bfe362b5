<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include database connection
require_once '../config/database.php';

// Cek apakah ID restoran disediakan
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "ID restoran tidak disediakan.";
    exit;
}

$restaurant_id = intval($_GET['id']);
echo "ID Restoran: " . $restaurant_id . "<br>";

try {
    // Koneksi ke database
    $conn = connectDB();
    echo "Koneksi database berhasil.<br>";
    
    // Ambil detail restoran
    $stmt = $conn->prepare("
        SELECT r.*, ro.name as owner_name, ro.email as owner_email, ro.phone as owner_phone
        FROM restaurants r
        LEFT JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
        WHERE r.restaurant_id = ?
    ");
    $stmt->execute([$restaurant_id]);
    
    if ($stmt->rowCount() === 0) {
        echo "Restoran dengan ID $restaurant_id tidak ditemukan.";
        exit;
    }
    
    $restaurant = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Detail Restoran:<br>";
    echo "Nama: " . $restaurant['name'] . "<br>";
    echo "Alamat: " . $restaurant['address'] . "<br>";
    echo "Telepon: " . $restaurant['phone'] . "<br>";
    echo "Email: " . $restaurant['email'] . "<br>";
    echo "Pemilik: " . $restaurant['owner_name'] . "<br>";
    
    // Ambil menu restoran
    $stmt = $conn->prepare("
        SELECT mi.*, mc.name as category_name
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.category_id
        WHERE mi.restaurant_id = ?
        ORDER BY mc.name, mi.name
    ");
    $stmt->execute([$restaurant_id]);
    $menuItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<br>Menu Restoran:<br>";
    if (empty($menuItems)) {
        echo "Belum ada menu.";
    } else {
        foreach ($menuItems as $item) {
            echo "- " . $item['name'] . " (Rp" . number_format((float)$item['price'] * 15000, 0, ',', '.') . ")<br>";
        }
    }
    
    // Ambil pesanan restoran
    $stmt = $conn->prepare("
        SELECT o.*, u.name as user_name, d.name as driver_name
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.user_id
        LEFT JOIN drivers d ON o.driver_id = d.driver_id
        WHERE o.restaurant_id = ?
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$restaurant_id]);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<br>Pesanan Terbaru:<br>";
    if (empty($orders)) {
        echo "Belum ada pesanan.";
    } else {
        foreach ($orders as $order) {
            echo "- Order #" . $order['order_id'] . " oleh " . $order['user_name'] . " (" . $order['order_status'] . ")<br>";
        }
    }
    
    // Ambil statistik restoran
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE restaurant_id = ?");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $orderCount = $result['count'] ?? 0;
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE restaurant_id = ? AND order_status = 'delivered'");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $completedOrderCount = $result['count'] ?? 0;
    
    $stmt = $conn->prepare("SELECT SUM(total_amount) as total FROM orders WHERE restaurant_id = ? AND order_status = 'delivered'");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $totalRevenue = (!empty($result['total'])) ? $result['total'] : 0;
    
    echo "<br>Statistik Restoran:<br>";
    echo "Total Pesanan: " . number_format($orderCount) . "<br>";
    echo "Pesanan Selesai: " . number_format($completedOrderCount) . "<br>";
    echo "Total Pendapatan: Rp" . number_format((float)$totalRevenue * 15000, 0, ',', '.') . "<br>";
    
} catch (PDOException $e) {
    echo "Error database: " . $e->getMessage();
}
?>
