<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Cek apakah ID restoran disediakan
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "ID restoran tidak disediakan.";
    exit;
}

$restaurant_id = intval($_GET['id']);
echo "ID Restoran: " . $restaurant_id . "<br>";

// Include database connection
require_once '../config/database.php';

try {
    // Koneksi ke database
    $conn = connectDB();
    echo "Koneksi database berhasil.<br>";
    
    // Ambil detail restoran dengan query sederhana
    $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = ?");
    $stmt->execute([$restaurant_id]);
    
    if ($stmt->rowCount() === 0) {
        echo "Restoran dengan ID $restaurant_id tidak ditemukan.";
        exit;
    }
    
    $restaurant = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<h2>Detail Restoran</h2>";
    echo "<p>Nama: " . htmlspecialchars($restaurant['name']) . "</p>";
    echo "<p>Alamat: " . htmlspecialchars($restaurant['address']) . "</p>";
    echo "<p>Telepon: " . htmlspecialchars($restaurant['phone']) . "</p>";
    
    if (isset($restaurant['email'])) {
        echo "<p>Email: " . htmlspecialchars($restaurant['email']) . "</p>";
    }
    
    echo "<p>Status: " . ($restaurant['is_active'] ? 'Aktif' : 'Tidak Aktif') . "</p>";
    
    if (isset($restaurant['rating'])) {
        echo "<p>Rating: " . number_format($restaurant['rating'], 1) . "</p>";
    }
    
    if (isset($restaurant['created_at'])) {
        echo "<p>Bergabung pada: " . date('d F Y', strtotime($restaurant['created_at'])) . "</p>";
    }
    
} catch (PDOException $e) {
    echo "Error database: " . $e->getMessage();
}
?>
