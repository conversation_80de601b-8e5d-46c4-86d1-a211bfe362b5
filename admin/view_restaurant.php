<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
try {
    $conn = connectDB();
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Include additional functions
require_once '../includes/functions.php';

// Check if restaurant ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: restaurants.php');
    exit;
}

$restaurant_id = intval($_GET['id']);

// Get restaurant details
try {
    // Periksa apakah tabel restaurants memiliki kolom category_id
    $stmt = $conn->query("SHOW COLUMNS FROM restaurants LIKE 'category_id'");
    $hasCategoryId = $stmt->rowCount() > 0;

    if ($hasCategoryId) {
        $stmt = $conn->prepare("
            SELECT r.*, ro.name as owner_name, ro.email as owner_email, ro.phone as owner_phone,
                   c.name as category_name
            FROM restaurants r
            LEFT JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
            LEFT JOIN categories c ON r.category_id = c.category_id
            WHERE r.restaurant_id = ?
        ");
    } else {
        // Query tanpa join ke tabel categories
        $stmt = $conn->prepare("
            SELECT r.*, ro.name as owner_name, ro.email as owner_email, ro.phone as owner_phone,
                   NULL as category_name
            FROM restaurants r
            LEFT JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
            WHERE r.restaurant_id = ?
        ");
    }

    $stmt->execute([$restaurant_id]);

    if ($stmt->rowCount() === 0) {
        header('Location: restaurants.php');
        exit;
    }

    $restaurant = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Error fetching restaurant details: " . $e->getMessage());
}

// Get restaurant menu items
try {
    // Periksa apakah tabel menu_items ada
    $stmt = $conn->query("SHOW TABLES LIKE 'menu_items'");
    $hasMenuItemsTable = $stmt->rowCount() > 0;

    if ($hasMenuItemsTable) {
        // Periksa apakah tabel menu_categories ada
        $stmt = $conn->query("SHOW TABLES LIKE 'menu_categories'");
        $hasMenuCategoriesTable = $stmt->rowCount() > 0;

        if ($hasMenuCategoriesTable) {
            $stmt = $conn->prepare("
                SELECT mi.*, mc.name as category_name
                FROM menu_items mi
                LEFT JOIN menu_categories mc ON mi.category_id = mc.category_id
                WHERE mi.restaurant_id = ?
                ORDER BY mc.name, mi.name
            ");
        } else {
            $stmt = $conn->prepare("
                SELECT mi.*, NULL as category_name
                FROM menu_items mi
                WHERE mi.restaurant_id = ?
                ORDER BY mi.name
            ");
        }

        $stmt->execute([$restaurant_id]);
        $menuItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $menuItems = [];
    }
} catch (PDOException $e) {
    $menuItems = [];
    // Log error but continue
    error_log("Error fetching menu items: " . $e->getMessage());
}

// Get restaurant orders
try {
    // Periksa apakah tabel orders ada
    $stmt = $conn->query("SHOW TABLES LIKE 'orders'");
    $hasOrdersTable = $stmt->rowCount() > 0;

    if ($hasOrdersTable) {
        // Periksa apakah tabel users ada
        $stmt = $conn->query("SHOW TABLES LIKE 'users'");
        $hasUsersTable = $stmt->rowCount() > 0;

        // Periksa apakah tabel drivers ada
        $stmt = $conn->query("SHOW TABLES LIKE 'drivers'");
        $hasDriversTable = $stmt->rowCount() > 0;

        if ($hasUsersTable && $hasDriversTable) {
            $stmt = $conn->prepare("
                SELECT o.*, u.name as user_name, d.name as driver_name
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.user_id
                LEFT JOIN drivers d ON o.driver_id = d.driver_id
                WHERE o.restaurant_id = ?
                ORDER BY o.created_at DESC
                LIMIT 10
            ");
        } else if ($hasUsersTable) {
            $stmt = $conn->prepare("
                SELECT o.*, u.name as user_name, NULL as driver_name
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.user_id
                WHERE o.restaurant_id = ?
                ORDER BY o.created_at DESC
                LIMIT 10
            ");
        } else {
            $stmt = $conn->prepare("
                SELECT o.*, NULL as user_name, NULL as driver_name
                FROM orders o
                WHERE o.restaurant_id = ?
                ORDER BY o.created_at DESC
                LIMIT 10
            ");
        }

        $stmt->execute([$restaurant_id]);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $orders = [];
    }
} catch (PDOException $e) {
    $orders = [];
    // Log error but continue
    error_log("Error fetching orders: " . $e->getMessage());
}

// Get restaurant stats
try {
    // Total orders
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE restaurant_id = ?");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $orderCount = $result['count'] ?? 0;

    // Completed orders
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE restaurant_id = ? AND order_status = 'delivered'");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $completedOrderCount = $result['count'] ?? 0;

    // Total revenue
    $stmt = $conn->prepare("SELECT SUM(total_amount) as total FROM orders WHERE restaurant_id = ? AND order_status = 'delivered'");
    $stmt->execute([$restaurant_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $totalRevenue = (!empty($result['total'])) ? $result['total'] : 0;
} catch (PDOException $e) {
    // Set default values
    $orderCount = 0;
    $completedOrderCount = 0;
    $totalRevenue = 0;
    // Log error but continue
    error_log("Error fetching restaurant stats: " . $e->getMessage());
}

// Handle restaurant status update
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_status') {
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        $stmt = $conn->prepare("UPDATE restaurants SET is_active = ? WHERE restaurant_id = ?");

        if ($stmt->execute([$isActive, $restaurant_id])) {
            $success = 'Status restoran berhasil diperbarui';

            // Refresh restaurant data
            $stmt = $conn->prepare("
                SELECT r.*, ro.name as owner_name, ro.email as owner_email, ro.phone as owner_phone,
                       c.name as category_name
                FROM restaurants r
                LEFT JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
                LEFT JOIN categories c ON r.category_id = c.category_id
                WHERE r.restaurant_id = ?
            ");
            $stmt->execute([$restaurant_id]);
            $restaurant = $stmt->fetch();
        } else {
            $error = 'Gagal memperbarui status restoran';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Restaurant Details Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0"><?= $restaurant['name'] ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="restaurants.php">Restoran</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Detail Restoran</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="restaurants.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar Restoran
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <img src="<?= $restaurant['banner_url'] ?? '../assets/restaurant-placeholder.png' ?>" class="card-img-top" alt="<?= $restaurant['name'] ?>" style="height: 200px; object-fit: cover;">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <div class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold; margin-top: -60px; border: 4px solid white;">
                                <?= substr($restaurant['name'], 0, 1) ?>
                            </div>
                        </div>
                        <h4><?= $restaurant['name'] ?></h4>
                        <p class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i> <?= $restaurant['address'] ?>
                        </p>
                        <p>
                            <span class="badge <?= isset($restaurant['is_active']) && $restaurant['is_active'] ? 'bg-success' : 'bg-danger' ?>">
                                <?= isset($restaurant['is_active']) && $restaurant['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                            </span>
                            <?php if (isset($restaurant['category_name']) && $restaurant['category_name']): ?>
                                <span class="badge bg-info"><?= $restaurant['category_name'] ?></span>
                            <?php endif; ?>
                        </p>
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <?php if (isset($restaurant['rating'])): ?>
                            <div class="me-2">
                                <i class="fas fa-star text-warning"></i>
                                <span class="fw-bold"><?= number_format($restaurant['rating'], 1) ?></span>
                            </div>
                            <div class="mx-2">|</div>
                            <?php endif; ?>
                            <div class="ms-2">
                                <i class="fas fa-utensils text-muted me-1"></i>
                                <span><?= count($menuItems) ?> Menu</span>
                            </div>
                        </div>
                        <?php if (isset($restaurant['created_at'])): ?>
                        <p class="text-muted small">
                            <i class="fas fa-clock me-1"></i> Bergabung pada <?= date('d F Y', strtotime($restaurant['created_at'])) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Kontak</h5>
                    </div>
                    <div class="card-body p-4">
                        <?php if (isset($restaurant['phone'])): ?>
                        <p>
                            <i class="fas fa-phone me-2 text-muted"></i> <?= $restaurant['phone'] ?>
                        </p>
                        <?php endif; ?>

                        <?php if (isset($restaurant['email']) && !empty($restaurant['email'])): ?>
                        <p>
                            <i class="fas fa-envelope me-2 text-muted"></i> <?= $restaurant['email'] ?>
                        </p>
                        <?php endif; ?>

                        <?php if (isset($restaurant['opening_hours'])): ?>
                        <p>
                            <i class="fas fa-clock me-2 text-muted"></i> <?= $restaurant['opening_hours'] ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pemilik</h5>
                    </div>
                    <div class="card-body p-4">
                        <?php if (isset($restaurant['owner_name']) && $restaurant['owner_name']): ?>
                            <p>
                                <i class="fas fa-user me-2 text-muted"></i> <?= $restaurant['owner_name'] ?>
                            </p>
                            <?php if (isset($restaurant['owner_email'])): ?>
                            <p>
                                <i class="fas fa-envelope me-2 text-muted"></i> <?= $restaurant['owner_email'] ?>
                            </p>
                            <?php endif; ?>
                            <?php if (isset($restaurant['owner_phone'])): ?>
                            <p>
                                <i class="fas fa-phone me-2 text-muted"></i> <?= $restaurant['owner_phone'] ?>
                            </p>
                            <?php endif; ?>
                            <?php if (isset($restaurant['owner_id'])): ?>
                            <div class="mt-3">
                                <a href="view_owner.php?id=<?= $restaurant['owner_id'] ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i> Lihat Detail Pemilik
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <p class="text-muted">Tidak ada pemilik yang terkait</p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Statistik Restoran</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0"><?= number_format($orderCount) ?></h3>
                                    <p class="text-muted mb-0">Total Pesanan</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0"><?= number_format($completedOrderCount) ?></h3>
                                    <p class="text-muted mb-0">Pesanan Selesai</p>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="border rounded p-3">
                                    <h3 class="mb-0">Rp<?= number_format((float)$totalRevenue * 15000, 0, ',', '.') ?></h3>
                                    <p class="text-muted mb-0">Total Pendapatan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ubah Status</h5>
                    </div>
                    <div class="card-body p-4">
                        <form action="view_restaurant.php?id=<?= $restaurant_id ?>" method="post">
                            <input type="hidden" name="action" value="update_status">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?= isset($restaurant['is_active']) && $restaurant['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">Restoran Aktif</label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Perbarui Status</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Menu Restoran</h5>
                        <a href="restaurant_menu.php?id=<?= $restaurant_id ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-edit me-1"></i> Kelola Menu
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($menuItems)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                                <h4>Belum ada menu</h4>
                                <p class="text-muted">Restoran ini belum memiliki menu yang terdaftar.</p>
                            </div>
                        <?php else: ?>
                            <?php
                            // Group menu items by category
                            $menuByCategory = [];
                            foreach ($menuItems as $item) {
                                $category = $item['category_name'] ?? 'Uncategorized';
                                if (!isset($menuByCategory[$category])) {
                                    $menuByCategory[$category] = [];
                                }
                                $menuByCategory[$category][] = $item;
                            }
                            ?>

                            <?php foreach ($menuByCategory as $category => $items): ?>
                                <h5 class="mt-3 mb-3"><?= $category ?></h5>
                                <div class="row row-cols-1 row-cols-md-2 g-3 mb-4">
                                    <?php foreach ($items as $item): ?>
                                        <div class="col">
                                            <div class="card h-100">
                                                <div class="row g-0">
                                                    <div class="col-4">
                                                        <img src="<?= $item['image_url'] ?? '../assets/food-placeholder.jpg' ?>" class="img-fluid rounded-start h-100" alt="<?= $item['name'] ?>" style="object-fit: cover;">
                                                    </div>
                                                    <div class="col-8">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><?= $item['name'] ?></h5>
                                                            <p class="card-text small text-muted"><?= substr($item['description'], 0, 50) ?>...</p>
                                                            <p class="card-text fw-bold">Rp<?= number_format((float)$item['price'] * 15000, 0, ',', '.') ?></p>
                                                            <p class="card-text">
                                                                <span class="badge <?= isset($item['is_available']) && $item['is_available'] ? 'bg-success' : 'bg-danger' ?>">
                                                                    <?= isset($item['is_available']) && $item['is_available'] ? 'Tersedia' : 'Tidak Tersedia' ?>
                                                                </span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h4>Belum ada pesanan</h4>
                                <p class="text-muted">Restoran ini belum menerima pesanan.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Pelanggan</th>
                                            <th>Pengemudi</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['user_name'] ?></td>
                                                <td><?= $order['driver_name'] ?? 'Belum ditugaskan' ?></td>
                                                <td>Rp<?= number_format((float)$order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>
                                                    <span class="badge <?php
                                                        $orderStatus = isset($order['order_status']) ? $order['order_status'] : '';
                                                        switch ($orderStatus) {
                                                            case 'pending': echo 'bg-warning text-dark'; break;
                                                            case 'confirmed': echo 'bg-info text-dark'; break;
                                                            case 'preparing': echo 'bg-primary'; break;
                                                            case 'ready_for_pickup': echo 'bg-primary'; break;
                                                            case 'picked_up': echo 'bg-info'; break;
                                                            case 'on_the_way': echo 'bg-info'; break;
                                                            case 'delivered': echo 'bg-success'; break;
                                                            case 'cancelled': echo 'bg-danger'; break;
                                                            default: echo 'bg-secondary';
                                                        }
                                                    ?>">
                                                        <?php
                                                            switch ($orderStatus) {
                                                                case 'pending': echo 'Menunggu'; break;
                                                                case 'confirmed': echo 'Dikonfirmasi'; break;
                                                                case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                                                case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                                                case 'picked_up': echo 'Diambil'; break;
                                                                case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                                                case 'delivered': echo 'Terkirim'; break;
                                                                case 'cancelled': echo 'Dibatalkan'; break;
                                                                default: echo $orderStatus;
                                                            }
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?= isset($order['created_at']) ? date('d/m/Y H:i', strtotime($order['created_at'])) : '-' ?></td>
                                                <td>
                                                    <a href="view_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> Detail
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if ($orderCount > 10): ?>
                                <div class="text-center mt-3">
                                    <a href="orders.php?restaurant_id=<?= $restaurant_id ?>" class="btn btn-outline-primary">
                                        Lihat Semua Pesanan
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
