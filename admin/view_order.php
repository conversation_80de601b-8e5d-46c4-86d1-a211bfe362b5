<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: orders.php');
    exit;
}

$order_id = $_GET['id'];

// Get order details
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address, r.phone as restaurant_phone,
           u.name as user_name, u.email as user_email, u.phone as user_phone, u.address as user_address,
           d.name as driver_name, d.email as driver_email, d.phone as driver_phone, d.vehicle_type
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    WHERE o.order_id = ?
");
$stmt->execute([$order_id]);

if ($stmt->rowCount() === 0) {
    header('Location: orders.php');
    exit;
}

$order = $stmt->fetch();

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, mi.name, mi.price
    FROM order_items oi
    JOIN menu_items mi ON oi.menu_item_id = mi.menu_item_id
    WHERE oi.order_id = ?
");
$stmt->execute([$order_id]);
$orderItems = $stmt->fetchAll();

// Handle order status update
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_status') {
        $newStatus = $_POST['status'] ?? '';
        
        if (empty($newStatus)) {
            $error = 'Silakan pilih status baru';
        } else {
            $stmt = $conn->prepare("UPDATE orders SET order_status = ? WHERE order_id = ?");
            
            if ($stmt->execute([$newStatus, $order_id])) {
                $success = 'Status pesanan berhasil diperbarui';
                
                // Refresh order data
                $stmt = $conn->prepare("
                    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address, r.phone as restaurant_phone,
                           u.name as user_name, u.email as user_email, u.phone as user_phone, u.address as user_address,
                           d.name as driver_name, d.email as driver_email, d.phone as driver_phone, d.vehicle_type
                    FROM orders o
                    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                    JOIN users u ON o.user_id = u.user_id
                    LEFT JOIN drivers d ON o.driver_id = d.driver_id
                    WHERE o.order_id = ?
                ");
                $stmt->execute([$order_id]);
                $order = $stmt->fetch();
            } else {
                $error = 'Gagal memperbarui status pesanan';
            }
        }
    } elseif ($action === 'assign_driver') {
        $driverId = $_POST['driver_id'] ?? '';
        
        if (empty($driverId)) {
            $error = 'Silakan pilih pengemudi';
        } else {
            $stmt = $conn->prepare("UPDATE orders SET driver_id = ? WHERE order_id = ?");
            
            if ($stmt->execute([$driverId, $order_id])) {
                $success = 'Pengemudi berhasil ditugaskan';
                
                // Refresh order data
                $stmt = $conn->prepare("
                    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address, r.phone as restaurant_phone,
                           u.name as user_name, u.email as user_email, u.phone as user_phone, u.address as user_address,
                           d.name as driver_name, d.email as driver_email, d.phone as driver_phone, d.vehicle_type
                    FROM orders o
                    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                    JOIN users u ON o.user_id = u.user_id
                    LEFT JOIN drivers d ON o.driver_id = d.driver_id
                    WHERE o.order_id = ?
                ");
                $stmt->execute([$order_id]);
                $order = $stmt->fetch();
            } else {
                $error = 'Gagal menugaskan pengemudi';
            }
        }
    }
}

// Get available drivers
$stmt = $conn->prepare("SELECT driver_id, name, phone, vehicle_type FROM drivers WHERE status = 'active'");
$stmt->execute();
$availableDrivers = $stmt->fetchAll();

// Order status options
$orderStatuses = [
    'pending' => 'Menunggu',
    'confirmed' => 'Dikonfirmasi',
    'preparing' => 'Sedang Dipersiapkan',
    'ready_for_pickup' => 'Siap Diambil',
    'picked_up' => 'Diambil',
    'on_the_way' => 'Dalam Perjalanan',
    'delivered' => 'Terkirim',
    'cancelled' => 'Dibatalkan'
];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Pesanan #<?= $order_id ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Admin</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Order Details Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Detail Pesanan #<?= $order_id ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="orders.php">Pesanan</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Detail Pesanan #<?= $order_id ?></li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="orders.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar Pesanan
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <!-- Order Items -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Item Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th class="text-center">Jumlah</th>
                                        <th class="text-end">Harga</th>
                                        <th class="text-end">Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orderItems as $item): ?>
                                        <tr>
                                            <td><?= $item['name'] ?></td>
                                            <td class="text-center"><?= $item['quantity'] ?></td>
                                            <td class="text-end">Rp<?= number_format($item['price'] * 15000, 0, ',', '.') ?></td>
                                            <td class="text-end">Rp<?= number_format($item['price'] * $item['quantity'] * 15000, 0, ',', '.') ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="3" class="text-end fw-bold">Subtotal</td>
                                        <td class="text-end">Rp<?= number_format($order['subtotal'] * 15000, 0, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="text-end fw-bold">Biaya Pengiriman</td>
                                        <td class="text-end">Rp<?= number_format($order['delivery_fee'] * 15000, 0, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="text-end fw-bold">Total</td>
                                        <td class="text-end fw-bold">Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Status Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-4">
                            <div class="text-center">
                                <div class="rounded-circle bg-<?= in_array($order['order_status'], ['pending', 'confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way', 'delivered']) ? 'success' : 'secondary' ?> text-white d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="mb-0">Pesanan Dibuat</p>
                                <small class="text-muted"><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></small>
                            </div>
                            <div class="text-center">
                                <div class="rounded-circle bg-<?= in_array($order['order_status'], ['confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way', 'delivered']) ? 'success' : 'secondary' ?> text-white d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="mb-0">Dikonfirmasi</p>
                            </div>
                            <div class="text-center">
                                <div class="rounded-circle bg-<?= in_array($order['order_status'], ['preparing', 'ready_for_pickup', 'picked_up', 'on_the_way', 'delivered']) ? 'success' : 'secondary' ?> text-white d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="mb-0">Diproses</p>
                            </div>
                            <div class="text-center">
                                <div class="rounded-circle bg-<?= in_array($order['order_status'], ['picked_up', 'on_the_way', 'delivered']) ? 'success' : 'secondary' ?> text-white d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="mb-0">Dikirim</p>
                            </div>
                            <div class="text-center">
                                <div class="rounded-circle bg-<?= $order['order_status'] === 'delivered' ? 'success' : 'secondary' ?> text-white d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="mb-0">Diterima</p>
                            </div>
                        </div>

                        <div class="alert alert-<?php
                            switch ($order['order_status']) {
                                case 'pending': echo 'warning'; break;
                                case 'confirmed': case 'preparing': case 'ready_for_pickup': echo 'info'; break;
                                case 'picked_up': case 'on_the_way': echo 'primary'; break;
                                case 'delivered': echo 'success'; break;
                                case 'cancelled': echo 'danger'; break;
                                default: echo 'secondary';
                            }
                        ?>">
                            <h5 class="alert-heading">
                                Status Saat Ini: 
                                <span class="badge bg-<?php
                                    switch ($order['order_status']) {
                                        case 'pending': echo 'warning text-dark'; break;
                                        case 'confirmed': echo 'info text-dark'; break;
                                        case 'preparing': echo 'primary'; break;
                                        case 'ready_for_pickup': echo 'primary'; break;
                                        case 'picked_up': echo 'info'; break;
                                        case 'on_the_way': echo 'info'; break;
                                        case 'delivered': echo 'success'; break;
                                        case 'cancelled': echo 'danger'; break;
                                        default: echo 'secondary';
                                    }
                                ?>">
                                    <?php
                                        switch ($order['order_status']) {
                                            case 'pending': echo 'Menunggu'; break;
                                            case 'confirmed': echo 'Dikonfirmasi'; break;
                                            case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                            case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                            case 'picked_up': echo 'Diambil'; break;
                                            case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                            case 'delivered': echo 'Terkirim'; break;
                                            case 'cancelled': echo 'Dibatalkan'; break;
                                            default: echo $order['order_status'];
                                        }
                                    ?>
                                </span>
                            </h5>
                            <p class="mb-0">
                                <?php
                                    switch ($order['order_status']) {
                                        case 'pending': echo 'Pesanan sedang menunggu konfirmasi dari restoran.'; break;
                                        case 'confirmed': echo 'Pesanan telah dikonfirmasi oleh restoran.'; break;
                                        case 'preparing': echo 'Restoran sedang menyiapkan pesanan.'; break;
                                        case 'ready_for_pickup': echo 'Pesanan siap untuk diambil oleh pengemudi.'; break;
                                        case 'picked_up': echo 'Pesanan telah diambil oleh pengemudi.'; break;
                                        case 'on_the_way': echo 'Pengemudi sedang dalam perjalanan mengantar pesanan.'; break;
                                        case 'delivered': echo 'Pesanan telah diterima oleh pelanggan.'; break;
                                        case 'cancelled': echo 'Pesanan telah dibatalkan.'; break;
                                        default: echo 'Status pesanan tidak diketahui.';
                                    }
                                ?>
                            </p>
                        </div>

                        <?php if ($order['order_status'] !== 'delivered' && $order['order_status'] !== 'cancelled'): ?>
                            <form action="view_order.php?id=<?= $order_id ?>" method="post" class="mt-3">
                                <input type="hidden" name="action" value="update_status">
                                <div class="row g-2">
                                    <div class="col-md-8">
                                        <select name="status" class="form-select">
                                            <option value="">Pilih Status Baru</option>
                                            <?php foreach ($orderStatuses as $key => $value): ?>
                                                <?php if ($key !== $order['order_status']): ?>
                                                    <option value="<?= $key ?>"><?= $value ?></option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary w-100">Perbarui Status</button>
                                    </div>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Order Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>ID Pesanan:</strong> #<?= $order_id ?></p>
                        <p><strong>Tanggal Pesanan:</strong> <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                        <p><strong>Metode Pembayaran:</strong> <?= $order['payment_method'] ?></p>
                        <p><strong>Catatan:</strong> <?= $order['notes'] ?: 'Tidak ada' ?></p>
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pelanggan</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Nama:</strong> <?= $order['user_name'] ?></p>
                        <p><strong>Email:</strong> <?= $order['user_email'] ?></p>
                        <p><strong>Telepon:</strong> <?= $order['user_phone'] ?></p>
                        <p><strong>Alamat Pengiriman:</strong> <?= $order['delivery_address'] ?: $order['user_address'] ?></p>
                    </div>
                </div>

                <!-- Restaurant Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Restoran</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Nama:</strong> <?= $order['restaurant_name'] ?></p>
                        <p><strong>Telepon:</strong> <?= $order['restaurant_phone'] ?></p>
                        <p><strong>Alamat:</strong> <?= $order['restaurant_address'] ?></p>
                    </div>
                </div>

                <!-- Driver Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pengemudi</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($order['driver_id']): ?>
                            <p><strong>Nama:</strong> <?= $order['driver_name'] ?></p>
                            <p><strong>Email:</strong> <?= $order['driver_email'] ?></p>
                            <p><strong>Telepon:</strong> <?= $order['driver_phone'] ?></p>
                            <p><strong>Kendaraan:</strong> 
                                <?php
                                    switch ($order['vehicle_type']) {
                                        case 'motorcycle': echo 'Motor'; break;
                                        case 'car': echo 'Mobil'; break;
                                        case 'bicycle': echo 'Sepeda'; break;
                                        default: echo $order['vehicle_type'];
                                    }
                                ?>
                            </p>
                        <?php else: ?>
                            <p class="text-muted">Belum ada pengemudi yang ditugaskan</p>
                            
                            <?php if ($order['order_status'] !== 'delivered' && $order['order_status'] !== 'cancelled'): ?>
                                <form action="view_order.php?id=<?= $order_id ?>" method="post" class="mt-3">
                                    <input type="hidden" name="action" value="assign_driver">
                                    <div class="mb-3">
                                        <label for="driver_id" class="form-label">Pilih Pengemudi</label>
                                        <select name="driver_id" id="driver_id" class="form-select" required>
                                            <option value="">Pilih Pengemudi</option>
                                            <?php foreach ($availableDrivers as $driver): ?>
                                                <option value="<?= $driver['driver_id'] ?>">
                                                    <?= $driver['name'] ?> (<?= $driver['phone'] ?>) - 
                                                    <?php
                                                        switch ($driver['vehicle_type']) {
                                                            case 'motorcycle': echo 'Motor'; break;
                                                            case 'car': echo 'Mobil'; break;
                                                            case 'bicycle': echo 'Sepeda'; break;
                                                            default: echo $driver['vehicle_type'];
                                                        }
                                                    ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">Tugaskan Pengemudi</button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
