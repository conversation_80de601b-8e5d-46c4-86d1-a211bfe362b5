<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'includes/two_factor_auth.php';

// Check if user is logged in
$isLoggedIn = false;
$userId = null;
$userType = null;
$userEmail = null;
$userPhone = null;
$userName = null;
$twoFactorEnabled = false;
$twoFactorMethod = null;

if (isset($_SESSION['user_id'])) {
    $isLoggedIn = true;
    $userId = $_SESSION['user_id'];
    $userType = 'user';
} elseif (isset($_SESSION['driver_id'])) {
    $isLoggedIn = true;
    $userId = $_SESSION['driver_id'];
    $userType = 'driver';
} elseif (isset($_SESSION['owner_id'])) {
    $isLoggedIn = true;
    $userId = $_SESSION['owner_id'];
    $userType = 'owner';
} elseif (isset($_SESSION['admin_id'])) {
    $isLoggedIn = true;
    $userId = $_SESSION['admin_id'];
    $userType = 'admin';
}

if (!$isLoggedIn) {
    header('Location: login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get user information
$tableName = '';
$idColumn = '';

switch ($userType) {
    case 'user':
        $tableName = 'users';
        $idColumn = 'user_id';
        break;
    case 'driver':
        $tableName = 'drivers';
        $idColumn = 'driver_id';
        break;
    case 'owner':
        $tableName = 'restaurant_owners';
        $idColumn = 'owner_id';
        break;
    case 'admin':
        $tableName = 'admins';
        $idColumn = 'admin_id';
        break;
}

$stmt = $conn->prepare("SELECT * FROM $tableName WHERE $idColumn = :user_id");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    header('Location: logout.php');
    exit;
}

$userEmail = $user['email'];
$userPhone = $user['phone'] ?? '';
$userName = $user['name'];
$twoFactorEnabled = $user['two_factor_enabled'] ?? 0;
$twoFactorMethod = $user['two_factor_method'] ?? '';

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['enable_2fa'])) {
        $method = $_POST['method'] ?? 'email';

        // Validate method
        if ($method === 'email' && empty($userEmail)) {
            $error = 'Anda harus memiliki alamat email yang terdaftar untuk menggunakan metode ini.';
        } elseif ($method === 'sms' && empty($userPhone)) {
            $error = 'Anda harus memiliki nomor telepon yang terdaftar untuk menggunakan metode ini.';
        } else {
            // Generate OTP
            $otp = generateOTP();

            // Store OTP in database
            if (storeOTP($conn, $userId, $otp, $userType)) {
                // Send OTP
                $otpSent = false;

                if ($method === 'email') {
                    $otpSent = sendOTPEmail($userEmail, $otp, $userName);
                } elseif ($method === 'sms') {
                    $otpSent = sendOTPSMS($userPhone, $otp);
                }

                if ($otpSent) {
                    // Store method in session for verification
                    $_SESSION['2fa_setup'] = [
                        'method' => $method,
                        'step' => 'verify'
                    ];

                    // Redirect to verification page
                    header('Location: two_factor_setup.php');
                    exit;
                } else {
                    $error = 'Gagal mengirim kode verifikasi. Silakan coba lagi.';
                }
            } else {
                $error = 'Terjadi kesalahan saat menyimpan kode verifikasi. Silakan coba lagi.';
            }
        }
    } elseif (isset($_POST['verify_otp'])) {
        $otp = $_POST['otp'] ?? '';

        if (empty($otp)) {
            $error = 'Kode verifikasi tidak boleh kosong.';
        } else {
            // Verify OTP
            if (verifyOTP($conn, $userId, $otp, $userType)) {
                // Enable 2FA
                $method = $_SESSION['2fa_setup']['method'] ?? 'email';

                if (enable2FA($conn, $userId, $method, $userType)) {
                    // Clear setup session
                    unset($_SESSION['2fa_setup']);

                    // Log security event
                    logSecurityEvent('2fa_enabled', 'info', ['method' => $method]);

                    $success = 'Autentikasi dua faktor berhasil diaktifkan!';

                    // Refresh user data
                    $stmt = $conn->prepare("SELECT * FROM $tableName WHERE $idColumn = :user_id");
                    $stmt->bindParam(':user_id', $userId);
                    $stmt->execute();
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    $twoFactorEnabled = $user['two_factor_enabled'] ?? 0;
                    $twoFactorMethod = $user['two_factor_method'] ?? '';
                } else {
                    $error = 'Terjadi kesalahan saat mengaktifkan autentikasi dua faktor. Silakan coba lagi.';
                }
            } else {
                $error = 'Kode verifikasi tidak valid atau sudah kedaluwarsa. Silakan coba lagi.';
            }
        }
    } elseif (isset($_POST['disable_2fa'])) {
        // Disable 2FA
        if (disable2FA($conn, $userId, $userType)) {
            // Log security event
            logSecurityEvent('2fa_disabled', 'warning', []);

            $success = 'Autentikasi dua faktor berhasil dinonaktifkan.';

            // Refresh user data
            $stmt = $conn->prepare("SELECT * FROM $tableName WHERE $idColumn = :user_id");
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            $twoFactorEnabled = $user['two_factor_enabled'] ?? 0;
            $twoFactorMethod = $user['two_factor_method'] ?? '';
        } else {
            $error = 'Terjadi kesalahan saat menonaktifkan autentikasi dua faktor. Silakan coba lagi.';
        }
    }
}

// Determine current step
$currentStep = 'setup';
if (isset($_SESSION['2fa_setup']) && $_SESSION['2fa_setup']['step'] === 'verify') {
    $currentStep = 'verify';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Autentikasi Dua Faktor - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        .otp-input {
            letter-spacing: 0.5em;
            text-align: center;
            font-size: 1.5em;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <?php if ($userType === 'user'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="user/dashboard.php">Dasbor</a>
                        </li>
                    <?php elseif ($userType === 'driver'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="driver/dashboard.php">Dasbor Pengemudi</a>
                        </li>
                    <?php elseif ($userType === 'owner'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="owner/dashboard.php">Dasbor Restoran</a>
                        </li>
                    <?php elseif ($userType === 'admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="admin/dashboard.php">Dasbor Admin</a>
                        </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $userName ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php if ($userType === 'user'): ?>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                            <?php elseif ($userType === 'driver'): ?>
                                <li><a class="dropdown-item" href="driver/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="driver/orders.php">Pesanan</a></li>
                                <li><a class="dropdown-item" href="driver/earnings.php">Penghasilan</a></li>
                            <?php elseif ($userType === 'owner'): ?>
                                <li><a class="dropdown-item" href="owner/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="owner/restaurants.php">Restoran</a></li>
                            <?php elseif ($userType === 'admin'): ?>
                                <li><a class="dropdown-item" href="admin/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                            <?php endif; ?>
                            <li><a class="dropdown-item active" href="two_factor_setup.php">Keamanan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Pengaturan Autentikasi Dua Faktor</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                        <?php if ($userType === 'user'): ?>
                            <li class="breadcrumb-item"><a href="user/dashboard.php">Dasbor</a></li>
                        <?php elseif ($userType === 'driver'): ?>
                            <li class="breadcrumb-item"><a href="driver/dashboard.php">Dasbor Pengemudi</a></li>
                        <?php elseif ($userType === 'owner'): ?>
                            <li class="breadcrumb-item"><a href="owner/dashboard.php">Dasbor Restoran</a></li>
                        <?php elseif ($userType === 'admin'): ?>
                            <li class="breadcrumb-item"><a href="admin/dashboard.php">Dasbor Admin</a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page">Keamanan</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Autentikasi Dua Faktor</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($twoFactorEnabled): ?>
                            <div class="alert alert-success">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-shield-alt fa-2x me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">Autentikasi Dua Faktor Aktif</h5>
                                        <p class="mb-0">Akun Anda dilindungi dengan autentikasi dua faktor melalui <?= $twoFactorMethod === 'email' ? 'Email' : 'SMS' ?>.</p>
                                    </div>
                                </div>
                            </div>

                            <p>Dengan autentikasi dua faktor, Anda akan diminta untuk memasukkan kode verifikasi setiap kali login. Ini memberikan lapisan keamanan tambahan untuk akun Anda.</p>

                            <form action="two_factor_setup.php" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menonaktifkan autentikasi dua faktor? Ini akan mengurangi keamanan akun Anda.');">
                                <input type="hidden" name="disable_2fa" value="1">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-shield-alt me-2"></i>Nonaktifkan Autentikasi Dua Faktor
                                </button>
                            </form>
                        <?php elseif ($currentStep === 'verify'): ?>
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">Verifikasi Kode</h5>
                                        <p class="mb-0">
                                            Kami telah mengirimkan kode verifikasi ke
                                            <?php if ($_SESSION['2fa_setup']['method'] === 'email'): ?>
                                                alamat email Anda (<?= substr($userEmail, 0, 3) . '***' . substr($userEmail, strpos($userEmail, '@')) ?>).
                                            <?php else: ?>
                                                nomor telepon Anda (<?= substr($userPhone, 0, 3) . '***' . substr($userPhone, -3) ?>).
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <form action="two_factor_setup.php" method="post">
                                <div class="mb-3">
                                    <label for="otp" class="form-label">Kode Verifikasi</label>
                                    <input type="text" class="form-control otp-input" id="otp" name="otp" maxlength="6" placeholder="Masukkan kode 6 digit" required>
                                    <div class="form-text">Kode verifikasi berlaku selama 5 menit.</div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-check me-2"></i>Verifikasi
                                    </button>
                                    <a href="two_factor_setup.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Kembali
                                    </a>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">Autentikasi Dua Faktor Tidak Aktif</h5>
                                        <p class="mb-0">Akun Anda tidak dilindungi dengan autentikasi dua faktor. Aktifkan sekarang untuk meningkatkan keamanan akun Anda.</p>
                                    </div>
                                </div>
                            </div>

                            <p>Dengan autentikasi dua faktor, Anda akan diminta untuk memasukkan kode verifikasi setiap kali login. Ini memberikan lapisan keamanan tambahan untuk akun Anda.</p>

                            <form action="two_factor_setup.php" method="post">
                                <input type="hidden" name="enable_2fa" value="1">

                                <div class="mb-3">
                                    <label for="method" class="form-label">Metode Verifikasi</label>
                                    <select class="form-select" id="method" name="method" required>
                                        <option value="email" <?= empty($userEmail) ? 'disabled' : '' ?>>Email (<?= empty($userEmail) ? 'Tidak tersedia' : $userEmail ?>)</option>
                                        <option value="sms" <?= empty($userPhone) ? 'disabled' : '' ?>>SMS (<?= empty($userPhone) ? 'Tidak tersedia' : $userPhone ?>)</option>
                                    </select>
                                    <div class="form-text">Pilih metode yang ingin Anda gunakan untuk menerima kode verifikasi.</div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-shield-alt me-2"></i>Aktifkan Autentikasi Dua Faktor
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Keamanan</h5>
                    </div>
                    <div class="card-body">
                        <h6><i class="fas fa-shield-alt me-2"></i>Autentikasi Dua Faktor</h6>
                        <p class="small">Autentikasi dua faktor menambahkan lapisan keamanan tambahan untuk akun Anda dengan meminta kode verifikasi setiap kali Anda login.</p>

                        <h6><i class="fas fa-key me-2"></i>Kata Sandi Kuat</h6>
                        <p class="small">Gunakan kata sandi yang kuat dengan kombinasi huruf, angka, dan simbol. Jangan gunakan kata sandi yang sama untuk beberapa akun.</p>

                        <h6><i class="fas fa-user-shield me-2"></i>Jaga Kerahasiaan</h6>
                        <p class="small">Jangan pernah membagikan kata sandi atau kode verifikasi Anda kepada siapa pun, termasuk staf KikaZen Ship.</p>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Aktivitas Login Terakhir</h5>
                    </div>
                    <div class="card-body">
                        <p class="small">
                            <i class="fas fa-clock me-2"></i>
                            <?= $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Tidak ada data' ?>
                        </p>
                        <p class="small">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?= $_SERVER['REMOTE_ADDR'] ?? 'Tidak diketahui' ?>
                        </p>
                        <p class="small">
                            <i class="fas fa-desktop me-2"></i>
                            <?= $_SERVER['HTTP_USER_AGENT'] ?? 'Tidak diketahui' ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Format OTP input
        document.addEventListener('DOMContentLoaded', function() {
            const otpInput = document.getElementById('otp');
            if (otpInput) {
                otpInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
            }
        });
    </script>
</body>
</html>
