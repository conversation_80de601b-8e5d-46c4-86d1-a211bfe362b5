-- Promotions table for Kika<PERSON><PERSON> Ship
USE kikazen_ship;

-- Promotions table
CREATE TABLE IF NOT EXISTS promotions (
    promotion_id INT AUTO_INCREMENT PRIMARY KEY,
    restaurant_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    discount_type ENUM('percentage', 'fixed_amount') NOT NULL,
    discount_value DECIMAL(10, 2) NOT NULL,
    min_order_amount DECIMAL(10, 2) DEFAULT 0.00,
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    usage_limit INT DEFAULT NULL,
    usage_count INT DEFAULT 0,
    promo_code VARCHAR(20) UNIQUE,
    applies_to ENUM('all_items', 'specific_items', 'specific_categories') DEFAULT 'all_items',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id) ON DELETE CASCADE
);

-- Promotion items (for specific item promotions)
CREATE TABLE IF NOT EXISTS promotion_items (
    promotion_item_id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_id INT NOT NULL,
    item_id INT NOT NULL,
    FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES menu_items(item_id) ON DELETE CASCADE
);

-- Promotion categories (for specific category promotions)
CREATE TABLE IF NOT EXISTS promotion_categories (
    promotion_category_id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_id INT NOT NULL,
    category_id INT NOT NULL,
    FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE
);

-- Add promotion_id to order table
ALTER TABLE orders ADD COLUMN promotion_id INT NULL;
ALTER TABLE orders ADD FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id) ON DELETE SET NULL;

-- Add discount_amount to orders table
ALTER TABLE orders ADD COLUMN discount_amount DECIMAL(10, 2) DEFAULT 0.00 AFTER delivery_fee;
