-- Sustainability and Social Responsibility Improvements for KikaZen Ship
USE kikazen_ship;

-- Add sustainability columns to orders table
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS no_utensils TINYINT(1) DEFAULT 0 COMMENT 'Whether the customer opted out of disposable utensils',
ADD COLUMN IF NOT EXISTS eco_packaging TINYINT(1) DEFAULT 0 COMMENT 'Whether the order uses eco-friendly packaging',
ADD COLUMN IF NOT EXISTS carbon_offset TINYINT(1) DEFAULT 0 COMMENT 'Whether the customer purchased carbon offset',
ADD COLUMN IF NOT EXISTS carbon_offset_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Amount paid for carbon offset in IDR',
ADD COLUMN IF NOT EXISTS estimated_emissions DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Estimated CO2 emissions in kg';

-- Add sustainability columns to restaurants table
ALTER TABLE restaurants
ADD COLUMN IF NOT EXISTS is_local TINYINT(1) DEFAULT 0 COMMENT 'Whether the restaurant is locally owned',
ADD COLUMN IF NOT EXISTS uses_eco_packaging TINYINT(1) DEFAULT 0 COMMENT 'Whether the restaurant uses eco-friendly packaging',
ADD COLUMN IF NOT EXISTS sustainability_rating INT DEFAULT 0 COMMENT 'Sustainability rating from 0-5',
ADD COLUMN IF NOT EXISTS sustainability_description TEXT COMMENT 'Description of sustainability practices';

-- Add sustainability columns to drivers table
ALTER TABLE drivers
ADD COLUMN IF NOT EXISTS eco_vehicle_type VARCHAR(50) DEFAULT NULL COMMENT 'Type of eco-friendly vehicle (bicycle, electric, etc.)',
ADD COLUMN IF NOT EXISTS is_eco_friendly TINYINT(1) DEFAULT 0 COMMENT 'Whether the driver uses eco-friendly transportation';

-- Create donations table
CREATE TABLE IF NOT EXISTS donations (
    donation_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    order_id INT,
    amount DECIMAL(10,2) NOT NULL,
    donation_type VARCHAR(50) NOT NULL COMMENT 'food_bank, tree_planting, etc.',
    recipient_organization VARCHAR(100),
    is_anonymous TINYINT(1) DEFAULT 0,
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE SET NULL
);

-- Create community events table
CREATE TABLE IF NOT EXISTS community_events (
    event_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME,
    location VARCHAR(255),
    max_participants INT,
    current_participants INT DEFAULT 0,
    is_virtual TINYINT(1) DEFAULT 0,
    virtual_link VARCHAR(255),
    event_type VARCHAR(50) COMMENT 'cleanup, food_drive, workshop, etc.',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create event participants table
CREATE TABLE IF NOT EXISTS event_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attendance_status VARCHAR(20) DEFAULT 'registered' COMMENT 'registered, attended, cancelled',
    FOREIGN KEY (event_id) REFERENCES community_events(event_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY (event_id, user_id)
);

-- Create sustainability badges table
CREATE TABLE IF NOT EXISTS sustainability_badges (
    badge_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    badge_type VARCHAR(20) COMMENT 'restaurant, driver, user',
    criteria TEXT COMMENT 'Criteria for earning the badge',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user badges table
CREATE TABLE IF NOT EXISTS user_badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    badge_id INT NOT NULL,
    earned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES sustainability_badges(badge_id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, badge_id)
);

-- Create restaurant badges table
CREATE TABLE IF NOT EXISTS restaurant_badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    restaurant_id INT NOT NULL,
    badge_id INT NOT NULL,
    earned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES sustainability_badges(badge_id) ON DELETE CASCADE,
    UNIQUE KEY (restaurant_id, badge_id)
);

-- Create driver badges table
CREATE TABLE IF NOT EXISTS driver_badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    driver_id INT NOT NULL,
    badge_id INT NOT NULL,
    earned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (driver_id) REFERENCES drivers(driver_id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES sustainability_badges(badge_id) ON DELETE CASCADE,
    UNIQUE KEY (driver_id, badge_id)
);

-- Create sustainability tips table
CREATE TABLE IF NOT EXISTS sustainability_tips (
    tip_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) COMMENT 'food_waste, packaging, transportation, etc.',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sustainability newsletter subscribers table
CREATE TABLE IF NOT EXISTS sustainability_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL,
    name VARCHAR(100),
    user_id INT,
    is_active TINYINT(1) DEFAULT 1,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY (email)
);

-- Create food waste tracking table
CREATE TABLE IF NOT EXISTS food_waste_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    restaurant_id INT NOT NULL,
    waste_date DATE NOT NULL,
    waste_amount DECIMAL(10,2) COMMENT 'Amount in kg',
    waste_type VARCHAR(50) COMMENT 'prepared_food, raw_ingredients, etc.',
    action_taken VARCHAR(50) COMMENT 'composted, donated, discarded',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id) ON DELETE CASCADE
);

-- Create carbon offset projects table
CREATE TABLE IF NOT EXISTS carbon_offset_projects (
    project_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    project_type VARCHAR(50) COMMENT 'reforestation, renewable_energy, etc.',
    cost_per_kg DECIMAL(10,2) COMMENT 'Cost per kg of CO2 offset in IDR',
    total_funded DECIMAL(10,2) DEFAULT 0.00,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default sustainability badges
INSERT INTO sustainability_badges (name, description, icon, badge_type, criteria) VALUES
('Eco-Friendly Packaging', 'Restoran ini menggunakan kemasan ramah lingkungan', 'leaf', 'restaurant', 'Menggunakan kemasan yang dapat terurai secara hayati atau dapat didaur ulang'),
('Zero Waste Champion', 'Restoran ini berkomitmen untuk mengurangi limbah makanan', 'recycle', 'restaurant', 'Memiliki program pengurangan limbah makanan dan daur ulang'),
('Local Hero', 'Restoran ini dimiliki dan dioperasikan secara lokal', 'store', 'restaurant', 'Dimiliki oleh penduduk lokal dan menggunakan bahan-bahan lokal'),
('Green Rider', 'Pengemudi ini menggunakan transportasi ramah lingkungan', 'bicycle', 'driver', 'Menggunakan sepeda atau kendaraan listrik untuk pengiriman'),
('Carbon Neutral', 'Pengguna ini telah mengimbangi jejak karbon dari semua pesanannya', 'tree', 'user', 'Membeli offset karbon untuk semua pesanan'),
('No Utensils Hero', 'Pengguna ini selalu menolak alat makan sekali pakai', 'utensils-slash', 'user', 'Menolak alat makan sekali pakai dalam 10 pesanan berturut-turut');

-- Insert default sustainability tips
INSERT INTO sustainability_tips (title, content, category) VALUES
('Tolak Alat Makan Sekali Pakai', 'Gunakan alat makan yang dapat digunakan kembali di rumah Anda untuk mengurangi limbah plastik. Setiap set alat makan plastik yang tidak digunakan menghemat sekitar 20 gram plastik.', 'packaging'),
('Pilih Restoran Lokal', 'Dengan memesan dari restoran lokal, Anda mendukung ekonomi lokal dan mengurangi jejak karbon dari transportasi makanan jarak jauh.', 'transportation'),
('Kompos Sisa Makanan', 'Alih-alih membuang sisa makanan, pertimbangkan untuk mengomposnya. Ini mengurangi limbah yang berakhir di tempat pembuangan sampah dan menciptakan tanah yang kaya nutrisi.', 'food_waste'),
('Bagikan Porsi Besar', 'Jika porsi terlalu besar, bagikan dengan teman atau simpan sisa untuk dimakan nanti. Ini membantu mengurangi limbah makanan dan menghemat uang.', 'food_waste'),
('Pilih Pengiriman Ramah Lingkungan', 'Saat tersedia, pilih opsi pengiriman dengan sepeda atau kendaraan listrik untuk mengurangi emisi karbon dari pesanan Anda.', 'transportation');

-- Insert default carbon offset projects
INSERT INTO carbon_offset_projects (name, description, location, project_type, cost_per_kg, is_active) VALUES
('Reforestasi Kalimantan', 'Proyek penanaman kembali hutan hujan di Kalimantan yang telah rusak akibat penebangan dan kebakaran hutan.', 'Kalimantan, Indonesia', 'reforestation', 2000.00, 1),
('Energi Surya Jawa Timur', 'Instalasi panel surya di daerah pedesaan Jawa Timur untuk menggantikan generator diesel.', 'Jawa Timur, Indonesia', 'renewable_energy', 1500.00, 1),
('Konservasi Mangrove Bali', 'Perlindungan dan penanaman kembali hutan mangrove di pesisir Bali untuk menyerap karbon dan melindungi garis pantai.', 'Bali, Indonesia', 'conservation', 1800.00, 1);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_orders_no_utensils ON orders(no_utensils);
CREATE INDEX IF NOT EXISTS idx_orders_eco_packaging ON orders(eco_packaging);
CREATE INDEX IF NOT EXISTS idx_orders_carbon_offset ON orders(carbon_offset);
CREATE INDEX IF NOT EXISTS idx_restaurants_is_local ON restaurants(is_local);
CREATE INDEX IF NOT EXISTS idx_restaurants_uses_eco_packaging ON restaurants(uses_eco_packaging);
CREATE INDEX IF NOT EXISTS idx_restaurants_sustainability_rating ON restaurants(sustainability_rating);
CREATE INDEX IF NOT EXISTS idx_drivers_is_eco_friendly ON drivers(is_eco_friendly);
CREATE INDEX IF NOT EXISTS idx_donations_user_id ON donations(user_id);
CREATE INDEX IF NOT EXISTS idx_donations_order_id ON donations(order_id);
CREATE INDEX IF NOT EXISTS idx_donations_donation_type ON donations(donation_type);
CREATE INDEX IF NOT EXISTS idx_community_events_event_date ON community_events(event_date);
CREATE INDEX IF NOT EXISTS idx_community_events_event_type ON community_events(event_type);
CREATE INDEX IF NOT EXISTS idx_event_participants_event_id ON event_participants(event_id);
CREATE INDEX IF NOT EXISTS idx_event_participants_user_id ON event_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_sustainability_badges_badge_type ON sustainability_badges(badge_type);
CREATE INDEX IF NOT EXISTS idx_user_badges_user_id ON user_badges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_badges_badge_id ON user_badges(badge_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_badges_restaurant_id ON restaurant_badges(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_badges_badge_id ON restaurant_badges(badge_id);
CREATE INDEX IF NOT EXISTS idx_driver_badges_driver_id ON driver_badges(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_badges_badge_id ON driver_badges(badge_id);
CREATE INDEX IF NOT EXISTS idx_sustainability_tips_category ON sustainability_tips(category);
CREATE INDEX IF NOT EXISTS idx_sustainability_subscribers_email ON sustainability_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_sustainability_subscribers_user_id ON sustainability_subscribers(user_id);
CREATE INDEX IF NOT EXISTS idx_food_waste_tracking_restaurant_id ON food_waste_tracking(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_food_waste_tracking_waste_date ON food_waste_tracking(waste_date);
CREATE INDEX IF NOT EXISTS idx_carbon_offset_projects_is_active ON carbon_offset_projects(is_active);
CREATE INDEX IF NOT EXISTS idx_carbon_offset_projects_project_type ON carbon_offset_projects(project_type);
