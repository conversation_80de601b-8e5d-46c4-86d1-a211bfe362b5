-- Insert default addresses for users
USE kikazen_ship;

-- Create a temporary table to store user IDs and addresses
CREATE TEMPORARY TABLE temp_user_addresses AS
SELECT user_id, address FROM users WHERE address IS NOT NULL AND address != '';

-- Insert default address for each user
INSERT INTO user_addresses (user_id, address_label, address, latitude, longitude, is_default)
SELECT 
    u.user_id, 
    'Rumah', 
    u.address, 
    -6.2088, -- Default latitude (Jakarta)
    106.8456, -- Default longitude (Jakarta)
    1 -- is_default
FROM temp_user_addresses u
WHERE NOT EXISTS (
    SELECT 1 FROM user_addresses ua WHERE ua.user_id = u.user_id
);

-- Drop temporary table
DROP TEMPORARY TABLE temp_user_addresses;
