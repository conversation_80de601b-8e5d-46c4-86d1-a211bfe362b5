<?php
// <PERSON><PERSON><PERSON> to insert default address for users

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get all users
$stmt = $conn->query("SELECT user_id, address FROM users WHERE address IS NOT NULL AND address != ''");
$users = $stmt->fetchAll();

// Insert default address for each user
foreach ($users as $user) {
    // Check if user already has an address
    $checkStmt = $conn->prepare("SELECT COUNT(*) FROM user_addresses WHERE user_id = ?");
    $checkStmt->execute([$user['user_id']]);
    $hasAddress = $checkStmt->fetchColumn() > 0;
    
    if (!$hasAddress && !empty($user['address'])) {
        // Insert default address
        $insertStmt = $conn->prepare("
            INSERT INTO user_addresses (
                user_id, address_label, address, latitude, longitude, is_default
            ) VALUES (
                ?, 'Rumah', ?, -6.2088, 106.8456, 1
            )
        ");
        $insertStmt->execute([$user['user_id'], $user['address']]);
        
        echo "Added default address for user ID: " . $user['user_id'] . "<br>";
    } else {
        echo "User ID: " . $user['user_id'] . " already has an address or no address in profile<br>";
    }
}

echo "Done!";
?>
