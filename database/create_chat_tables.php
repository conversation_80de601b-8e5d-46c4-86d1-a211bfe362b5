<?php
/**
 * Create Chat Tables
 * This script creates the necessary tables for the chat feature
 */

// Include database connection
require_once '../config/database.php';

// Connect to database
$conn = connectDB();

// Read SQL file
$sql = file_get_contents('chat_tables.sql');

// Execute SQL
try {
    // Execute each statement separately
    $statements = explode(';', $sql);
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $conn->exec($statement);
        }
    }
    echo "Chat tables created successfully\n";

    // Insert sample data for testing
    // Create a support chat room
    $conn->exec("INSERT INTO chat_rooms (room_type, status) VALUES ('support', 'active')");
    $supportRoomId = $conn->lastInsertId();

    // Add admin as participant
    $conn->exec("INSERT INTO chat_participants (room_id, user_type, user_id) VALUES ($supportRoomId, 'admin', 1)");

    // Add system welcome message
    $welcomeMessage = "Selamat datang di dukungan pelanggan KikaZen Ship. Silakan jelaskan masalah Anda dan tim kami akan segera membantu.";
    $conn->exec("INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type) VALUES ($supportRoomId, 'system', 0, '$welcomeMessage', 'system')");

    echo "Sample data inserted successfully\n";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
