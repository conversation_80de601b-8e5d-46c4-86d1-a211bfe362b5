-- Create categories table if not exists
CREATE TABLE IF NOT EXISTS `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default categories if not exists
INSERT IGNORE INTO `categories` (`category_id`, `name`, `description`) VALUES
(1, '<PERSON><PERSON><PERSON>', '<PERSON>u makanan utama'),
(2, '<PERSON><PERSON><PERSON>', '<PERSON>u makanan ringan dan camilan'),
(3, 'Minuman', '<PERSON>u minuman'),
(4, 'Dessert', '<PERSON>u pencuci mulut dan makanan manis'),
(5, 'Paket Hemat', '<PERSON>u paket hemat');

-- Create menu_items table if not exists
CREATE TABLE IF NOT EXISTS `menu_items` (
  `item_id` int(11) NOT NULL AUTO_INCREMENT,
  `restaurant_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `image_url` varchar(255) DEFAULT NULL,
  `is_available` tinyint(1) NOT NULL DEFAULT 1,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`item_id`),
  KEY `restaurant_id` (`restaurant_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `menu_items_ibfk_1` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`restaurant_id`) ON DELETE CASCADE,
  CONSTRAINT `menu_items_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add columns to restaurants table if not exists
ALTER TABLE `restaurants` 
ADD COLUMN IF NOT EXISTS `logo_url` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `banner_url` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `min_order_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS `delivery_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS `opening_time` time DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `closing_time` time DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `is_open` tinyint(1) NOT NULL DEFAULT 1;
