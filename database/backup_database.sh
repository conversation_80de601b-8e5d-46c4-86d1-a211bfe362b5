#!/bin/bash

# Script untuk membuat backup database kikazen_ship
# Gunakan: ./backup_database.sh

# Variabel
DB_NAME="kikazen_ship"
BACKUP_DIR="database/backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}_${DATE}.sql"

# Buat direktori backup jika belum ada
mkdir -p $BACKUP_DIR

# Buat backup database
echo "Membuat backup database ${DB_NAME}..."
/opt/lampp/bin/mysqldump -u root $DB_NAME > $BACKUP_FILE

# Kompres file backup
echo "Mengompres file backup..."
gzip $BACKUP_FILE

echo "Backup selesai: ${BACKUP_FILE}.gz"
echo "Lokasi: $(pwd)/${BACKUP_FILE}.gz"
