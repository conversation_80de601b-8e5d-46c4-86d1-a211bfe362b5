-- Sustainability and Social Responsibility Tables for KikaZen Ship
USE kikazen_ship;

-- Add eco-friendly options to orders table
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS eco_packaging TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS no_cutlery TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS carbon_offset TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS carbon_offset_amount DECIMAL(10,2) DEFAULT 0.00;

-- Create sustainability initiatives table
CREATE TABLE IF NOT EXISTS sustainability_initiatives (
    initiative_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    subtitle VARCHAR(255),
    description TEXT NOT NULL,
    content TEXT,
    image_url VARCHAR(255),
    action_url VARCHAR(255),
    action_text VARCHAR(100),
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create initiative updates table
CREATE TABLE IF NOT EXISTS initiative_updates (
    update_id INT AUTO_INCREMENT PRIMARY KEY,
    initiative_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    update_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (initiative_id) REFERENCES sustainability_initiatives(initiative_id) ON DELETE CASCADE
);

-- Create initiative impact table
CREATE TABLE IF NOT EXISTS initiative_impact (
    impact_id INT AUTO_INCREMENT PRIMARY KEY,
    initiative_id INT NOT NULL,
    metric1_value VARCHAR(50),
    metric1_label VARCHAR(100),
    metric2_value VARCHAR(50),
    metric2_label VARCHAR(100),
    metric3_value VARCHAR(50),
    metric3_label VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (initiative_id) REFERENCES sustainability_initiatives(initiative_id) ON DELETE CASCADE
);

-- Create sustainability tags table
CREATE TABLE IF NOT EXISTS sustainability_tags (
    tag_id INT AUTO_INCREMENT PRIMARY KEY,
    tag_name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create restaurant sustainability tags table
CREATE TABLE IF NOT EXISTS restaurant_sustainability_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    restaurant_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES sustainability_tags(tag_id) ON DELETE CASCADE,
    UNIQUE KEY (restaurant_id, tag_id)
);

-- Create restaurant initiatives table
CREATE TABLE IF NOT EXISTS restaurant_initiatives (
    id INT AUTO_INCREMENT PRIMARY KEY,
    restaurant_id INT NOT NULL,
    initiative_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id) ON DELETE CASCADE,
    FOREIGN KEY (initiative_id) REFERENCES sustainability_initiatives(initiative_id) ON DELETE CASCADE,
    UNIQUE KEY (restaurant_id, initiative_id)
);

-- Create donation programs table
CREATE TABLE IF NOT EXISTS donation_programs (
    program_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    subtitle VARCHAR(255),
    description TEXT NOT NULL,
    image_url VARCHAR(255),
    target_amount DECIMAL(12,2) NOT NULL,
    current_amount DECIMAL(12,2) DEFAULT 0.00,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create donation tiers table
CREATE TABLE IF NOT EXISTS donation_tiers (
    tier_id INT AUTO_INCREMENT PRIMARY KEY,
    program_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (program_id) REFERENCES donation_programs(program_id) ON DELETE CASCADE
);

-- Create donations table
CREATE TABLE IF NOT EXISTS donations (
    donation_id INT AUTO_INCREMENT PRIMARY KEY,
    program_id INT NOT NULL,
    user_id INT,
    donor_name VARCHAR(100),
    amount DECIMAL(10,2) NOT NULL,
    message TEXT,
    is_anonymous TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (program_id) REFERENCES donation_programs(program_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create donation impact table
CREATE TABLE IF NOT EXISTS donation_impact (
    impact_id INT AUTO_INCREMENT PRIMARY KEY,
    program_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (program_id) REFERENCES donation_programs(program_id) ON DELETE CASCADE
);

-- Create sustainability tips table
CREATE TABLE IF NOT EXISTS sustainability_tips (
    tip_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    user_id INT,
    is_approved TINYINT(1) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create carbon footprint table
CREATE TABLE IF NOT EXISTS carbon_footprint (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    delivery_distance DECIMAL(10,2) NOT NULL, -- in kilometers
    vehicle_type VARCHAR(50) NOT NULL,
    carbon_emission DECIMAL(10,2) NOT NULL, -- in kg CO2
    offset_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
);

-- Insert sample sustainability tags
INSERT INTO sustainability_tags (tag_name, description, icon) VALUES
('Kemasan Ramah Lingkungan', 'Restoran ini menggunakan kemasan yang ramah lingkungan dan dapat terurai secara alami.', 'fas fa-box'),
('Bahan Lokal', 'Restoran ini menggunakan bahan-bahan dari petani dan produsen lokal.', 'fas fa-map-marker-alt'),
('Hemat Energi', 'Restoran ini menerapkan praktik hemat energi dalam operasionalnya.', 'fas fa-bolt'),
('Zero Waste', 'Restoran ini berkomitmen untuk mengurangi limbah hingga mendekati nol.', 'fas fa-recycle'),
('Makanan Organik', 'Restoran ini menggunakan bahan-bahan organik dalam menu mereka.', 'fas fa-seedling'),
('Donasi Makanan', 'Restoran ini mendonasikan makanan yang tidak terjual kepada yang membutuhkan.', 'fas fa-hand-holding-heart');

-- Insert sample sustainability initiatives
INSERT INTO sustainability_initiatives (title, subtitle, description, content, image_url, is_active) VALUES
('Program Kemasan Ramah Lingkungan', 'Mengurangi limbah plastik dengan kemasan yang dapat terurai', 
'KikaZen Ship berkomitmen untuk mengurangi limbah plastik dengan menggunakan kemasan yang ramah lingkungan dan dapat terurai secara alami.',
'<p>Limbah plastik adalah salah satu masalah lingkungan terbesar yang kita hadapi saat ini. Setiap tahun, jutaan ton plastik berakhir di lautan kita, membahayakan kehidupan laut dan ekosistem.</p>
<p>Melalui program Kemasan Ramah Lingkungan, KikaZen Ship berkomitmen untuk mengurangi dampak lingkungan dari operasi pengiriman makanan kami dengan:</p>
<ul>
<li>Menggunakan kemasan yang terbuat dari bahan yang dapat terurai secara alami</li>
<li>Bekerja sama dengan restoran mitra untuk mengurangi penggunaan plastik sekali pakai</li>
<li>Memberikan opsi kepada pelanggan untuk menolak alat makan sekali pakai</li>
<li>Mendorong penggunaan kembali tas dan wadah makanan</li>
</ul>
<p>Sejak diluncurkan pada tahun 2022, program ini telah berhasil mengurangi penggunaan plastik sekali pakai sebanyak 50% dalam operasi KikaZen Ship.</p>',
'images/sustainability/eco-packaging.jpg', 1),

('Program Donasi Makanan', 'Mengurangi limbah makanan dan membantu yang membutuhkan', 
'KikaZen Ship bekerja sama dengan restoran mitra untuk mendonasikan makanan yang tidak terjual kepada yang membutuhkan, mengurangi limbah makanan sekaligus membantu komunitas.',
'<p>Limbah makanan adalah masalah global yang serius. Sepertiga dari semua makanan yang diproduksi di dunia terbuang sia-sia, sementara jutaan orang kelaparan setiap hari.</p>
<p>Program Donasi Makanan KikaZen Ship bertujuan untuk mengatasi masalah ini dengan:</p>
<ul>
<li>Bekerja sama dengan restoran mitra untuk mengidentifikasi makanan yang masih layak konsumsi tetapi tidak terjual</li>
<li>Mengumpulkan dan mendistribusikan makanan tersebut ke panti asuhan, panti jompo, dan komunitas yang membutuhkan</li>
<li>Mengedukasi restoran dan pelanggan tentang pentingnya mengurangi limbah makanan</li>
</ul>
<p>Program ini telah berhasil mendistribusikan lebih dari 10.000 porsi makanan kepada yang membutuhkan sejak diluncurkan.</p>',
'images/sustainability/food-donation.jpg', 1),

('Inisiatif Pengurangan Jejak Karbon', 'Mengurangi dampak lingkungan dari pengiriman makanan', 
'KikaZen Ship berkomitmen untuk mengurangi jejak karbon dari operasi pengiriman makanan dengan menggunakan kendaraan ramah lingkungan dan mengimplementasikan rute pengiriman yang efisien.',
'<p>Transportasi adalah salah satu kontributor utama emisi gas rumah kaca. Sebagai layanan pengiriman makanan, KikaZen Ship menyadari tanggung jawabnya untuk mengurangi dampak lingkungan dari operasinya.</p>
<p>Inisiatif Pengurangan Jejak Karbon kami meliputi:</p>
<ul>
<li>Mendorong penggunaan sepeda dan kendaraan listrik untuk pengiriman</li>
<li>Mengimplementasikan algoritma optimasi rute untuk mengurangi jarak tempuh</li>
<li>Menawarkan opsi carbon offset kepada pelanggan</li>
<li>Berinvestasi dalam proyek energi terbarukan untuk mengimbangi emisi yang tidak dapat dihindari</li>
</ul>
<p>Melalui inisiatif ini, KikaZen Ship bertujuan untuk mengurangi emisi karbon dari operasinya sebesar 30% pada tahun 2025.</p>',
'images/sustainability/carbon-footprint.jpg', 1);

-- Insert sample initiative impact
INSERT INTO initiative_impact (initiative_id, metric1_value, metric1_label, metric2_value, metric2_label, metric3_value, metric3_label) VALUES
(1, '500,000+', 'Kemasan Plastik Dihemat', '5,000 kg', 'Limbah Plastik Dikurangi', '50%', 'Pengurangan Plastik Sekali Pakai'),
(2, '10,000+', 'Porsi Makanan Didonasikan', '100+', 'Restoran Berpartisipasi', '2,500 kg', 'Limbah Makanan Dikurangi'),
(3, '30%', 'Target Pengurangan Emisi', '500+', 'Pengemudi Sepeda & Kendaraan Listrik', '10,000 kg', 'CO2 Diimbangi');

-- Insert sample donation programs
INSERT INTO donation_programs (title, subtitle, description, image_url, target_amount, current_amount, start_date, end_date, is_active) VALUES
('Bantu Anak-anak Kurang Mampu', 'Sediakan makanan bergizi untuk anak-anak yang membutuhkan', 
'Program ini bertujuan untuk menyediakan makanan bergizi bagi anak-anak kurang mampu di daerah perkotaan. Setiap donasi akan digunakan untuk membeli dan mendistribusikan makanan bergizi kepada anak-anak yang membutuhkan.',
'images/donation/children-food.jpg', 50000000, 25000000, '2023-01-01', '2023-12-31', 1),

('Dukung Petani Lokal', 'Bantu petani lokal terdampak perubahan iklim', 
'Program ini bertujuan untuk membantu petani lokal yang terdampak perubahan iklim. Dana yang terkumpul akan digunakan untuk memberikan pelatihan, alat, dan benih kepada petani lokal agar mereka dapat beradaptasi dengan perubahan iklim.',
'images/donation/local-farmers.jpg', 75000000, 30000000, '2023-03-01', '2023-12-31', 1);

-- Insert sample donation tiers
INSERT INTO donation_tiers (program_id, amount, description) VALUES
(1, 50000, 'Menyediakan 5 porsi makanan bergizi untuk anak-anak yang membutuhkan'),
(1, 100000, 'Menyediakan 10 porsi makanan bergizi untuk anak-anak yang membutuhkan'),
(1, 500000, 'Menyediakan 50 porsi makanan bergizi untuk anak-anak yang membutuhkan'),
(1, 1000000, 'Menyediakan 100 porsi makanan bergizi untuk anak-anak yang membutuhkan'),
(2, 100000, 'Membantu 1 petani lokal dengan pelatihan dan benih'),
(2, 250000, 'Membantu 3 petani lokal dengan pelatihan dan benih'),
(2, 500000, 'Membantu 5 petani lokal dengan pelatihan dan benih'),
(2, 1000000, 'Membantu 10 petani lokal dengan pelatihan dan benih');

-- Insert sample donation impact
INSERT INTO donation_impact (program_id, title, description, icon) VALUES
(1, 'Makanan Bergizi', 'Setiap Rp 10.000 menyediakan 1 porsi makanan bergizi untuk anak-anak yang membutuhkan', 'fas fa-utensils'),
(1, 'Kesehatan Anak', 'Makanan bergizi membantu anak-anak tumbuh sehat dan fokus pada pendidikan mereka', 'fas fa-heartbeat'),
(1, 'Komunitas yang Lebih Kuat', 'Membantu anak-anak hari ini berarti membangun komunitas yang lebih kuat untuk masa depan', 'fas fa-users'),
(2, 'Mendukung Petani Lokal', 'Dana Anda membantu petani lokal beradaptasi dengan perubahan iklim', 'fas fa-seedling'),
(2, 'Ketahanan Pangan', 'Mendukung petani lokal berarti mendukung ketahanan pangan untuk komunitas kita', 'fas fa-wheat-awn'),
(2, 'Ekonomi Lokal', 'Petani yang sukses berkontribusi pada ekonomi lokal yang kuat', 'fas fa-hand-holding-dollar');

-- Insert sample sustainability tips
INSERT INTO sustainability_tips (title, content, is_approved, is_active) VALUES
('Bawa Tas Belanja Sendiri', 'Kurangi penggunaan kantong plastik dengan membawa tas belanja sendiri saat memesan makanan untuk dibawa pulang.', 1, 1),
('Pilih Restoran Lokal', 'Dukung restoran lokal untuk mengurangi jejak karbon dari transportasi makanan jarak jauh.', 1, 1),
('Tolak Alat Makan Sekali Pakai', 'Gunakan opsi "Tanpa Alat Makan" saat memesan makanan jika Anda makan di rumah dan sudah memiliki alat makan sendiri.', 1, 1),
('Kompos Sisa Makanan', 'Kompos sisa makanan Anda untuk mengurangi limbah yang berakhir di tempat pembuangan sampah.', 1, 1),
('Pilih Menu Nabati', 'Makanan nabati umumnya memiliki jejak karbon yang lebih rendah dibandingkan makanan hewani.', 1, 1);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sustainability_initiatives_is_active ON sustainability_initiatives(is_active);
CREATE INDEX IF NOT EXISTS idx_initiative_updates_initiative_id ON initiative_updates(initiative_id);
CREATE INDEX IF NOT EXISTS idx_initiative_impact_initiative_id ON initiative_impact(initiative_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_sustainability_tags_restaurant_id ON restaurant_sustainability_tags(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_sustainability_tags_tag_id ON restaurant_sustainability_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_initiatives_restaurant_id ON restaurant_initiatives(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_initiatives_initiative_id ON restaurant_initiatives(initiative_id);
CREATE INDEX IF NOT EXISTS idx_donation_programs_is_active ON donation_programs(is_active);
CREATE INDEX IF NOT EXISTS idx_donation_tiers_program_id ON donation_tiers(program_id);
CREATE INDEX IF NOT EXISTS idx_donations_program_id ON donations(program_id);
CREATE INDEX IF NOT EXISTS idx_donations_user_id ON donations(user_id);
CREATE INDEX IF NOT EXISTS idx_donation_impact_program_id ON donation_impact(program_id);
CREATE INDEX IF NOT EXISTS idx_sustainability_tips_is_active ON sustainability_tips(is_active);
CREATE INDEX IF NOT EXISTS idx_carbon_footprint_order_id ON carbon_footprint(order_id);
