# Praktik Terbaik Database KikaZen Ship

Dokumen ini berisi praktik terbaik untuk pengembangan dan pengelolaan database KikaZen Ship.

## Prinsip Umum

1. **Periksa Struktur yang Ada Terlebih Dahulu**
   - Sebelum membuat tabel baru, periksa apakah sudah ada tabel yang dapat digunakan untuk fitur yang ingin diimplementasikan.
   - Gunakan `SHOW TABLES` untuk melihat semua tabel yang ada.
   - Gunakan `DESCRIBE [nama_tabel]` untuk melihat struktur tabel.

2. **Dokumentasikan Perubahan**
   - Dokumentasikan setiap perubahan pada struktur database di file `DATABASE_STRUCTURE.md`.
   - Buat file SQL terpisah untuk setiap perubahan besar pada struktur database.

3. **Gunakan Versioning**
   - Beri nama file SQL dengan format yang jelas, misalnya `v1.2.3_add_feature_x.sql`.
   - <PERSON><PERSON> men<PERSON> file SQL yang sudah ada, buat file baru untuk perubahan baru.

4. **Backup Secara Teratur**
   - <PERSON><PERSON><PERSON> script `backup_database.sh` untuk membuat backup database secara teratur.
   - Simpan backup di lokasi yang aman.

## Desain Tabel

1. **Normalisasi**
   - Ikuti prinsip normalisasi database untuk menghindari redundansi data.
   - Gunakan foreign key untuk menjaga integritas referensial.

2. **Penamaan**
   - Gunakan nama tabel dalam bentuk jamak (contoh: `users`, bukan `user`).
   - Gunakan nama kolom yang deskriptif dan konsisten.
   - Gunakan underscore untuk memisahkan kata dalam nama tabel dan kolom.

3. **Tipe Data**
   - Pilih tipe data yang sesuai untuk setiap kolom.
   - Gunakan `INT` untuk ID dan angka bulat.
   - Gunakan `DECIMAL` untuk nilai uang dan angka desimal.
   - Gunakan `VARCHAR` untuk teks dengan panjang tetap.
   - Gunakan `TEXT` untuk teks dengan panjang variabel.
   - Gunakan `TIMESTAMP` untuk tanggal dan waktu.
   - Gunakan `ENUM` untuk nilai yang terbatas.

4. **Indeks**
   - Buat indeks pada kolom yang sering digunakan dalam query `WHERE`, `JOIN`, dan `ORDER BY`.
   - Buat indeks pada foreign key.
   - Hindari membuat terlalu banyak indeks karena dapat memperlambat operasi `INSERT` dan `UPDATE`.

5. **Constraints**
   - Gunakan `PRIMARY KEY` untuk kolom ID.
   - Gunakan `FOREIGN KEY` untuk menjaga integritas referensial.
   - Gunakan `UNIQUE` untuk kolom yang harus unik.
   - Gunakan `NOT NULL` untuk kolom yang tidak boleh kosong.
   - Gunakan `DEFAULT` untuk memberikan nilai default pada kolom.

## Pengembangan Fitur

1. **Pendekatan Database-First**
   - Desain struktur database terlebih dahulu sebelum mengimplementasikan fitur.
   - Pastikan struktur database mendukung semua kebutuhan fitur.

2. **Penggunaan Tabel yang Ada**
   - Periksa apakah fitur baru dapat menggunakan tabel yang sudah ada.
   - Jika perlu, tambahkan kolom baru ke tabel yang sudah ada daripada membuat tabel baru.

3. **Migrasi Data**
   - Jika perlu mengubah struktur tabel yang sudah ada, buat script migrasi untuk memindahkan data.
   - Uji script migrasi di lingkungan pengembangan sebelum menjalankannya di lingkungan produksi.

## Keamanan

1. **Backup**
   - Buat backup database secara teratur.
   - Simpan backup di lokasi yang aman.

2. **Akses**
   - Batasi akses ke database hanya untuk pengguna yang membutuhkan.
   - Gunakan password yang kuat untuk akses database.

3. **Validasi Input**
   - Validasi semua input pengguna sebelum menyimpannya ke database.
   - Gunakan prepared statements untuk mencegah SQL injection.

## Performa

1. **Indeks**
   - Gunakan indeks untuk meningkatkan performa query.
   - Monitor performa query dan tambahkan indeks jika diperlukan.

2. **Query**
   - Tulis query yang efisien.
   - Hindari menggunakan `SELECT *` kecuali jika memang membutuhkan semua kolom.
   - Gunakan `EXPLAIN` untuk menganalisis performa query.

3. **Caching**
   - Gunakan caching untuk data yang sering diakses dan jarang berubah.
   - Gunakan tabel `cache` yang sudah ada untuk menyimpan data cache.

## Pemeliharaan

1. **Monitoring**
   - Monitor ukuran database dan pertumbuhannya.
   - Monitor performa query dan identifikasi query yang lambat.

2. **Optimasi**
   - Jalankan `OPTIMIZE TABLE` secara berkala untuk tabel yang sering diupdate.
   - Jalankan `ANALYZE TABLE` untuk memperbarui statistik tabel.

3. **Pembersihan**
   - Hapus data yang tidak diperlukan secara berkala.
   - Arsipkan data lama yang masih perlu disimpan.

## Contoh Penggunaan

### Memeriksa Tabel yang Ada

```sql
SHOW TABLES;
```

### Memeriksa Struktur Tabel

```sql
DESCRIBE users;
```

### Menambahkan Kolom Baru

```sql
ALTER TABLE users
ADD COLUMN last_login TIMESTAMP NULL;
```

### Membuat Indeks

```sql
CREATE INDEX idx_users_email ON users(email);
```

### Menggunakan Foreign Key

```sql
ALTER TABLE orders
ADD CONSTRAINT fk_orders_users
FOREIGN KEY (user_id) REFERENCES users(user_id)
ON DELETE CASCADE;
```

### Menggunakan Prepared Statements (PHP)

```php
$stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
$stmt->execute([$email]);
$user = $stmt->fetch();
```
