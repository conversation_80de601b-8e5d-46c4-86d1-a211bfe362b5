-- Chat Tables for KikaZen Ship
-- This file contains SQL statements to create tables for the chat feature

-- Table: chat_rooms
-- Stores information about chat rooms
CREATE TABLE IF NOT EXISTS `chat_rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `room_type` enum('order','support','complaint') NOT NULL DEFAULT 'order',
  `status` enum('active','closed') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`room_id`),
  KEY `order_id` (`order_id`),
  CONSTRAINT `chat_rooms_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: chat_participants
-- Stores information about participants in chat rooms
CREATE TABLE IF NOT EXISTS `chat_participants` (
  `participant_id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `user_type` enum('customer','restaurant_owner','driver','admin') NOT NULL,
  `user_id` int(11) NOT NULL,
  `last_read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`participant_id`),
  UNIQUE KEY `room_user` (`room_id`,`user_type`,`user_id`),
  KEY `room_id` (`room_id`),
  CONSTRAINT `chat_participants_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `chat_rooms` (`room_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: chat_messages
-- Stores chat messages
CREATE TABLE IF NOT EXISTS `chat_messages` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `sender_type` enum('customer','restaurant_owner','driver','admin','system') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message_text` text NOT NULL,
  `message_type` enum('text','image','location','system') NOT NULL DEFAULT 'text',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`message_id`),
  KEY `room_id` (`room_id`),
  CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `chat_rooms` (`room_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add indexes for better performance
ALTER TABLE `chat_messages` ADD INDEX `idx_sender` (`sender_type`, `sender_id`);
ALTER TABLE `chat_messages` ADD INDEX `idx_created_at` (`created_at`);
ALTER TABLE `chat_messages` ADD INDEX `idx_is_read` (`is_read`);

ALTER TABLE `chat_participants` ADD INDEX `idx_user` (`user_type`, `user_id`);
ALTER TABLE `chat_participants` ADD INDEX `idx_last_read` (`last_read_at`);

ALTER TABLE `chat_rooms` ADD INDEX `idx_room_type` (`room_type`);
ALTER TABLE `chat_rooms` ADD INDEX `idx_status` (`status`);
ALTER TABLE `chat_rooms` ADD INDEX `idx_updated_at` (`updated_at`);
