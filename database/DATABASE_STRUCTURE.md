# Struktur Database KikaZen Ship

Dokumen ini berisi informasi tentang struktur database KikaZen Ship, termasuk tabel-tabel yang ada, hubungan antar tabel, dan tujuan masing-masing tabel.

## Tabel Utama

### 1. Pengguna dan Autentikasi

- **users**: Menyimpan data pengguna (pelanggan)
- **drivers**: Menyimpan data pengemudi/kurir
- **restaurant_owners**: Menyimpan data pemilik restoran
- **admins**: Menyimpan data administrator
- **user_addresses**: Menyimpan alamat-alamat pengguna

### 2. Restoran dan Menu

- **restaurants**: Menyimpan data restoran
- **categories**: Kategori makanan
- **menu_items**: Item menu dari restoran

### 3. Pesanan

- **orders**: Menyimpan data pesanan
- **order_items**: Item yang dipesan dalam suatu pesanan
- **order_tracking**: Pelacakan status pesanan

### 4. Ulasan dan <PERSON>

- **reviews**: Ulasan dan penilaian untuk restoran dan pengemudi
- **review_photos**: Foto yang dilampirkan pada ulasan
- **review_helpful**: Penanda ulasan yang membantu
- **review_reports**: Laporan ulasan yang tidak pantas

## Fitur Keberlanjutan dan Tanggung Jawab Sosial

- **sustainability_initiatives**: Inisiatif keberlanjutan
- **initiative_updates**: Update untuk inisiatif keberlanjutan
- **initiative_impact**: Dampak dari inisiatif keberlanjutan
- **sustainability_tags**: Tag keberlanjutan untuk restoran
- **restaurant_sustainability_tags**: Hubungan antara restoran dan tag keberlanjutan
- **restaurant_initiatives**: Hubungan antara restoran dan inisiatif keberlanjutan
- **donation_programs**: Program donasi
- **donation_tiers**: Tingkatan donasi
- **donations**: Donasi yang dilakukan
- **donation_impact**: Dampak dari program donasi
- **sustainability_tips**: Tips keberlanjutan
- **carbon_footprint**: Jejak karbon dari pesanan
- **sustainability_badges**: Badge keberlanjutan
- **sustainability_subscribers**: Pelanggan yang berlangganan inisiatif keberlanjutan

## Fitur Chat dan Komunikasi

- **chat_rooms**: Ruang chat
- **chat_participants**: Peserta dalam ruang chat
- **chat_messages**: Pesan dalam ruang chat
- **unread_message_counts**: Jumlah pesan yang belum dibaca

## Fitur Keluhan dan Dukungan

- **complaints**: Keluhan dari pengguna
- **complaint_attachments**: Lampiran untuk keluhan
- **complaint_updates**: Update untuk keluhan
- **active_complaints_view**: View untuk keluhan yang aktif

## Fitur Promosi dan Diskon

- **promotions**: Promosi dan diskon
- **promotion_items**: Item yang termasuk dalam promosi
- **promotion_categories**: Kategori yang termasuk dalam promosi

## Fitur Komunitas

- **community_events**: Acara komunitas
- **event_participants**: Peserta acara komunitas

## Fitur Preferensi dan Personalisasi

- **preferences**: Preferensi umum
- **user_preferences**: Preferensi pengguna
- **user_cuisine_preferences**: Preferensi masakan pengguna
- **favorites**: Restoran favorit pengguna
- **user_search_history**: Riwayat pencarian pengguna
- **user_view_history**: Riwayat tampilan pengguna

## Fitur Keamanan dan Logging

- **security_logs**: Log keamanan
- **user_activity_log**: Log aktivitas pengguna
- **sessions**: Sesi pengguna
- **api_keys**: Kunci API
- **otp_codes**: Kode OTP untuk verifikasi

## Fitur Badge dan Penghargaan

- **user_badges**: Badge pengguna
- **driver_badges**: Badge pengemudi
- **restaurant_badges**: Badge restoran

## Fitur Pelacakan Limbah Makanan

- **food_waste_tracking**: Pelacakan limbah makanan

## Hubungan Antar Tabel

- **users** -> **orders**: Satu pengguna dapat memiliki banyak pesanan
- **restaurants** -> **menu_items**: Satu restoran dapat memiliki banyak item menu
- **orders** -> **order_items**: Satu pesanan dapat memiliki banyak item
- **users** -> **reviews**: Satu pengguna dapat memberikan banyak ulasan
- **restaurants** -> **reviews**: Satu restoran dapat memiliki banyak ulasan
- **drivers** -> **reviews**: Satu pengemudi dapat memiliki banyak ulasan

## Catatan Penting

1. Sebelum membuat tabel baru, periksa apakah sudah ada tabel yang dapat digunakan untuk fitur yang ingin diimplementasikan.
2. Gunakan foreign key untuk menjaga integritas referensial antar tabel.
3. Pastikan untuk membuat indeks pada kolom yang sering digunakan dalam query untuk meningkatkan performa.
4. Dokumentasikan perubahan pada struktur database di dokumen ini.

## File SQL

Berikut adalah file-file SQL yang digunakan untuk membuat dan memperbarui struktur database:

- **schema.sql**: Skema dasar database
- **sustainability_tables.sql**: Tabel untuk fitur keberlanjutan
- **chat_tables.sql**: Tabel untuk fitur chat
- **promotions.sql**: Tabel untuk fitur promosi
- **user_addresses.sql**: Tabel untuk alamat pengguna
- **add_subtotal_column.sql**: Menambahkan kolom subtotal ke tabel order_items
