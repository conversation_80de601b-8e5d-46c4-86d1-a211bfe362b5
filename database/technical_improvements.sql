-- Technical improvements for KikaZen Ship
USE kikazen_ship;

-- Add 2FA columns to users table (if they don't exist)
ALTER TABLE users
ADD COLUMN IF NOT EXISTS two_factor_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS two_factor_method VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_login DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until DATETIME DEFAULT NULL;

-- Add 2FA columns to drivers table
ALTER TABLE drivers
ADD COLUMN IF NOT EXISTS two_factor_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS two_factor_method VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_login DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until DATETIME DEFAULT NULL;

-- Add 2FA columns to restaurant_owners table
ALTER TABLE restaurant_owners
ADD COLUMN IF NOT EXISTS two_factor_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS two_factor_method VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_login DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until DATETIME DEFAULT NULL;

-- Add 2FA columns to admins table
ALTER TABLE admins
ADD COLUMN IF NOT EXISTS two_factor_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS two_factor_method VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_login DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until DATETIME DEFAULT NULL;

-- Create OTP codes table
CREATE TABLE IF NOT EXISTS otp_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    user_type VARCHAR(10) NOT NULL, -- 'user', 'driver', 'owner', 'admin'
    otp VARCHAR(10) NOT NULL,
    expires_at DATETIME NOT NULL,
    attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create API keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    user_type VARCHAR(10) NOT NULL, -- 'user', 'driver', 'owner', 'admin'
    api_key VARCHAR(64) NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    last_used DATETIME DEFAULT NULL,
    expires_at DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (api_key)
);

-- Create security logs table
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    user_type VARCHAR(10) DEFAULT NULL, -- 'user', 'driver', 'owner', 'admin', 'guest'
    event_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'failed_login', '2fa', etc.
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sessions table for better session management
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT DEFAULT NULL,
    user_type VARCHAR(10) DEFAULT NULL, -- 'user', 'driver', 'owner', 'admin'
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    payload TEXT,
    last_activity INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create cache table for database query caching
CREATE TABLE IF NOT EXISTS cache (
    id VARCHAR(128) PRIMARY KEY,
    value MEDIUMTEXT NOT NULL,
    expiration INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for performance optimization
-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);

-- Drivers table indexes
CREATE INDEX IF NOT EXISTS idx_drivers_email ON drivers(email);
CREATE INDEX IF NOT EXISTS idx_drivers_phone ON drivers(phone);
CREATE INDEX IF NOT EXISTS idx_drivers_status ON drivers(status);
CREATE INDEX IF NOT EXISTS idx_drivers_location ON drivers(current_latitude, current_longitude);

-- Restaurant owners table indexes
CREATE INDEX IF NOT EXISTS idx_restaurant_owners_email ON restaurant_owners(email);
CREATE INDEX IF NOT EXISTS idx_restaurant_owners_phone ON restaurant_owners(phone);

-- Restaurants table indexes
CREATE INDEX IF NOT EXISTS idx_restaurants_owner_id ON restaurants(owner_id);
CREATE INDEX IF NOT EXISTS idx_restaurants_location ON restaurants(latitude, longitude);
-- Uncomment and modify the line below after confirming the correct column name
-- CREATE INDEX IF NOT EXISTS idx_restaurants_status ON restaurants(status); -- Change 'status' to the actual column name

-- Menu items table indexes
CREATE INDEX IF NOT EXISTS idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_menu_items_category_id ON menu_items(category_id);
CREATE INDEX IF NOT EXISTS idx_menu_items_status ON menu_items(is_available);

-- Orders table indexes
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_orders_driver_id ON orders(driver_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- Order items table indexes
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_item_id ON order_items(item_id);

-- Order tracking table indexes
CREATE INDEX IF NOT EXISTS idx_order_tracking_order_id ON order_tracking(order_id);
CREATE INDEX IF NOT EXISTS idx_order_tracking_status ON order_tracking(status);
CREATE INDEX IF NOT EXISTS idx_order_tracking_timestamp ON order_tracking(timestamp);

-- OTP codes table indexes
CREATE INDEX IF NOT EXISTS idx_otp_codes_user ON otp_codes(user_id, user_type);
CREATE INDEX IF NOT EXISTS idx_otp_codes_expires_at ON otp_codes(expires_at);

-- API keys table indexes
CREATE INDEX IF NOT EXISTS idx_api_keys_user ON api_keys(user_id, user_type);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);

-- Security logs table indexes
CREATE INDEX IF NOT EXISTS idx_security_logs_user ON security_logs(user_id, user_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);

-- Sessions table indexes
CREATE INDEX IF NOT EXISTS idx_sessions_user ON sessions(user_id, user_type);
CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON sessions(last_activity);

-- Cache table indexes
CREATE INDEX IF NOT EXISTS idx_cache_expiration ON cache(expiration);
