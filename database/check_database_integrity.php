<?php
/**
 * Script untuk memeriksa integritas database KikaZen Ship
 * 
 * Script ini memeriksa:
 * 1. Tabel-tabel yang ada
 * 2. Foreign key constraints
 * 3. Indeks
 * 4. Data yang tidak valid
 * 
 * Penggunaan: php check_database_integrity.php
 */

// Koneksi ke database
require_once '../config/database.php';
$conn = connectDB();

// Fungsi untuk mencetak header
function printHeader($text) {
    echo "\n";
    echo "=================================================================\n";
    echo " $text\n";
    echo "=================================================================\n";
}

// Fungsi untuk mencetak subheader
function printSubHeader($text) {
    echo "\n";
    echo "-----------------------------------------------------------------\n";
    echo " $text\n";
    echo "-----------------------------------------------------------------\n";
}

// Fungsi untuk mencetak pesan sukses
function printSuccess($text) {
    echo "✅ $text\n";
}

// Fungsi untuk mencetak pesan error
function printError($text) {
    echo "❌ $text\n";
}

// Fungsi untuk mencetak pesan warning
function printWarning($text) {
    echo "⚠️ $text\n";
}

// Fungsi untuk mencetak pesan info
function printInfo($text) {
    echo "ℹ️ $text\n";
}

// Fungsi untuk mendapatkan semua tabel
function getAllTables($conn) {
    $stmt = $conn->query("SHOW TABLES");
    $tables = [];
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    return $tables;
}

// Fungsi untuk mendapatkan struktur tabel
function getTableStructure($conn, $table) {
    $stmt = $conn->query("DESCRIBE `$table`");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk mendapatkan foreign key constraints
function getForeignKeys($conn, $table) {
    $stmt = $conn->query("
        SELECT 
            TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE
            REFERENCED_TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = '$table'
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk mendapatkan indeks
function getIndexes($conn, $table) {
    $stmt = $conn->query("SHOW INDEX FROM `$table`");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk memeriksa data yang tidak valid
function checkInvalidData($conn, $table, $column, $referencedTable, $referencedColumn) {
    $stmt = $conn->query("
        SELECT COUNT(*) as count
        FROM `$table` t
        LEFT JOIN `$referencedTable` rt ON t.`$column` = rt.`$referencedColumn`
        WHERE t.`$column` IS NOT NULL AND rt.`$referencedColumn` IS NULL
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['count'];
}

// Mulai pemeriksaan
printHeader("PEMERIKSAAN INTEGRITAS DATABASE KIKAZEN SHIP");
echo "Waktu: " . date('Y-m-d H:i:s') . "\n";

// Dapatkan semua tabel
$tables = getAllTables($conn);
$tableCount = count($tables);

printSubHeader("TABEL ($tableCount tabel ditemukan)");
foreach ($tables as $table) {
    printInfo($table);
}

// Periksa foreign key constraints
printSubHeader("FOREIGN KEY CONSTRAINTS");
$constraintIssues = 0;

foreach ($tables as $table) {
    $foreignKeys = getForeignKeys($conn, $table);
    if (count($foreignKeys) > 0) {
        foreach ($foreignKeys as $fk) {
            $referencedTable = $fk['REFERENCED_TABLE_NAME'];
            $referencedColumn = $fk['REFERENCED_COLUMN_NAME'];
            $column = $fk['COLUMN_NAME'];
            $constraintName = $fk['CONSTRAINT_NAME'];
            
            // Periksa apakah tabel yang direferensikan ada
            if (!in_array($referencedTable, $tables)) {
                printError("Tabel '$table' memiliki foreign key '$constraintName' yang mereferensikan tabel '$referencedTable' yang tidak ada");
                $constraintIssues++;
                continue;
            }
            
            // Periksa apakah kolom yang direferensikan ada
            $referencedTableStructure = getTableStructure($conn, $referencedTable);
            $referencedColumns = array_column($referencedTableStructure, 'Field');
            if (!in_array($referencedColumn, $referencedColumns)) {
                printError("Tabel '$table' memiliki foreign key '$constraintName' yang mereferensikan kolom '$referencedColumn' yang tidak ada di tabel '$referencedTable'");
                $constraintIssues++;
                continue;
            }
            
            // Periksa data yang tidak valid
            $invalidCount = checkInvalidData($conn, $table, $column, $referencedTable, $referencedColumn);
            if ($invalidCount > 0) {
                printWarning("Tabel '$table' memiliki $invalidCount baris dengan nilai '$column' yang tidak valid (tidak ada di '$referencedTable.$referencedColumn')");
                $constraintIssues++;
                continue;
            }
            
            printSuccess("Foreign key '$constraintName' pada tabel '$table' valid");
        }
    } else {
        printInfo("Tabel '$table' tidak memiliki foreign key constraints");
    }
}

if ($constraintIssues === 0) {
    printSuccess("Semua foreign key constraints valid");
} else {
    printError("Ditemukan $constraintIssues masalah dengan foreign key constraints");
}

// Periksa indeks
printSubHeader("INDEKS");
$indexIssues = 0;

foreach ($tables as $table) {
    $indexes = getIndexes($conn, $table);
    if (count($indexes) > 0) {
        $indexNames = array_unique(array_column($indexes, 'Key_name'));
        foreach ($indexNames as $indexName) {
            if ($indexName === 'PRIMARY') {
                printSuccess("Tabel '$table' memiliki primary key");
            } else {
                printInfo("Tabel '$table' memiliki indeks '$indexName'");
            }
        }
    } else {
        printWarning("Tabel '$table' tidak memiliki indeks");
        $indexIssues++;
    }
}

if ($indexIssues === 0) {
    printSuccess("Semua tabel memiliki indeks");
} else {
    printWarning("Ditemukan $indexIssues tabel tanpa indeks");
}

// Periksa tabel kosong
printSubHeader("TABEL KOSONG");
$emptyTables = 0;

foreach ($tables as $table) {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM `$table`");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $count = $result['count'];
    
    if ($count === '0') {
        printWarning("Tabel '$table' kosong");
        $emptyTables++;
    } else {
        printInfo("Tabel '$table' berisi $count baris");
    }
}

if ($emptyTables === 0) {
    printSuccess("Semua tabel berisi data");
} else {
    printWarning("Ditemukan $emptyTables tabel kosong");
}

// Kesimpulan
printHeader("KESIMPULAN");

if ($constraintIssues === 0 && $indexIssues === 0) {
    printSuccess("Database dalam kondisi baik");
} else {
    if ($constraintIssues > 0) {
        printError("Ditemukan $constraintIssues masalah dengan foreign key constraints");
    }
    if ($indexIssues > 0) {
        printWarning("Ditemukan $indexIssues tabel tanpa indeks");
    }
    printWarning("Database memerlukan perbaikan");
}

echo "\n";
echo "Pemeriksaan selesai pada " . date('Y-m-d H:i:s') . "\n";
echo "\n";
