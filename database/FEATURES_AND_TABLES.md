# Fitur dan Tabel KikaZen Ship

Dokumen ini berisi informasi tentang fitur-fitur yang sudah ada dan yang akan datang di KikaZen Ship, serta tabel-tabel yang digunakan untuk setiap fitur.

## Fitur yang Sudah Ada

### 1. Autentikasi dan Manajemen Pengguna

**Deskripsi**: Fitur untuk pendaftaran, login, dan manajemen profil pengguna.

**Tabel yang Digunakan**:
- `users`: Data pengguna (pelanggan)
- `drivers`: Data pengemudi/kurir
- `restaurant_owners`: Data pemilik restoran
- `admins`: Data administrator
- `user_addresses`: <PERSON><PERSON><PERSON>-al<PERSON>t pengguna

**File PHP Terkait**:
- `login.php`
- `register.php`
- `user/profile.php`
- `user/addresses.php`

### 2. Manajemen Restoran dan Menu

**Deskripsi**: Fitur untuk mengelola restoran dan menu makanan.

**Tabel yang Digunakan**:
- `restaurants`: Data restoran
- `categories`: <PERSON><PERSON>i makanan
- `menu_items`: Item menu dari restoran

**File PHP Terkait**:
- `restaurants.php`
- `restaurant.php`
- `owner/dashboard.php`
- `owner/menu.php`

### 3. Pemesanan Makanan

**Deskripsi**: Fitur untuk memesan makanan dari restoran.

**Tabel yang Digunakan**:
- `orders`: Data pesanan
- `order_items`: Item yang dipesan dalam suatu pesanan
- `order_tracking`: Pelacakan status pesanan

**File PHP Terkait**:
- `cart.php`
- `checkout.php`
- `order_confirmation.php`
- `user/orders.php`

### 4. Ulasan dan Penilaian

**Deskripsi**: Fitur untuk memberikan ulasan dan penilaian untuk restoran dan pengemudi.

**Tabel yang Digunakan**:
- `reviews`: Ulasan dan penilaian
- `review_photos`: Foto yang dilampirkan pada ulasan
- `review_helpful`: Penanda ulasan yang membantu
- `review_reports`: Laporan ulasan yang tidak pantas

**File PHP Terkait**:
- `review.php`
- `user/reviews.php`

## Fitur yang Akan Datang

### 1. Keberlanjutan dan Tanggung Jawab Sosial

**Deskripsi**: Fitur untuk mendukung inisiatif keberlanjutan dan tanggung jawab sosial.

**Tabel yang Digunakan**:
- `sustainability_initiatives`: Inisiatif keberlanjutan
- `initiative_updates`: Update untuk inisiatif keberlanjutan
- `initiative_impact`: Dampak dari inisiatif keberlanjutan
- `sustainability_tags`: Tag keberlanjutan untuk restoran
- `restaurant_sustainability_tags`: Hubungan antara restoran dan tag keberlanjutan
- `restaurant_initiatives`: Hubungan antara restoran dan inisiatif keberlanjutan
- `donation_programs`: Program donasi
- `donation_tiers`: Tingkatan donasi
- `donations`: Donasi yang dilakukan
- `donation_impact`: Dampak dari program donasi
- `sustainability_tips`: Tips keberlanjutan
- `carbon_footprint`: Jejak karbon dari pesanan
- `sustainability_badges`: Badge keberlanjutan
- `sustainability_subscribers`: Pelanggan yang berlangganan inisiatif keberlanjutan

**File PHP yang Direncanakan**:
- `sustainability.php`
- `donations.php`
- `carbon_offset.php`

### 2. Chat dan Komunikasi

**Deskripsi**: Fitur untuk komunikasi antara pengguna, restoran, pengemudi, dan admin.

**Tabel yang Digunakan**:
- `chat_rooms`: Ruang chat
- `chat_participants`: Peserta dalam ruang chat
- `chat_messages`: Pesan dalam ruang chat
- `unread_message_counts`: Jumlah pesan yang belum dibaca

**File PHP yang Direncanakan**:
- `chat.php`
- `user/messages.php`
- `driver/messages.php`
- `owner/messages.php`
- `admin/messages.php`

### 3. Keluhan dan Dukungan

**Deskripsi**: Fitur untuk mengelola keluhan dan memberikan dukungan kepada pengguna.

**Tabel yang Digunakan**:
- `complaints`: Keluhan dari pengguna
- `complaint_attachments`: Lampiran untuk keluhan
- `complaint_updates`: Update untuk keluhan
- `active_complaints_view`: View untuk keluhan yang aktif

**File PHP yang Direncanakan**:
- `support.php`
- `user/complaints.php`
- `admin/complaints.php`

### 4. Promosi dan Diskon

**Deskripsi**: Fitur untuk mengelola promosi dan diskon.

**Tabel yang Digunakan**:
- `promotions`: Promosi dan diskon
- `promotion_items`: Item yang termasuk dalam promosi
- `promotion_categories`: Kategori yang termasuk dalam promosi

**File PHP yang Direncanakan**:
- `promotions.php`
- `owner/promotions.php`
- `admin/promotions.php`

### 5. Komunitas

**Deskripsi**: Fitur untuk mengelola acara komunitas.

**Tabel yang Digunakan**:
- `community_events`: Acara komunitas
- `event_participants`: Peserta acara komunitas

**File PHP yang Direncanakan**:
- `community.php`
- `events.php`
- `user/events.php`

### 6. Preferensi dan Personalisasi

**Deskripsi**: Fitur untuk mengelola preferensi dan personalisasi pengguna.

**Tabel yang Digunakan**:
- `preferences`: Preferensi umum
- `user_preferences`: Preferensi pengguna
- `user_cuisine_preferences`: Preferensi masakan pengguna
- `favorites`: Restoran favorit pengguna
- `user_search_history`: Riwayat pencarian pengguna
- `user_view_history`: Riwayat tampilan pengguna

**File PHP yang Direncanakan**:
- `user/preferences.php`
- `user/favorites.php`

## Panduan Pengembangan

1. **Periksa Tabel yang Ada**: Sebelum mengembangkan fitur baru, periksa apakah tabel yang diperlukan sudah ada.

2. **Gunakan Tabel yang Ada**: Jika memungkinkan, gunakan tabel yang sudah ada untuk fitur baru.

3. **Dokumentasikan Penggunaan Tabel**: Dokumentasikan tabel-tabel yang digunakan untuk setiap fitur di dokumen ini.

4. **Perbarui Dokumen Ini**: Perbarui dokumen ini saat mengembangkan fitur baru atau mengubah penggunaan tabel.

## Catatan Penting

- Beberapa tabel mungkin belum digunakan dalam aplikasi saat ini, tetapi sudah disiapkan untuk fitur yang akan datang.
- Jika Anda menemukan tabel yang tidak terdokumentasi di sini, harap tambahkan informasinya.
- Jika Anda menemukan fitur yang tidak terdokumentasi di sini, harap tambahkan informasinya.
