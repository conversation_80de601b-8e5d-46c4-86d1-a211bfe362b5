-- Cha<PERSON> and <PERSON><PERSON><PERSON>t System for KikaZen Ship
USE kikazen_ship;

-- Chat Rooms Table
CREATE TABLE IF NOT EXISTS chat_rooms (
    room_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT,
    room_type ENUM('order', 'complaint', 'support') NOT NULL,
    status ENUM('active', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
);

-- Chat Participants Table
CREATE TABLE IF NOT EXISTS chat_participants (
    participant_id INT AUTO_INCREMENT PRIMARY KEY,
    room_id INT NOT NULL,
    user_type ENUM('customer', 'restaurant_owner', 'driver', 'admin') NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (room_id) REFERENCES chat_rooms(room_id) ON DELETE CASCADE
);

-- Chat Messages Table
CREATE TABLE IF NOT EXISTS chat_messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    room_id INT NOT NULL,
    sender_type ENUM('customer', 'restaurant_owner', 'driver', 'admin') NOT NULL,
    sender_id INT NOT NULL,
    message_text TEXT NOT NULL,
    message_type ENUM('text', 'image', 'location', 'system') DEFAULT 'text',
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(room_id) ON DELETE CASCADE
);

-- Complaints Table
CREATE TABLE IF NOT EXISTS complaints (
    complaint_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_id INT,
    complaint_type ENUM('order', 'delivery', 'restaurant', 'app', 'other') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status ENUM('pending', 'in_progress', 'resolved', 'closed') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_admin_id INT,
    resolution_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_admin_id) REFERENCES admins(admin_id) ON DELETE SET NULL
);

-- Complaint Attachments Table
CREATE TABLE IF NOT EXISTS complaint_attachments (
    attachment_id INT AUTO_INCREMENT PRIMARY KEY,
    complaint_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE
);

-- Complaint Updates Table
CREATE TABLE IF NOT EXISTS complaint_updates (
    update_id INT AUTO_INCREMENT PRIMARY KEY,
    complaint_id INT NOT NULL,
    admin_id INT NOT NULL,
    update_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_chat_rooms_order_id ON chat_rooms(order_id);
CREATE INDEX idx_chat_rooms_status ON chat_rooms(status);
CREATE INDEX idx_chat_participants_room_id ON chat_participants(room_id);
CREATE INDEX idx_chat_participants_user ON chat_participants(user_type, user_id);
CREATE INDEX idx_chat_messages_room_id ON chat_messages(room_id);
CREATE INDEX idx_chat_messages_sender ON chat_messages(sender_type, sender_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_complaints_user_id ON complaints(user_id);
CREATE INDEX idx_complaints_order_id ON complaints(order_id);
CREATE INDEX idx_complaints_status ON complaints(status);
CREATE INDEX idx_complaints_assigned_admin_id ON complaints(assigned_admin_id);

-- Add notification settings to users table if not exists
ALTER TABLE users
ADD COLUMN IF NOT EXISTS chat_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the user receives chat notifications',
ADD COLUMN IF NOT EXISTS email_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the user receives email notifications';

-- Add notification settings to restaurant_owners table if not exists
ALTER TABLE restaurant_owners
ADD COLUMN IF NOT EXISTS chat_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the restaurant owner receives chat notifications',
ADD COLUMN IF NOT EXISTS email_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the restaurant owner receives email notifications';

-- Add notification settings to drivers table if not exists
ALTER TABLE drivers
ADD COLUMN IF NOT EXISTS chat_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the driver receives chat notifications',
ADD COLUMN IF NOT EXISTS email_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the driver receives email notifications';

-- Add notification settings to admins table if not exists
ALTER TABLE admins
ADD COLUMN IF NOT EXISTS chat_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the admin receives chat notifications',
ADD COLUMN IF NOT EXISTS email_notifications TINYINT(1) DEFAULT 1 COMMENT 'Whether the admin receives email notifications';

-- Create a view for unread message counts
CREATE OR REPLACE VIEW unread_message_counts AS
SELECT 
    cp.user_type,
    cp.user_id,
    cp.room_id,
    COUNT(cm.message_id) AS unread_count
FROM 
    chat_participants cp
JOIN 
    chat_messages cm ON cp.room_id = cm.room_id
WHERE 
    cm.created_at > cp.last_read_at
    AND cm.sender_id != cp.user_id
    AND cm.sender_type != cp.user_type
GROUP BY 
    cp.user_type, cp.user_id, cp.room_id;

-- Create a view for active complaints with details
CREATE OR REPLACE VIEW active_complaints_view AS
SELECT 
    c.complaint_id,
    c.user_id,
    u.name AS user_name,
    c.order_id,
    c.complaint_type,
    c.subject,
    c.status,
    c.priority,
    c.assigned_admin_id,
    a.name AS admin_name,
    c.created_at,
    c.updated_at,
    DATEDIFF(NOW(), c.created_at) AS days_open
FROM 
    complaints c
LEFT JOIN 
    users u ON c.user_id = u.user_id
LEFT JOIN 
    admins a ON c.assigned_admin_id = a.admin_id
WHERE 
    c.status IN ('pending', 'in_progress')
ORDER BY 
    CASE c.priority
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    c.created_at;
