<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: user/orders.php');
    exit;
}

$order_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Connect to database
$conn = connectDB();

// Get order details
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address, r.phone as restaurant_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.order_id = ? AND o.user_id = ?
");
$stmt->execute([$order_id, $user_id]);

if ($stmt->rowCount() === 0) {
    header('Location: user/orders.php');
    exit;
}

$order = $stmt->fetch();

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, mi.name as item_name
    FROM order_items oi
    JOIN menu_items mi ON oi.item_id = mi.item_id
    WHERE oi.order_id = ?
");
$stmt->execute([$order_id]);
$orderItems = $stmt->fetchAll();

// Clear cart in JavaScript
$clearCart = true;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konfirmasi Pesanan - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isUserLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['user_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Masuk</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Daftar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Confirmation Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Konfirmasi Pesanan</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="user/orders.php">Pesanan</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Konfirmasi</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                        </div>
                        <h2 class="mb-3">Terima Kasih!</h2>
                        <p class="lead mb-4">Pesanan Anda telah berhasil dibuat.</p>
                        <div class="mb-4">
                            <h4>Nomor Pesanan: #<?= $order_id ?></h4>
                            <p class="text-muted">Dibuat pada: <?= date('d F Y, H:i', strtotime($order['created_at'])) ?></p>
                        </div>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="user/orders.php" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>Lihat Pesanan Saya
                            </a>
                            <a href="restaurants.php" class="btn btn-outline-primary">
                                <i class="fas fa-utensils me-2"></i>Jelajahi Restoran Lain
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Order Status -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Status Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between position-relative mb-4">
                            <div class="progress position-absolute" style="width: 80%; top: 1.5rem; left: 10%; height: 2px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>

                            <div class="text-center position-relative">
                                <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 40px; height: 40px; z-index: 1;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>Pesanan Dibuat</div>
                            </div>

                            <div class="text-center position-relative">
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 40px; height: 40px; z-index: 1; border: 1px solid #ccc;">
                                    <i class="fas fa-utensils text-muted"></i>
                                </div>
                                <div>Diproses</div>
                            </div>

                            <div class="text-center position-relative">
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 40px; height: 40px; z-index: 1; border: 1px solid #ccc;">
                                    <i class="fas fa-motorcycle text-muted"></i>
                                </div>
                                <div>Dalam Perjalanan</div>
                            </div>

                            <div class="text-center position-relative">
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 40px; height: 40px; z-index: 1; border: 1px solid #ccc;">
                                    <i class="fas fa-home text-muted"></i>
                                </div>
                                <div>Terkirim</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Restoran akan segera memproses pesanan Anda. Anda akan menerima notifikasi saat status pesanan berubah.
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Order Summary -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ringkasan Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="mb-2">Restoran</h6>
                            <p class="mb-1"><?= $order['restaurant_name'] ?></p>
                            <p class="text-muted small mb-0"><?= $order['restaurant_address'] ?></p>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <h6 class="mb-2">Item Pesanan</h6>
                            <?php foreach ($orderItems as $item): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <div>
                                        <span class="fw-bold"><?= $item['quantity'] ?>x</span> <?= $item['item_name'] ?>
                                    </div>
                                    <div>
                                        Rp<?= number_format($item['subtotal'] * 15000, 0, ',', '.') ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>Rp<?= number_format($order['subtotal'] * 15000, 0, ',', '.') ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Biaya Pengiriman:</span>
                                <span>Rp<?= number_format($order['delivery_fee'] * 15000, 0, ',', '.') ?></span>
                            </div>
                            <?php if ($order['carbon_offset']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Offset Karbon:</span>
                                    <span>Rp<?= number_format($order['carbon_offset_amount'] * 15000, 0, ',', '.') ?></span>
                                </div>
                            <?php endif; ?>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-bold">Total:</span>
                                <span class="fw-bold">Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></span>
                            </div>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <h6 class="mb-2">Alamat Pengiriman</h6>
                            <p class="mb-0"><?= $order['delivery_address'] ?></p>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <h6 class="mb-2">Metode Pembayaran</h6>
                            <p class="mb-0">
                                <?php if ($order['payment_method'] === 'cash'): ?>
                                    <i class="fas fa-money-bill-wave me-2 text-success"></i> Tunai
                                <?php elseif ($order['payment_method'] === 'credit_card'): ?>
                                    <i class="fas fa-credit-card me-2 text-primary"></i> Kartu Kredit
                                <?php elseif ($order['payment_method'] === 'e-wallet'): ?>
                                    <i class="fas fa-wallet me-2 text-warning"></i> E-Wallet
                                <?php endif; ?>
                            </p>
                        </div>

                        <?php if (!empty($order['notes'])): ?>
                            <hr>
                            <div class="mb-0">
                                <h6 class="mb-2">Catatan</h6>
                                <p class="mb-0"><?= $order['notes'] ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Sustainability Impact -->
                <?php if ($order['no_utensils'] || $order['eco_packaging'] || $order['carbon_offset']): ?>
                    <div class="card shadow-sm mb-4 border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Dampak Keberlanjutan Anda</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($order['no_utensils']): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-utensils-slash text-success fa-2x me-3"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Tanpa Alat Makan</h6>
                                        <p class="text-muted mb-0">Anda telah menghemat ~20g plastik sekali pakai</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($order['eco_packaging']): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-box text-success fa-2x me-3"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Kemasan Ramah Lingkungan</h6>
                                        <p class="text-muted mb-0">Anda telah memilih kemasan yang lebih berkelanjutan</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($order['carbon_offset']): ?>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-leaf text-success fa-2x me-3"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Offset Karbon</h6>
                                        <p class="text-muted mb-0">Anda telah mengimbangi <?= number_format($order['estimated_emissions'], 2) ?> kg emisi CO₂</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php if ($clearCart): ?>
    <script>
        // Clear cart on successful order
        localStorage.removeItem('cart');

        // Also clear cart on server
        fetch('api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ items: [], restaurant_id: null })
        })
        .catch(error => {
            console.error('Error clearing cart on server:', error);
        });
    </script>
    <?php endif; ?>
</body>
</html>
