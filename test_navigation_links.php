<?php
// Test file untuk memverifikasi semua link navigasi
session_start();
require_once 'config/database.php';

echo "<h2>Test Link Navigasi KikaZen Ship</h2>";

// Simulasi berbagai user types
$userTypes = ['customer', 'driver', 'admin', 'owner'];

foreach ($userTypes as $userType) {
    echo "<h3>User Type: " . ucfirst($userType) . "</h3>";
    
    // Set session untuk simulasi
    $_SESSION['user_type'] = $userType;
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = "Test " . ucfirst($userType);
    
    $isLoggedIn = true;
    
    echo "<h4>Link Navigasi Utama:</h4>";
    echo "<ul>";
    
    if ($userType === 'customer') {
        $links = [
            'Dasbor' => 'user/dashboard.php',
            'Pesanan' => 'user/orders.php',
            'Profil' => 'user/profile.php',
            'Chat' => 'user/chat.php',
            'Alamat' => 'user/addresses.php'
        ];
    } elseif ($userType === 'driver') {
        $links = [
            'Dasbor' => 'driver/dashboard.php',
            'Pesanan Tersedia' => 'driver/available_orders.php',
            'Pesanan Saya' => 'driver/orders.php',
            'Profil' => 'driver/profile.php',
            'Chat' => 'driver/chat.php'
        ];
    } elseif ($userType === 'admin') {
        $links = [
            'Dasbor' => 'admin/dashboard.php',
            'Pesanan' => 'admin/orders.php',
            'Pengguna' => 'admin/users.php',
            'Restoran' => 'admin/restaurants.php',
            'Driver' => 'admin/drivers.php',
            'Profil' => 'admin/profile.php'
        ];
    } elseif ($userType === 'owner') {
        $links = [
            'Dasbor' => 'owner/dashboard.php',
            'Menu' => 'owner/menu.php',
            'Pesanan' => 'owner/orders.php',
            'Profil' => 'owner/profile.php',
            'Chat' => 'owner/chat.php'
        ];
    }
    
    foreach ($links as $name => $path) {
        $fullPath = __DIR__ . '/' . $path;
        $exists = file_exists($fullPath);
        $status = $exists ? '✓' : '✗';
        $color = $exists ? 'green' : 'red';
        
        echo "<li style='color: $color;'>$status <strong>$name</strong>: <code>$path</code>";
        if (!$exists) {
            echo " <em>(File tidak ditemukan)</em>";
        }
        echo "</li>";
    }
    
    echo "</ul>";
    echo "<hr>";
}

// Test link umum
echo "<h3>Link Umum</h3>";
$commonLinks = [
    'Beranda' => 'index.php',
    'Restoran' => 'restaurants.php',
    'Login' => 'login.php',
    'Register' => 'register.php',
    'Logout' => 'logout.php',
    'Keranjang' => 'cart.php'
];

echo "<ul>";
foreach ($commonLinks as $name => $path) {
    $fullPath = __DIR__ . '/' . $path;
    $exists = file_exists($fullPath);
    $status = $exists ? '✓' : '✗';
    $color = $exists ? 'green' : 'red';
    
    echo "<li style='color: $color;'>$status <strong>$name</strong>: <code>$path</code>";
    if (!$exists) {
        echo " <em>(File tidak ditemukan)</em>";
    }
    echo "</li>";
}
echo "</ul>";

// Test navigasi dari index.php
echo "<h3>Test Navigasi dari index.php</h3>";
echo "<p>Simulasi link yang akan muncul di index.php berdasarkan user type:</p>";

foreach ($userTypes as $userType) {
    echo "<h4>Sebagai " . ucfirst($userType) . ":</h4>";
    echo "<ul>";
    
    if ($userType === 'customer') {
        echo "<li>Navigasi: <code>user/dashboard.php</code> (Dasbor)</li>";
        echo "<li>Dropdown: <code>user/dashboard.php</code> (Dasbor)</li>";
        echo "<li>Dropdown: <code>user/orders.php</code> (Pesanan)</li>";
        echo "<li>Dropdown: <code>user/profile.php</code> (Profil)</li>";
        echo "<li>Dropdown: <code>user/chat.php</code> (Chat)</li>";
    } elseif ($userType === 'driver') {
        echo "<li>Navigasi: <code>driver/dashboard.php</code> (Dasbor Pengemudi)</li>";
        echo "<li>Dropdown: <code>driver/dashboard.php</code> (Dasbor)</li>";
        echo "<li>Dropdown: <code>driver/orders.php</code> (Pesanan)</li>";
        echo "<li>Dropdown: <code>driver/profile.php</code> (Profil)</li>";
    } elseif ($userType === 'admin') {
        echo "<li>Navigasi: <code>admin/dashboard.php</code> (Dasbor Admin)</li>";
        echo "<li>Dropdown: <code>admin/dashboard.php</code> (Dasbor)</li>";
        echo "<li>Dropdown: <code>admin/profile.php</code> (Profil)</li>";
    }
    
    echo "</ul>";
}

// Test file yang mungkin hilang
echo "<h3>File yang Mungkin Hilang</h3>";
$missingFiles = [];

// Check for missing files
$checkFiles = [
    'cart.php',
    'about.php',
    'contact.php',
    'recommendations.php'
];

echo "<ul>";
foreach ($checkFiles as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $status = $exists ? '✓' : '✗';
    $color = $exists ? 'green' : 'red';
    
    echo "<li style='color: $color;'>$status <code>$file</code>";
    if (!$exists) {
        echo " <em>(Perlu dibuat)</em>";
        $missingFiles[] = $file;
    }
    echo "</li>";
}
echo "</ul>";

if (!empty($missingFiles)) {
    echo "<h4>Rekomendasi:</h4>";
    echo "<p>File-file berikut perlu dibuat atau diperbaiki:</p>";
    echo "<ul>";
    foreach ($missingFiles as $file) {
        echo "<li><code>$file</code></li>";
    }
    echo "</ul>";
}

// Test struktur direktori
echo "<h3>Struktur Direktori</h3>";
$directories = ['user', 'driver', 'admin', 'owner', 'restaurant'];

echo "<ul>";
foreach ($directories as $dir) {
    $exists = is_dir(__DIR__ . '/' . $dir);
    $status = $exists ? '✓' : '✗';
    $color = $exists ? 'green' : 'red';
    
    echo "<li style='color: $color;'>$status Direktori <code>$dir/</code>";
    if (!$exists) {
        echo " <em>(Direktori tidak ditemukan)</em>";
    }
    echo "</li>";
}
echo "</ul>";

echo "<hr>";
echo "<h3>Kesimpulan</h3>";
echo "<p><strong>Masalah yang telah diperbaiki:</strong></p>";
echo "<ul>";
echo "<li>✓ Link navigasi di index.php untuk customer sudah diperbaiki dari <code>orders.php</code> ke <code>user/dashboard.php</code></li>";
echo "<li>✓ Dropdown menu di index.php sudah diperbaiki dengan path yang benar untuk setiap user type</li>";
echo "<li>✓ File restaurants.php sudah memiliki navigasi yang benar</li>";
echo "</ul>";

echo "<p><strong>Status:</strong> Error 404 di dashboard pelanggan seharusnya sudah teratasi!</p>";

echo "<p><a href='index.php'>← Test Navigasi di Index</a> | <a href='restaurants.php'>Test Navigasi di Restaurants</a></p>";
?>
