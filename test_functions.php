<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Test functions
echo "<h1>Testing Functions</h1>";

// Test getOrderStatusText
echo "<h2>Testing getOrderStatusText</h2>";
$statuses = ['pending', 'confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way', 'delivered', 'cancelled'];
foreach ($statuses as $status) {
    echo "getOrderStatusText('$status') = " . getOrderStatusText($status) . "<br>";
}

// Test getOrderStatusBadgeClass
echo "<h2>Testing getOrderStatusBadgeClass</h2>";
foreach ($statuses as $status) {
    echo "getOrderStatusBadgeClass('$status') = " . getOrderStatusBadgeClass($status) . "<br>";
}

// Test getPaymentStatusText
echo "<h2>Testing getPaymentStatusText</h2>";
$paymentStatuses = ['pending', 'paid', 'failed'];
foreach ($paymentStatuses as $status) {
    echo "getPaymentStatusText('$status') = " . getPaymentStatusText($status) . "<br>";
}

// Test getPaymentStatusBadgeClass
echo "<h2>Testing getPaymentStatusBadgeClass</h2>";
foreach ($paymentStatuses as $status) {
    echo "getPaymentStatusBadgeClass('$status') = " . getPaymentStatusBadgeClass($status) . "<br>";
}

// Test getPaymentMethodText
echo "<h2>Testing getPaymentMethodText</h2>";
$paymentMethods = ['cash', 'credit_card', 'e-wallet'];
foreach ($paymentMethods as $method) {
    echo "getPaymentMethodText('$method') = " . getPaymentMethodText($method) . "<br>";
}

// Test formatRupiah
echo "<h2>Testing formatRupiah</h2>";
$amounts = [0, 10000, 15000, 100000, 1000000];
foreach ($amounts as $amount) {
    echo "formatRupiah($amount) = " . formatRupiah($amount) . "<br>";
}
