/**
 * Main JavaScript file for KikaZen Ship Food Delivery App
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Try to load cart from server first, then fall back to localStorage
    loadCartFromServer();

    // Initialize cart from localStorage (as fallback)
    initializeCart();

    // Add event listeners for add to cart buttons
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    if (addToCartButtons) {
        addToCartButtons.forEach(button => {
            button.addEventListener('click', addToCart);
        });
    }

    // Add event listeners for quantity buttons in cart
    const quantityButtons = document.querySelectorAll('.quantity-btn');
    if (quantityButtons) {
        quantityButtons.forEach(button => {
            button.addEventListener('click', updateCartItemQuantity);
        });
    }

    // Add event listener for remove from cart buttons
    const removeButtons = document.querySelectorAll('.remove-from-cart');
    if (removeButtons) {
        removeButtons.forEach(button => {
            button.addEventListener('click', removeFromCart);
        });
    }

    // Add event listener for clear cart button
    const clearCartButton = document.getElementById('clear-cart');
    if (clearCartButton) {
        clearCartButton.addEventListener('click', clearCart);
    }

    // Add event listener for checkout form
    const checkoutForm = document.getElementById('checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', processCheckout);
    }

    // Add event listener for driver status toggle
    const statusToggle = document.getElementById('status-toggle');
    if (statusToggle) {
        statusToggle.addEventListener('change', updateDriverStatus);
    }

    // Add event listener for order acceptance
    const acceptOrderButtons = document.querySelectorAll('.accept-order');
    if (acceptOrderButtons) {
        acceptOrderButtons.forEach(button => {
            button.addEventListener('click', acceptOrder);
        });
    }

    // Add event listener for order status update
    const updateStatusButtons = document.querySelectorAll('.update-status');
    if (updateStatusButtons) {
        updateStatusButtons.forEach(button => {
            button.addEventListener('click', updateOrderStatus);
        });
    }

    // Initialize location tracking for drivers
    if (document.getElementById('driver-dashboard')) {
        initializeLocationTracking();
    }

    // Initialize maps for order tracking
    const trackingMap = document.getElementById('tracking-map');
    if (trackingMap) {
        initializeTrackingMap();
    }
});

/**
 * Cart Functions
 */

// Initialize cart from localStorage
function initializeCart() {
    let cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };
    updateCartDisplay(cart);
}

// Add item to cart
function addToCart(event) {
    event.preventDefault();

    const button = event.currentTarget;
    const itemId = button.dataset.itemId;
    const itemName = button.dataset.itemName;
    const itemPrice = parseFloat(button.dataset.itemPrice);
    const restaurantId = button.dataset.restaurantId;

    let cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };

    // Check if adding from a different restaurant
    if (cart.restaurant_id && cart.restaurant_id !== restaurantId && cart.items.length > 0) {
        if (!confirm('Keranjang belanja Anda berisi item dari restoran lain. Apakah Anda ingin mengosongkan keranjang dan menambahkan item ini?')) {
            return;
        }
        cart = { items: [], restaurant_id: null };
    }

    // Set restaurant ID if cart is empty
    if (!cart.restaurant_id) {
        cart.restaurant_id = restaurantId;
    }

    // Check if item already in cart
    const existingItem = cart.items.find(item => item.id === itemId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.items.push({
            id: itemId,
            item_id: itemId, // Add item_id for server-side processing
            name: itemName,
            price: itemPrice,
            quantity: 1
        });
    }

    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Sync cart with server
    syncCartWithServer(cart);

    // Update cart display
    updateCartDisplay(cart);

    // Show success message
    showToast('Item ditambahkan ke keranjang');
}

// Update cart item quantity
function updateCartItemQuantity(event) {
    const button = event.currentTarget;
    const itemId = button.dataset.itemId;
    const action = button.dataset.action;

    let cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };
    const itemIndex = cart.items.findIndex(item => item.id === itemId);

    if (itemIndex !== -1) {
        if (action === 'increase') {
            cart.items[itemIndex].quantity += 1;
        } else if (action === 'decrease') {
            cart.items[itemIndex].quantity -= 1;

            // Remove item if quantity is 0
            if (cart.items[itemIndex].quantity <= 0) {
                cart.items.splice(itemIndex, 1);
            }
        }

        // Clear restaurant ID if cart is empty
        if (cart.items.length === 0) {
            cart.restaurant_id = null;
        }

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Sync cart with server
        syncCartWithServer(cart);

        // Update cart display
        updateCartDisplay(cart);
    }
}

// Remove item from cart
function removeFromCart(event) {
    const button = event.currentTarget;
    const itemId = button.dataset.itemId;

    let cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };
    const itemIndex = cart.items.findIndex(item => item.id === itemId);

    if (itemIndex !== -1) {
        cart.items.splice(itemIndex, 1);

        // Clear restaurant ID if cart is empty
        if (cart.items.length === 0) {
            cart.restaurant_id = null;
        }

        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));

        // Sync cart with server
        syncCartWithServer(cart);

        // Update cart display
        updateCartDisplay(cart);
    }
}

// Clear cart
function clearCart() {
    if (confirm('Apakah Anda yakin ingin mengosongkan keranjang belanja?')) {
        const emptyCart = { items: [], restaurant_id: null };
        localStorage.setItem('cart', JSON.stringify(emptyCart));

        // Sync empty cart with server
        syncCartWithServer(emptyCart);

        updateCartDisplay(emptyCart);
    }
}

// Update cart display
function updateCartDisplay(cart) {
    const cartItemsElement = document.getElementById('cart-items');
    const cartTotalElement = document.getElementById('cart-total');
    const cartCountElement = document.getElementById('cart-count');

    if (cartItemsElement) {
        // Clear current cart items
        cartItemsElement.innerHTML = '';

        if (cart.items.length === 0) {
            cartItemsElement.innerHTML = '<p class="text-center">Keranjang belanja Anda kosong</p>';
        } else {
            // Add each item to cart display
            cart.items.forEach(item => {
                const itemTotal = item.price * item.quantity;

                const itemElement = document.createElement('div');
                itemElement.className = 'cart-item';
                itemElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">${item.name}</h6>
                            <small class="text-muted">Rp${formatRupiah(item.price * 15000)} per item</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-secondary quantity-btn" data-item-id="${item.id}" data-action="decrease">-</button>
                            <span class="mx-2">${item.quantity}</span>
                            <button class="btn btn-sm btn-outline-secondary quantity-btn" data-item-id="${item.id}" data-action="increase">+</button>
                            <button class="btn btn-sm btn-outline-danger ms-2 remove-from-cart" data-item-id="${item.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-end mt-1">
                        <span>Rp${formatRupiah(itemTotal * 15000)}</span>
                    </div>
                `;

                cartItemsElement.appendChild(itemElement);
            });

            // Add event listeners to new buttons
            const quantityButtons = cartItemsElement.querySelectorAll('.quantity-btn');
            quantityButtons.forEach(button => {
                button.addEventListener('click', updateCartItemQuantity);
            });

            const removeButtons = cartItemsElement.querySelectorAll('.remove-from-cart');
            removeButtons.forEach(button => {
                button.addEventListener('click', removeFromCart);
            });
        }
    }

    // Update cart total
    if (cartTotalElement) {
        const total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        cartTotalElement.textContent = `Rp${formatRupiah(total * 15000)}`;
    }

    // Update cart count
    if (cartCountElement) {
        const count = cart.items.reduce((sum, item) => sum + item.quantity, 0);
        cartCountElement.textContent = count;

        if (count > 0) {
            cartCountElement.style.display = 'inline-block';
        } else {
            cartCountElement.style.display = 'none';
        }
    }
}

/**
 * Checkout Functions
 */

// Process checkout
function processCheckout(event) {
    event.preventDefault();

    const form = event.currentTarget;
    const cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };

    if (cart.items.length === 0) {
        showToast('Keranjang belanja Anda kosong', 'error');
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const orderData = {
        user_id: formData.get('user_id'),
        restaurant_id: cart.restaurant_id,
        items: cart.items.map(item => ({
            item_id: item.id,
            quantity: item.quantity,
            special_instructions: ''
        })),
        delivery_address: formData.get('delivery_address'),
        delivery_latitude: formData.get('delivery_latitude'),
        delivery_longitude: formData.get('delivery_longitude'),
        payment_method: formData.get('payment_method'),
        notes: formData.get('notes')
    };

    // Send order to server
    fetch('/api/orders.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear cart
            localStorage.setItem('cart', JSON.stringify({ items: [], restaurant_id: null }));

            // Redirect to order confirmation page
            window.location.href = `order_confirmation.php?order_id=${data.order_id}`;
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while processing your order', 'error');
    });
}

/**
 * Driver Functions
 */

// Update driver status
function updateDriverStatus(event) {
    const statusToggle = event.currentTarget;
    const status = statusToggle.checked ? 'available' : 'offline';

    fetch('/api/driver_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Status updated to ${status}`);
        } else {
            showToast(data.message, 'error');
            // Revert toggle if update failed
            statusToggle.checked = !statusToggle.checked;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while updating status', 'error');
        // Revert toggle if update failed
        statusToggle.checked = !statusToggle.checked;
    });
}

// Initialize location tracking for drivers
function initializeLocationTracking() {
    if (navigator.geolocation) {
        // Get initial position
        navigator.geolocation.getCurrentPosition(updateDriverLocation);

        // Watch position changes
        navigator.geolocation.watchPosition(updateDriverLocation);
    }
}

// Update driver location
function updateDriverLocation(position) {
    const latitude = position.coords.latitude;
    const longitude = position.coords.longitude;

    fetch('/api/driver_location.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ latitude: latitude, longitude: longitude })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to update location:', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating location:', error);
    });
}

/**
 * Utility Functions
 */

// Sync cart with server to persist cart data
function syncCartWithServer(cart) {
    // Ensure all items have item_id property
    if (cart && cart.items && Array.isArray(cart.items)) {
        cart.items = cart.items.map(item => {
            // If item doesn't have item_id but has id, copy id to item_id
            if (!item.hasOwnProperty('item_id') && item.hasOwnProperty('id')) {
                return { ...item, item_id: item.id };
            }
            return item;
        });

        // Update localStorage with fixed cart
        localStorage.setItem('cart', JSON.stringify(cart));
    }

    // Use fetch API to send cart data to server
    fetch('/api/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(cart)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to sync cart with server:', data.message);
        }
    })
    .catch(error => {
        console.error('Error syncing cart with server:', error);
    });
}

// Load cart from server
function loadCartFromServer() {
    fetch('/api/cart.php', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.cart) {
            // Ensure all items have item_id property
            if (data.cart && data.cart.items && Array.isArray(data.cart.items)) {
                data.cart.items = data.cart.items.map(item => {
                    // If item doesn't have item_id but has id, copy id to item_id
                    if (!item.hasOwnProperty('item_id') && item.hasOwnProperty('id')) {
                        return { ...item, item_id: item.id };
                    }
                    return item;
                });
            }

            // Update localStorage with server cart data
            localStorage.setItem('cart', JSON.stringify(data.cart));
            // Update cart display
            updateCartDisplay(data.cart);
        }
    })
    .catch(error => {
        console.error('Error loading cart from server:', error);
    });
}

// Format number to Indonesian Rupiah format
function formatRupiah(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}

// Show toast notification
function showToast(message, type = 'success') {
    const toastContainer = document.getElementById('toast-container');

    if (!toastContainer) {
        // Create toast container if it doesn't exist
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }

    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    document.getElementById('toast-container').appendChild(toast);

    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
