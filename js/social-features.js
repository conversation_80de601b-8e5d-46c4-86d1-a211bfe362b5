/**
 * KikaZen Ship - Social Features
 * JavaScript untuk fitur sosial
 */

// Tunggu hingga dokumen siap
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi komponen
    initializeReviews();
    initializePhotoUpload();
    initializePhotoViewer();
    initializeSocialSharing();
    initializeHelpfulButtons();
});

/**
 * Inisialisasi ulasan
 */
function initializeReviews() {
    const reviewForm = document.getElementById('review-form');
    if (!reviewForm) return;
    
    reviewForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validasi form
        const rating = document.querySelector('input[name="rating"]:checked');
        const reviewText = document.getElementById('review-text').value.trim();
        
        if (!rating) {
            showToast('Silakan berikan rating', 'warning');
            return;
        }
        
        if (!reviewText) {
            showToast('Silakan tulis ulasan Anda', 'warning');
            return;
        }
        
        // Kumpulkan data form
        const formData = new FormData(this);
        
        // Tambahkan foto yang diunggah
        const photoFiles = document.querySelectorAll('.review-photo-input')[0].files;
        for (let i = 0; i < photoFiles.length; i++) {
            formData.append('photos[]', photoFiles[i]);
        }
        
        // Tampilkan spinner
        showSpinner();
        
        // Kirim data ke server menggunakan fetch API
        fetch('api/submit_review.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Sembunyikan spinner
            hideSpinner();
            
            if (data.success) {
                // Reset form
                reviewForm.reset();
                document.querySelector('.review-photo-preview').innerHTML = '';
                
                // Tampilkan toast notification
                showToast('Ulasan berhasil dikirim', 'success');
                
                // Refresh halaman setelah beberapa detik
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showToast(data.message || 'Gagal mengirim ulasan', 'error');
            }
        })
        .catch(error => {
            // Sembunyikan spinner
            hideSpinner();
            
            // Tampilkan toast notification error
            showToast('Terjadi kesalahan saat mengirim ulasan', 'error');
            console.error('Error submitting review:', error);
        });
    });
    
    // Tampilkan lebih banyak ulasan
    const loadMoreButton = document.getElementById('load-more-reviews');
    if (loadMoreButton) {
        loadMoreButton.addEventListener('click', function() {
            const restaurantId = this.dataset.restaurantId;
            const page = parseInt(this.dataset.page) || 1;
            const nextPage = page + 1;
            
            loadMoreReviews(restaurantId, nextPage);
        });
    }
}

/**
 * Muat lebih banyak ulasan
 * @param {string} restaurantId - ID restoran
 * @param {number} page - Nomor halaman
 */
function loadMoreReviews(restaurantId, page) {
    // Tampilkan spinner
    showSpinner();
    
    // Kirim permintaan ke server
    fetch(`api/get_reviews.php?restaurant_id=${restaurantId}&page=${page}`)
    .then(response => response.json())
    .then(data => {
        // Sembunyikan spinner
        hideSpinner();
        
        if (data.success) {
            // Tambahkan ulasan ke daftar
            const reviewList = document.querySelector('.review-list');
            
            data.reviews.forEach(review => {
                const reviewElement = createReviewElement(review);
                reviewList.appendChild(reviewElement);
            });
            
            // Update nomor halaman pada tombol
            const loadMoreButton = document.getElementById('load-more-reviews');
            loadMoreButton.dataset.page = page;
            
            // Sembunyikan tombol jika tidak ada lagi ulasan
            if (!data.has_more) {
                loadMoreButton.style.display = 'none';
            }
        } else {
            showToast(data.message || 'Gagal memuat ulasan', 'error');
        }
    })
    .catch(error => {
        // Sembunyikan spinner
        hideSpinner();
        
        // Tampilkan toast notification error
        showToast('Terjadi kesalahan saat memuat ulasan', 'error');
        console.error('Error loading reviews:', error);
    });
}

/**
 * Buat elemen ulasan
 * @param {Object} review - Data ulasan
 * @returns {Element} Elemen ulasan
 */
function createReviewElement(review) {
    const reviewElement = document.createElement('div');
    reviewElement.className = 'review-item';
    
    // Format tanggal
    const reviewDate = new Date(review.created_at);
    const formattedDate = reviewDate.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // Buat HTML untuk rating
    let ratingHTML = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= review.rating) {
            ratingHTML += '<i class="fas fa-star"></i>';
        } else {
            ratingHTML += '<i class="far fa-star"></i>';
        }
    }
    
    // Buat HTML untuk foto
    let photosHTML = '';
    if (review.photos && review.photos.length > 0) {
        photosHTML = '<div class="review-photos">';
        review.photos.forEach(photo => {
            photosHTML += `<img src="${photo}" class="review-photo" data-src="${photo}" alt="Review Photo">`;
        });
        photosHTML += '</div>';
    }
    
    // Isi elemen ulasan
    reviewElement.innerHTML = `
        <div class="review-header">
            <div class="review-user">
                <img src="${review.user_avatar || 'images/default-avatar.png'}" class="review-user-avatar" alt="${review.user_name}">
                <div>
                    <div class="review-user-name">${review.user_name}</div>
                    <div class="review-date">${formattedDate}</div>
                </div>
            </div>
            <div class="review-rating">
                ${ratingHTML}
            </div>
        </div>
        <div class="review-content">${review.content}</div>
        ${photosHTML}
        <div class="review-actions">
            <div class="review-helpful">
                <button class="review-helpful-button" data-review-id="${review.id}">
                    <i class="far fa-thumbs-up"></i> Membantu
                </button>
                <span class="review-helpful-count">${review.helpful_count || 0}</span>
            </div>
            <div class="review-share">
                <button class="review-share-button" data-review-id="${review.id}">
                    <i class="fas fa-share-alt"></i> Bagikan
                </button>
            </div>
        </div>
    `;
    
    // Tambahkan event listener
    const helpfulButton = reviewElement.querySelector('.review-helpful-button');
    helpfulButton.addEventListener('click', function() {
        toggleHelpful(review.id, this);
    });
    
    const shareButton = reviewElement.querySelector('.review-share-button');
    shareButton.addEventListener('click', function() {
        shareReview(review.id);
    });
    
    const photoElements = reviewElement.querySelectorAll('.review-photo');
    photoElements.forEach(photo => {
        photo.addEventListener('click', function() {
            openPhotoViewer(this.dataset.src);
        });
    });
    
    return reviewElement;
}

/**
 * Inisialisasi unggah foto
 */
function initializePhotoUpload() {
    const photoInput = document.querySelector('.review-photo-input');
    if (!photoInput) return;
    
    photoInput.addEventListener('change', function() {
        const previewContainer = document.querySelector('.review-photo-preview');
        previewContainer.innerHTML = '';
        
        if (this.files.length > 0) {
            for (let i = 0; i < this.files.length; i++) {
                const file = this.files[i];
                
                // Validasi tipe file
                if (!file.type.match('image.*')) {
                    continue;
                }
                
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'review-photo-preview-item';
                    
                    previewItem.innerHTML = `
                        <img src="${e.target.result}" alt="Preview">
                        <div class="remove-photo" data-index="${i}">
                            <i class="fas fa-times"></i>
                        </div>
                    `;
                    
                    previewContainer.appendChild(previewItem);
                    
                    // Tambahkan event listener untuk tombol hapus
                    const removeButton = previewItem.querySelector('.remove-photo');
                    removeButton.addEventListener('click', function() {
                        previewItem.remove();
                        
                        // Reset input file jika semua preview dihapus
                        if (previewContainer.children.length === 0) {
                            photoInput.value = '';
                        }
                    });
                };
                
                reader.readAsDataURL(file);
            }
        }
    });
}

/**
 * Inisialisasi penampil foto
 */
function initializePhotoViewer() {
    // Buat elemen penampil foto jika belum ada
    if (!document.querySelector('.photo-viewer')) {
        const photoViewer = document.createElement('div');
        photoViewer.className = 'photo-viewer';
        photoViewer.innerHTML = `
            <div class="photo-viewer-overlay"></div>
            <div class="photo-viewer-content">
                <img src="" alt="Full size photo">
                <button class="photo-viewer-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(photoViewer);
        
        // Tambahkan event listener untuk menutup penampil
        const closeButton = photoViewer.querySelector('.photo-viewer-close');
        const overlay = photoViewer.querySelector('.photo-viewer-overlay');
        
        closeButton.addEventListener('click', closePhotoViewer);
        overlay.addEventListener('click', closePhotoViewer);
    }
    
    // Tambahkan event listener untuk foto ulasan
    const reviewPhotos = document.querySelectorAll('.review-photo');
    reviewPhotos.forEach(photo => {
        photo.addEventListener('click', function() {
            openPhotoViewer(this.dataset.src || this.src);
        });
    });
}

/**
 * Buka penampil foto
 * @param {string} src - URL gambar
 */
function openPhotoViewer(src) {
    const photoViewer = document.querySelector('.photo-viewer');
    const photoImg = photoViewer.querySelector('img');
    
    photoImg.src = src;
    photoViewer.classList.add('active');
    document.body.style.overflow = 'hidden';
}

/**
 * Tutup penampil foto
 */
function closePhotoViewer() {
    const photoViewer = document.querySelector('.photo-viewer');
    photoViewer.classList.remove('active');
    document.body.style.overflow = '';
}

/**
 * Inisialisasi berbagi sosial
 */
function initializeSocialSharing() {
    const shareButtons = document.querySelectorAll('.social-sharing-button');
    if (!shareButtons.length) return;
    
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const platform = this.dataset.platform;
            const url = window.location.href;
            const title = document.title;
            
            shareContent(platform, url, title);
        });
    });
    
    // Tambahkan event listener untuk tombol berbagi ulasan
    const reviewShareButtons = document.querySelectorAll('.review-share-button');
    reviewShareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const reviewId = this.dataset.reviewId;
            shareReview(reviewId);
        });
    });
}

/**
 * Berbagi konten ke platform sosial
 * @param {string} platform - Platform sosial
 * @param {string} url - URL yang akan dibagikan
 * @param {string} title - Judul konten
 */
function shareContent(platform, url, title) {
    let shareUrl = '';
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(title + ' ' + url)}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            break;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

/**
 * Berbagi ulasan
 * @param {string} reviewId - ID ulasan
 */
function shareReview(reviewId) {
    // Buat modal berbagi
    const shareModal = document.createElement('div');
    shareModal.className = 'modal fade';
    shareModal.id = 'shareModal';
    shareModal.setAttribute('tabindex', '-1');
    
    shareModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bagikan Ulasan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Bagikan ulasan ini ke:</p>
                    <div class="social-sharing">
                        <a href="#" class="social-sharing-button social-sharing-facebook" data-platform="facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-sharing-button social-sharing-twitter" data-platform="twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-sharing-button social-sharing-whatsapp" data-platform="whatsapp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="#" class="social-sharing-button social-sharing-telegram" data-platform="telegram">
                            <i class="fab fa-telegram-plane"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(shareModal);
    
    // Tampilkan modal
    const modal = new bootstrap.Modal(shareModal);
    modal.show();
    
    // Tambahkan event listener untuk tombol berbagi
    const shareButtons = shareModal.querySelectorAll('.social-sharing-button');
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const platform = this.dataset.platform;
            const url = `${window.location.origin}/review.php?id=${reviewId}`;
            const title = 'Lihat ulasan ini di KikaZen Ship';
            
            shareContent(platform, url, title);
            
            // Tutup modal
            modal.hide();
        });
    });
    
    // Hapus modal setelah ditutup
    shareModal.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Inisialisasi tombol membantu
 */
function initializeHelpfulButtons() {
    const helpfulButtons = document.querySelectorAll('.review-helpful-button');
    if (!helpfulButtons.length) return;
    
    helpfulButtons.forEach(button => {
        button.addEventListener('click', function() {
            const reviewId = this.dataset.reviewId;
            toggleHelpful(reviewId, this);
        });
    });
}

/**
 * Toggle status membantu pada ulasan
 * @param {string} reviewId - ID ulasan
 * @param {Element} button - Tombol membantu
 */
function toggleHelpful(reviewId, button) {
    // Periksa apakah tombol sudah aktif
    const isActive = button.classList.contains('active');
    
    // Kirim data ke server
    fetch('api/toggle_helpful.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            review_id: reviewId,
            helpful: !isActive
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            if (!isActive) {
                button.classList.add('active');
                button.querySelector('i').className = 'fas fa-thumbs-up';
            } else {
                button.classList.remove('active');
                button.querySelector('i').className = 'far fa-thumbs-up';
            }
            
            // Update jumlah
            const countElement = button.nextElementSibling;
            if (countElement) {
                countElement.textContent = data.helpful_count || 0;
            }
        } else {
            showToast(data.message || 'Gagal memperbarui status membantu', 'error');
        }
    })
    .catch(error => {
        showToast('Terjadi kesalahan saat memperbarui status membantu', 'error');
        console.error('Error toggling helpful:', error);
    });
}
