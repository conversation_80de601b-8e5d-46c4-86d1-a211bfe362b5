/**
 * KikaZen Ship - Fungsionalitas Chat
 * JavaScript untuk chat interaktif antara pengguna, pemilik restoran, pengemudi, dan admin
 */

// Tunggu hingga dokumen siap
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi komponen chat
    initializeChat();

    // Siapkan polling untuk pesan baru
    setupMessagePolling();

    // Tambahkan event listener untuk aksi chat
    setupChatEventListeners();
});

/**
 * Inisialisasi komponen chat
 */
function initializeChat() {
    // Inisialisasi daftar ruang chat jika ada
    const chatRoomsList = document.getElementById('chat-rooms-list');
    if (chatRoomsList) {
        loadChatRooms();
    }

    // Inisialisasi pesan chat jika ada
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        const roomId = chatMessages.dataset.roomId;
        if (roomId) {
            loadChatMessages(roomId);
            markRoomAsRead(roomId);
        }
    }

    // Inisialisasi form pesan jika ada
    const messageForm = document.getElementById('message-form');
    if (messageForm) {
        messageForm.addEventListener('submit', sendMessage);
    }

    // Inisialisasi upload file jika ada
    const fileUpload = document.getElementById('file-upload');
    if (fileUpload) {
        fileUpload.addEventListener('change', handleFileUpload);
    }

    // Inisialisasi berbagi lokasi jika ada
    const locationButton = document.getElementById('share-location');
    if (locationButton) {
        locationButton.addEventListener('click', shareLocation);
    }
}

/**
 * Siapkan polling untuk pesan baru
 */
function setupMessagePolling() {
    // Periksa pesan baru setiap 5 detik
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        const roomId = chatMessages.dataset.roomId;
        if (roomId) {
            // Dapatkan timestamp pesan terakhir
            const lastMessageTime = getLastMessageTimestamp();

            // Siapkan interval untuk memeriksa pesan baru
            setInterval(() => {
                checkForNewMessages(roomId, lastMessageTime);
            }, 5000);
        }
    }

    // Periksa pesan yang belum dibaca di daftar ruang setiap 10 detik
    const chatRoomsList = document.getElementById('chat-rooms-list');
    if (chatRoomsList) {
        setInterval(() => {
            updateUnreadCounts();
        }, 10000);
    }
}

/**
 * Siapkan event listener untuk aksi chat
 */
function setupChatEventListeners() {
    // Tambahkan event listener untuk pemilihan ruang
    document.addEventListener('click', function(e) {
        if (e.target.closest('.chat-room-item')) {
            const roomItem = e.target.closest('.chat-room-item');
            const roomId = roomItem.dataset.roomId;
            selectChatRoom(roomId);
        }
    });

    // Tambahkan event listener untuk pemilih emoji
    const emojiButton = document.getElementById('emoji-button');
    if (emojiButton) {
        emojiButton.addEventListener('click', toggleEmojiPicker);
    }

    // Tambahkan event listener untuk pencarian pesan
    const searchInput = document.getElementById('message-search');
    if (searchInput) {
        searchInput.addEventListener('input', searchMessages);
    }
}

/**
 * Muat ruang chat untuk pengguna saat ini
 */
function loadChatRooms() {
    const chatRoomsList = document.getElementById('chat-rooms-list');
    if (!chatRoomsList) return;

    // Tampilkan indikator loading
    chatRoomsList.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Memuat...</span></div></div>';

    // Ambil ruang chat dari API
    fetch('../api/chat.php?action=get_rooms')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayChatRooms(data.data);
            } else {
                chatRoomsList.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error memuat ruang chat:', error);
            chatRoomsList.innerHTML = '<div class="alert alert-danger">Gagal memuat ruang chat. Silakan coba lagi nanti.</div>';
        });
}

/**
 * Tampilkan ruang chat dalam daftar
 */
function displayChatRooms(rooms) {
    const chatRoomsList = document.getElementById('chat-rooms-list');
    if (!chatRoomsList) return;

    if (rooms.length === 0) {
        chatRoomsList.innerHTML = '<div class="text-center p-3">Tidak ada ruang chat ditemukan</div>';
        return;
    }

    let html = '';
    rooms.forEach(room => {
        const unreadBadge = room.unread_count > 0 ? `<span class="badge bg-danger rounded-pill">${room.unread_count}</span>` : '';
        const lastMessageTime = new Date(room.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        let roomTitle = '';
        if (room.room_type === 'order') {
            roomTitle = `Pesanan #${room.order_id}`;
        } else if (room.room_type === 'complaint') {
            roomTitle = `Dukungan Keluhan`;
        } else {
            roomTitle = `Chat Dukungan`;
        }

        let participantInfo = '';
        if (room.customer_name) participantInfo += `<div class="small text-muted">Pelanggan: ${room.customer_name}</div>`;
        if (room.restaurant_name) participantInfo += `<div class="small text-muted">Restoran: ${room.restaurant_name}</div>`;
        if (room.driver_name) participantInfo += `<div class="small text-muted">Pengemudi: ${room.driver_name}</div>`;

        html += `
            <div class="chat-room-item list-group-item list-group-item-action d-flex justify-content-between align-items-start ${room.status === 'closed' ? 'bg-light' : ''}" data-room-id="${room.room_id}">
                <div class="ms-2 me-auto">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${roomTitle}</h6>
                        <small>${lastMessageTime}</small>
                    </div>
                    ${participantInfo}
                    <p class="mb-1 text-truncate">${room.last_message || 'Belum ada pesan'}</p>
                </div>
                ${unreadBadge}
            </div>
        `;
    });

    chatRoomsList.innerHTML = html;
}

/**
 * Muat pesan chat untuk ruang tertentu
 */
function loadChatMessages(roomId) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // Tampilkan indikator loading
    chatMessages.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Memuat...</span></div></div>';

    // Ambil pesan dari API
    fetch(`../api/chat.php?action=get_messages&room_id=${roomId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayChatMessages(data.data.messages, data.data.participants);
            } else {
                chatMessages.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error memuat pesan:', error);
            chatMessages.innerHTML = '<div class="alert alert-danger">Gagal memuat pesan. Silakan coba lagi nanti.</div>';
        });
}

/**
 * Tampilkan pesan chat di jendela chat
 */
function displayChatMessages(messages, participants) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    if (messages.length === 0) {
        chatMessages.innerHTML = '<div class="text-center p-3">Belum ada pesan</div>';
        return;
    }

    let html = '';
    let currentDate = '';

    messages.forEach(message => {
        // Periksa apakah perlu menambahkan pemisah tanggal
        const messageDate = new Date(message.created_at).toLocaleDateString();
        if (messageDate !== currentDate) {
            html += `<div class="chat-date-separator"><span>${messageDate}</span></div>`;
            currentDate = messageDate;
        }

        // Tentukan nama pengirim berdasarkan tipe pengirim
        let senderName = 'Tidak dikenal';
        switch (message.sender_type) {
            case 'customer':
                senderName = message.customer_name || 'Pelanggan';
                break;
            case 'restaurant_owner':
                senderName = message.restaurant_owner_name || 'Restoran';
                break;
            case 'driver':
                senderName = message.driver_name || 'Pengemudi';
                break;
            case 'admin':
                senderName = message.admin_name || 'Admin';
                break;
            case 'system':
                senderName = 'Sistem';
                break;
        }

        // Tentukan apakah ini pesan dari pengguna saat ini
        const isCurrentUser = (message.sender_type === getCurrentUserType() && message.sender_id === getCurrentUserId());
        const messageClass = isCurrentUser ? 'chat-message-outgoing' : 'chat-message-incoming';

        // Format pesan berdasarkan tipe
        let messageContent = '';
        switch (message.message_type) {
            case 'text':
                messageContent = `<p class="mb-0">${message.message_text}</p>`;
                break;
            case 'image':
                messageContent = `<img src="${message.message_text}" class="img-fluid rounded" alt="Gambar yang dibagikan">`;
                break;
            case 'location':
                const [lat, lng] = message.message_text.split(',');
                messageContent = `
                    <div class="location-message">
                        <p class="mb-1">Lokasi yang dibagikan:</p>
                        <a href="https://maps.google.com/?q=${lat},${lng}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-map-marker-alt me-1"></i> Lihat di peta
                        </a>
                    </div>
                `;
                break;
            case 'system':
                messageContent = `<p class="mb-0 fst-italic">${message.message_text}</p>`;
                break;
        }

        // Format waktu
        const messageTime = new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        // Tambahkan pesan ke HTML
        if (message.message_type === 'system') {
            html += `
                <div class="chat-message chat-message-system">
                    ${messageContent}
                    <small class="text-muted">${messageTime}</small>
                </div>
            `;
        } else {
            html += `
                <div class="chat-message ${messageClass}">
                    <div class="chat-message-header">
                        <span class="chat-message-sender">${senderName}</span>
                        <small class="chat-message-time">${messageTime}</small>
                    </div>
                    <div class="chat-message-content">
                        ${messageContent}
                    </div>
                </div>
            `;
        }
    });

    chatMessages.innerHTML = html;

    // Scroll ke bawah
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Kirim pesan baru
 */
function sendMessage(event) {
    event.preventDefault();

    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    // Coba dapatkan room ID dari dataset atau dari input hidden
    let roomId = document.getElementById('chat-messages').dataset.roomId;

    // Jika tidak ada, coba dapatkan dari input hidden
    if (!roomId) {
        const roomIdInput = document.getElementById('room-id');
        if (roomIdInput) {
            roomId = roomIdInput.value;
        }
    }

    // Log untuk debugging
    console.log('Room ID:', roomId);

    if (!messageInput.value.trim()) return;

    const messageData = {
        room_id: roomId,
        message: messageInput.value.trim(),
        message_type: 'text'
    };

    // Nonaktifkan form saat mengirim
    const submitButton = messageForm.querySelector('button[type="submit"]');
    submitButton.disabled = true;

    // Log data untuk debugging
    console.log('Sending message data:', messageData);

    // Kirim pesan ke API
    fetch('../api/chat.php?action=send_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Bersihkan input
            messageInput.value = '';

            // Tambahkan pesan ke chat
            appendNewMessage(data.data);
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error mengirim pesan:', error);
        alert('Gagal mengirim pesan. Silakan coba lagi.');
    })
    .finally(() => {
        // Aktifkan kembali form
        submitButton.disabled = false;
    });
}

/**
 * Tambahkan pesan baru ke chat
 */
function appendNewMessage(message) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // Buat elemen pesan
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${message.sender_type === getCurrentUserType() && message.sender_id === getCurrentUserId() ? 'chat-message-outgoing' : 'chat-message-incoming'}`;

    // Format waktu
    const messageTime = new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    // Atur konten pesan berdasarkan tipe
    let messageContent = '';
    switch (message.message_type) {
        case 'text':
            messageContent = `<p class="mb-0">${message.message_text}</p>`;
            break;
        case 'image':
            messageContent = `<img src="${message.message_text}" class="img-fluid rounded" alt="Gambar yang dibagikan">`;
            break;
        case 'location':
            const [lat, lng] = message.message_text.split(',');
            messageContent = `
                <div class="location-message">
                    <p class="mb-1">Lokasi yang dibagikan:</p>
                    <a href="https://maps.google.com/?q=${lat},${lng}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-map-marker-alt me-1"></i> Lihat di peta
                    </a>
                </div>
            `;
            break;
    }

    // Atur HTML pesan
    messageDiv.innerHTML = `
        <div class="chat-message-header">
            <span class="chat-message-sender">${message.sender_name}</span>
            <small class="chat-message-time">${messageTime}</small>
        </div>
        <div class="chat-message-content">
            ${messageContent}
        </div>
    `;

    // Tambahkan ke chat dan scroll ke bawah
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Dapatkan tipe pengguna saat ini (customer, restaurant_owner, driver, admin)
 */
function getCurrentUserType() {
    return document.body.dataset.userType || '';
}

/**
 * Dapatkan ID pengguna saat ini
 */
function getCurrentUserId() {
    return parseInt(document.body.dataset.userId || '0');
}

/**
 * Dapatkan timestamp pesan terakhir di chat
 */
function getLastMessageTimestamp() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return null;

    const lastMessage = chatMessages.querySelector('.chat-message:last-child');
    if (!lastMessage) return null;

    const timeElement = lastMessage.querySelector('.chat-message-time');
    if (!timeElement) return null;

    return timeElement.dataset.timestamp || null;
}

/**
 * Periksa pesan baru sejak pemeriksaan terakhir
 */
function checkForNewMessages(roomId, lastTimestamp) {
    fetch(`../api/chat.php?action=get_new_messages&room_id=${roomId}&since=${lastTimestamp}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.messages.length > 0) {
                data.data.messages.forEach(message => {
                    appendNewMessage(message);
                });
            }
        })
        .catch(error => {
            console.error('Error memeriksa pesan baru:', error);
        });
}

/**
 * Tandai ruang sebagai telah dibaca
 */
function markRoomAsRead(roomId) {
    fetch(`../api/chat.php?action=mark_as_read&room_id=${roomId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Perbarui jumlah yang belum dibaca di daftar ruang
            const roomItem = document.querySelector(`.chat-room-item[data-room-id="${roomId}"]`);
            if (roomItem) {
                const badge = roomItem.querySelector('.badge');
                if (badge) {
                    badge.remove();
                }
            }
        }
    })
    .catch(error => {
        console.error('Error menandai ruang sebagai telah dibaca:', error);
    });
}

/**
 * Perbarui jumlah yang belum dibaca untuk semua ruang
 */
function updateUnreadCounts() {
    fetch('../api/chat.php?action=get_unread_counts')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateUnreadBadges(data.data);
            }
        })
        .catch(error => {
            console.error('Error memperbarui jumlah yang belum dibaca:', error);
        });
}

/**
 * Perbarui badge yang belum dibaca di daftar ruang
 */
function updateUnreadBadges(unreadCounts) {
    const roomItems = document.querySelectorAll('.chat-room-item');
    roomItems.forEach(item => {
        const roomId = item.dataset.roomId;
        const count = unreadCounts[roomId] || 0;

        // Hapus badge yang ada
        const existingBadge = item.querySelector('.badge');
        if (existingBadge) {
            existingBadge.remove();
        }

        // Tambahkan badge baru jika diperlukan
        if (count > 0) {
            const badge = document.createElement('span');
            badge.className = 'badge bg-danger rounded-pill';
            badge.textContent = count;
            item.appendChild(badge);
        }
    });
}
