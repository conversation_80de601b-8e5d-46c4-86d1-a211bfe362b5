/**
 * KikaZen Ship - Personalization Features
 * JavaScript untuk fitur personalisasi
 */

// Tunggu hingga dokumen siap
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi komponen
    initializeRecommendations();
    initializePreferenceTags();
    initializeFavorites();
    initializeRecentOrders();
    initializePersonalizationSettings();
});

/**
 * Inisialisasi rekomendasi restoran
 */
function initializeRecommendations() {
    const recommendationContainer = document.querySelector('.recommendation-slider');
    if (!recommendationContainer) return;
    
    // Jika menggunakan Swiper untuk slider
    if (typeof Swiper !== 'undefined' && recommendationContainer.classList.contains('swiper')) {
        new Swiper('.recommendation-slider', {
            slidesPerView: 1,
            spaceBetween: 10,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                },
            }
        });
    }
    
    // Tambahkan event listener untuk kartu rekomendasi
    const recommendationCards = document.querySelectorAll('.recommendation-card');
    recommendationCards.forEach(card => {
        card.addEventListener('click', function() {
            const restaurantId = this.dataset.restaurantId;
            if (restaurantId) {
                window.location.href = `restaurant_detail.php?id=${restaurantId}`;
            }
        });
    });
}

/**
 * Inisialisasi tag preferensi
 */
function initializePreferenceTags() {
    const preferenceTags = document.querySelectorAll('.preference-tag');
    if (!preferenceTags.length) return;
    
    preferenceTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const tagId = this.dataset.tagId;
            const isSelected = this.classList.toggle('selected');
            
            // Simpan preferensi ke server
            savePreference(tagId, isSelected);
        });
    });
}

/**
 * Simpan preferensi ke server
 * @param {string} tagId - ID tag preferensi
 * @param {boolean} isSelected - Status seleksi tag
 */
function savePreference(tagId, isSelected) {
    // Tampilkan spinner
    showSpinner();
    
    // Kirim data ke server menggunakan fetch API
    fetch('api/save_preference.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tag_id: tagId,
            selected: isSelected
        })
    })
    .then(response => response.json())
    .then(data => {
        // Sembunyikan spinner
        hideSpinner();
        
        // Tampilkan toast notification
        if (data.success) {
            showToast('Preferensi berhasil disimpan', 'success');
        } else {
            showToast('Gagal menyimpan preferensi', 'error');
        }
    })
    .catch(error => {
        // Sembunyikan spinner
        hideSpinner();
        
        // Tampilkan toast notification error
        showToast('Terjadi kesalahan saat menyimpan preferensi', 'error');
        console.error('Error saving preference:', error);
    });
}

/**
 * Inisialisasi restoran favorit
 */
function initializeFavorites() {
    const favoriteButtons = document.querySelectorAll('.favorite-toggle');
    if (!favoriteButtons.length) return;
    
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const restaurantId = this.dataset.restaurantId;
            const isFavorite = this.classList.contains('active');
            
            // Toggle status favorit
            toggleFavorite(restaurantId, !isFavorite, this);
        });
    });
    
    // Tambahkan event listener untuk tombol hapus favorit
    const removeFavoriteButtons = document.querySelectorAll('.remove-favorite');
    removeFavoriteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const restaurantId = this.dataset.restaurantId;
            const listItem = this.closest('.favorite-restaurant-item');
            
            // Hapus dari favorit
            toggleFavorite(restaurantId, false, null, listItem);
        });
    });
}

/**
 * Toggle status favorit restoran
 * @param {string} restaurantId - ID restoran
 * @param {boolean} setFavorite - Set sebagai favorit atau tidak
 * @param {Element} button - Tombol favorit (opsional)
 * @param {Element} listItem - Item daftar favorit (opsional)
 */
function toggleFavorite(restaurantId, setFavorite, button = null, listItem = null) {
    // Tampilkan spinner
    showSpinner();
    
    // Kirim data ke server menggunakan fetch API
    fetch('api/toggle_favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            restaurant_id: restaurantId,
            is_favorite: setFavorite
        })
    })
    .then(response => response.json())
    .then(data => {
        // Sembunyikan spinner
        hideSpinner();
        
        if (data.success) {
            // Update UI
            if (button) {
                if (setFavorite) {
                    button.classList.add('active');
                    button.innerHTML = '<i class="fas fa-heart"></i>';
                    button.setAttribute('title', 'Hapus dari Favorit');
                } else {
                    button.classList.remove('active');
                    button.innerHTML = '<i class="far fa-heart"></i>';
                    button.setAttribute('title', 'Tambahkan ke Favorit');
                }
            }
            
            // Jika menghapus dari daftar favorit
            if (listItem && !setFavorite) {
                // Animasi fade out
                listItem.style.opacity = '0';
                listItem.style.transform = 'translateX(20px)';
                listItem.style.transition = 'opacity 0.3s, transform 0.3s';
                
                // Hapus elemen setelah animasi selesai
                setTimeout(() => {
                    listItem.remove();
                    
                    // Periksa apakah daftar favorit kosong
                    const favoritesList = document.querySelector('.favorite-restaurants-list');
                    if (favoritesList && favoritesList.children.length === 0) {
                        favoritesList.innerHTML = '<div class="text-center text-muted py-3">Anda belum memiliki restoran favorit</div>';
                    }
                }, 300);
            }
            
            // Tampilkan toast notification
            showToast(
                setFavorite ? 'Restoran ditambahkan ke favorit' : 'Restoran dihapus dari favorit',
                'success'
            );
        } else {
            showToast('Gagal memperbarui status favorit', 'error');
        }
    })
    .catch(error => {
        // Sembunyikan spinner
        hideSpinner();
        
        // Tampilkan toast notification error
        showToast('Terjadi kesalahan saat memperbarui status favorit', 'error');
        console.error('Error toggling favorite:', error);
    });
}

/**
 * Inisialisasi pesanan terbaru
 */
function initializeRecentOrders() {
    const reorderButtons = document.querySelectorAll('.reorder-button');
    if (!reorderButtons.length) return;
    
    reorderButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const orderId = this.dataset.orderId;
            reorderItems(orderId);
        });
    });
}

/**
 * Pesan ulang item dari pesanan sebelumnya
 * @param {string} orderId - ID pesanan
 */
function reorderItems(orderId) {
    // Tampilkan spinner
    showSpinner();
    
    // Kirim data ke server menggunakan fetch API
    fetch('api/reorder.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId
        })
    })
    .then(response => response.json())
    .then(data => {
        // Sembunyikan spinner
        hideSpinner();
        
        if (data.success) {
            // Redirect ke halaman keranjang
            window.location.href = 'cart.php';
        } else {
            showToast(data.message || 'Gagal menambahkan item ke keranjang', 'error');
        }
    })
    .catch(error => {
        // Sembunyikan spinner
        hideSpinner();
        
        // Tampilkan toast notification error
        showToast('Terjadi kesalahan saat memproses pesanan ulang', 'error');
        console.error('Error reordering items:', error);
    });
}

/**
 * Inisialisasi pengaturan personalisasi
 */
function initializePersonalizationSettings() {
    const personalizationForm = document.getElementById('personalization-form');
    if (!personalizationForm) return;
    
    personalizationForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Kumpulkan data form
        const formData = new FormData(this);
        
        // Tampilkan spinner
        showSpinner();
        
        // Kirim data ke server menggunakan fetch API
        fetch('api/save_personalization.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Sembunyikan spinner
            hideSpinner();
            
            if (data.success) {
                showToast('Pengaturan personalisasi berhasil disimpan', 'success');
            } else {
                showToast(data.message || 'Gagal menyimpan pengaturan personalisasi', 'error');
            }
        })
        .catch(error => {
            // Sembunyikan spinner
            hideSpinner();
            
            // Tampilkan toast notification error
            showToast('Terjadi kesalahan saat menyimpan pengaturan', 'error');
            console.error('Error saving personalization settings:', error);
        });
    });
}
