/**
 * KikaZen Ship - User Experience Enhancements
 * JavaScript untuk meningkatkan pengalaman pengguna
 */

// Tunggu hingga dokumen siap
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi komponen
    initializeToasts();
    initializeLoadingSpinner();
    initializeAnimations();
    initializeResponsiveTables();
    initializeLazyLoading();
    initializeFormValidation();
    initializeSearchEnhancements();
    initializeScrollToTop();
    
    // Tambahkan event listener untuk dark mode toggle jika ada
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkMode);
    }
});

/**
 * Inisialisasi toast notifications
 */
function initializeToasts() {
    // Buat container untuk toast jika belum ada
    if (!document.querySelector('.toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    // Tambahkan event listener untuk tombol close pada toast
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('toast-close')) {
            const toast = e.target.closest('.toast');
            hideToast(toast);
        }
    });
}

/**
 * Tampilkan toast notification
 * @param {string} message - Pesan yang akan ditampilkan
 * @param {string} type - Tipe toast (success, error, warning, info)
 * @param {number} duration - Durasi tampilan dalam milidetik
 */
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.querySelector('.toast-container');
    
    // Buat elemen toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // Tentukan ikon berdasarkan tipe
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    
    // Isi toast
    toast.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-${icon} me-2"></i>
            <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
            <button type="button" class="btn-close toast-close" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    // Tambahkan ke container
    toastContainer.appendChild(toast);
    
    // Tampilkan toast dengan animasi
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Sembunyikan toast setelah durasi tertentu
    if (duration > 0) {
        setTimeout(() => {
            hideToast(toast);
        }, duration);
    }
}

/**
 * Sembunyikan toast notification
 * @param {Element} toast - Elemen toast yang akan disembunyikan
 */
function hideToast(toast) {
    toast.classList.remove('show');
    
    // Hapus dari DOM setelah animasi selesai
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

/**
 * Inisialisasi loading spinner
 */
function initializeLoadingSpinner() {
    // Buat elemen spinner jika belum ada
    if (!document.querySelector('.spinner-overlay')) {
        const spinnerOverlay = document.createElement('div');
        spinnerOverlay.className = 'spinner-overlay';
        spinnerOverlay.innerHTML = '<div class="spinner"></div>';
        document.body.appendChild(spinnerOverlay);
    }
    
    // Tambahkan event listener untuk form submissions
    document.addEventListener('submit', function(e) {
        const form = e.target;
        
        // Periksa apakah form memiliki atribut data-show-spinner="true"
        if (form.getAttribute('data-show-spinner') === 'true') {
            showSpinner();
        }
    });
}

/**
 * Tampilkan loading spinner
 */
function showSpinner() {
    const spinner = document.querySelector('.spinner-overlay');
    if (spinner) {
        spinner.classList.add('show');
    }
}

/**
 * Sembunyikan loading spinner
 */
function hideSpinner() {
    const spinner = document.querySelector('.spinner-overlay');
    if (spinner) {
        spinner.classList.remove('show');
    }
}

/**
 * Inisialisasi animasi
 */
function initializeAnimations() {
    // Animasi fade-in untuk elemen dengan class 'fade-in'
    const fadeElements = document.querySelectorAll('.fade-in');
    
    if (fadeElements.length > 0) {
        // Buat observer untuk animasi saat elemen terlihat
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        // Observe semua elemen fade-in
        fadeElements.forEach(el => {
            observer.observe(el);
        });
    }
}

/**
 * Inisialisasi tabel responsif
 */
function initializeResponsiveTables() {
    const tables = document.querySelectorAll('.table-responsive-stack');
    
    tables.forEach(table => {
        const thElements = table.querySelectorAll('thead th');
        const labels = Array.from(thElements).map(th => th.textContent);
        
        const tdElements = table.querySelectorAll('tbody td');
        tdElements.forEach((td, index) => {
            const labelIndex = index % labels.length;
            td.setAttribute('data-label', labels[labelIndex]);
        });
    });
}

/**
 * Inisialisasi lazy loading untuk gambar
 */
function initializeLazyLoading() {
    // Gunakan Intersection Observer API untuk lazy loading gambar
    if ('IntersectionObserver' in window) {
        const lazyImages = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback untuk browser yang tidak mendukung Intersection Observer
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    }
}

/**
 * Inisialisasi validasi form
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Tampilkan pesan error untuk setiap field yang tidak valid
                const invalidFields = form.querySelectorAll(':invalid');
                invalidFields.forEach(field => {
                    const feedbackElement = field.nextElementSibling;
                    if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                        feedbackElement.style.display = 'block';
                    }
                });
                
                // Fokus ke field pertama yang tidak valid
                if (invalidFields.length > 0) {
                    invalidFields[0].focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * Inisialisasi peningkatan pencarian
 */
function initializeSearchEnhancements() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        // Tambahkan clear button
        const wrapper = document.createElement('div');
        wrapper.className = 'position-relative';
        
        const clearButton = document.createElement('button');
        clearButton.className = 'btn btn-sm position-absolute top-50 end-0 translate-middle-y';
        clearButton.innerHTML = '<i class="fas fa-times"></i>';
        clearButton.style.display = 'none';
        clearButton.type = 'button';
        
        // Sisipkan elemen baru
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        wrapper.appendChild(clearButton);
        
        // Tampilkan/sembunyikan clear button berdasarkan input
        input.addEventListener('input', function() {
            clearButton.style.display = this.value ? 'block' : 'none';
        });
        
        // Clear input saat tombol diklik
        clearButton.addEventListener('click', function() {
            input.value = '';
            input.focus();
            this.style.display = 'none';
            
            // Trigger event untuk memperbarui hasil pencarian
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        });
    });
}

/**
 * Inisialisasi tombol scroll to top
 */
function initializeScrollToTop() {
    // Buat tombol scroll to top jika belum ada
    if (!document.querySelector('.scroll-to-top')) {
        const scrollButton = document.createElement('button');
        scrollButton.className = 'scroll-to-top';
        scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        scrollButton.setAttribute('aria-label', 'Scroll to top');
        scrollButton.style.display = 'none';
        document.body.appendChild(scrollButton);
        
        // Tampilkan/sembunyikan tombol berdasarkan posisi scroll
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollButton.style.display = 'block';
            } else {
                scrollButton.style.display = 'none';
            }
        });
        
        // Scroll ke atas saat tombol diklik
        scrollButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    
    // Simpan preferensi di localStorage
    const isDarkMode = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
    
    // Tampilkan toast
    showToast(
        isDarkMode ? 'Mode Gelap diaktifkan' : 'Mode Gelap dinonaktifkan',
        'info',
        2000
    );
}
