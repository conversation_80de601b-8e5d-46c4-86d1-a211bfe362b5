/* 
 * KikaZen Ship - Social Features CSS
 * Gaya untuk fitur sosial
 */

/* Review Section */
.reviews-section {
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.reviews-section h2 {
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.reviews-section h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #007bff;
}

.review-summary {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.review-summary-rating {
    text-align: center;
    padding-right: 1.5rem;
    margin-right: 1.5rem;
    border-right: 1px solid #dee2e6;
}

.review-summary-rating .average-rating {
    font-size: 3rem;
    font-weight: bold;
    color: #343a40;
    line-height: 1;
}

.review-summary-rating .rating-count {
    color: #6c757d;
    margin-top: 0.5rem;
}

.review-summary-bars {
    flex: 1;
}

.review-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.review-bar:last-child {
    margin-bottom: 0;
}

.review-bar-label {
    width: 30px;
    text-align: right;
    margin-right: 0.75rem;
    font-weight: 500;
}

.review-bar-outer {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.review-bar-inner {
    height: 100%;
    background-color: #ffc107;
    border-radius: 4px;
}

.review-bar-percent {
    width: 40px;
    text-align: right;
    margin-left: 0.75rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.review-list {
    margin-bottom: 2rem;
}

.review-item {
    padding: 1.5rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.review-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.review-user {
    display: flex;
    align-items: center;
}

.review-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 1rem;
}

.review-user-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.review-date {
    color: #6c757d;
    font-size: 0.85rem;
}

.review-rating {
    color: #ffc107;
    font-size: 1.25rem;
}

.review-content {
    margin-bottom: 1rem;
    color: #343a40;
    line-height: 1.5;
}

.review-photos {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.review-photo {
    width: 80px;
    height: 80px;
    border-radius: 0.25rem;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s;
}

.review-photo:hover {
    transform: scale(1.05);
}

.review-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.review-helpful {
    display: flex;
    align-items: center;
}

.review-helpful-button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    display: flex;
    align-items: center;
    transition: color 0.3s;
}

.review-helpful-button:hover {
    color: #007bff;
}

.review-helpful-button i {
    margin-right: 0.5rem;
}

.review-helpful-button.active {
    color: #007bff;
}

.review-helpful-count {
    margin-left: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.review-share {
    display: flex;
    align-items: center;
}

.review-share-button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    display: flex;
    align-items: center;
    transition: color 0.3s;
}

.review-share-button:hover {
    color: #007bff;
}

.review-share-button i {
    margin-right: 0.5rem;
}

/* Write Review Form */
.write-review-form {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.write-review-form h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    margin-bottom: 1.5rem;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #e9ecef;
    padding: 0 0.25rem;
    transition: color 0.3s;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffc107;
}

.review-photo-upload {
    margin-bottom: 1.5rem;
}

.review-photo-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.review-photo-preview-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 0.25rem;
    overflow: hidden;
}

.review-photo-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.review-photo-preview-item .remove-photo {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 0.75rem;
}

/* Social Sharing */
.social-sharing {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.social-sharing-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 1.25rem;
    transition: transform 0.3s, opacity 0.3s;
}

.social-sharing-button:hover {
    transform: translateY(-3px);
    opacity: 0.9;
}

.social-sharing-facebook {
    background-color: #3b5998;
}

.social-sharing-twitter {
    background-color: #1da1f2;
}

.social-sharing-whatsapp {
    background-color: #25d366;
}

.social-sharing-telegram {
    background-color: #0088cc;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .review-summary {
        flex-direction: column;
        align-items: stretch;
    }
    
    .review-summary-rating {
        padding-right: 0;
        margin-right: 0;
        border-right: none;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .review-header {
        flex-direction: column;
    }
    
    .review-rating {
        margin-top: 0.5rem;
    }
    
    .review-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .review-share {
        margin-top: 0.5rem;
    }
}
