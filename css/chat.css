/**
 * Kika<PERSON><PERSON> Ship - Chat Styles
 * CSS untuk tampilan chat
 */

/* Card Chat */
.chat-card {
    height: calc(100vh - 250px);
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

/* Daftar Ruang Chat */
.chat-room-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.chat-room-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.chat-room-item.active {
    background-color: rgba(0, 123, 255, 0.1);
    border-left-color: #0d6efd;
}

/* Area Pesan Chat */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
}

/* <PERSON><PERSON><PERSON><PERSON> */
.chat-date-separator {
    display: flex;
    align-items: center;
    margin: 1rem 0;
    color: #6c757d;
    font-size: 0.8rem;
}

.chat-date-separator::before,
.chat-date-separator::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #dee2e6;
}

.chat-date-separator::before {
    margin-right: 0.5rem;
}

.chat-date-separator::after {
    margin-left: 0.5rem;
}

/* <PERSON>esan Chat */
.chat-message {
    margin-bottom: 1rem;
    max-width: 80%;
    clear: both;
}

.chat-message-incoming {
    float: left;
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 0.75rem;
}

.chat-message-outgoing {
    float: right;
    background-color: #dcf8c6;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 0.75rem;
}

.chat-message-system {
    clear: both;
    text-align: center;
    margin: 1rem auto;
    color: #6c757d;
    font-size: 0.9rem;
}

.chat-message-header {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.chat-message-sender {
    font-weight: bold;
    color: #495057;
}

.chat-message-time {
    color: #6c757d;
    margin-left: 0.5rem;
}

.chat-message-content img {
    max-width: 200px;
    margin-top: 0.5rem;
}

.chat-message-content p {
    margin-bottom: 0;
}

/* Form Pesan */
#message-form {
    margin-bottom: 0.5rem;
}

/* Tombol Emoji dan Upload */
#emoji-button,
#share-location,
#upload-button {
    font-size: 1.2rem;
    padding: 0.25rem 0.5rem;
}

/* Pesan Lokasi */
.location-message {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-top: 0.5rem;
}

/* Timeline untuk Riwayat Pembaruan */
.timeline {
    position: relative;
    padding-left: 1.5rem;
    list-style: none;
    margin-bottom: 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    width: 1px;
    background-color: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item-marker {
    position: absolute;
    left: -1.5rem;
    width: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.timeline-item-marker-text {
    font-size: 0.7rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.timeline-item-marker-indicator {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    background-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.timeline-item-content {
    padding: 0 0 0 1rem;
}

/* Responsif */
@media (max-width: 768px) {
    .chat-card {
        height: calc(100vh - 200px);
    }
    
    .chat-message {
        max-width: 90%;
    }
}
