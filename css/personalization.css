/* 
 * KikaZen Ship - Personalization CSS
 * Gaya untuk fitur personalisasi
 */

/* Recommendation Section */
.recommendations-section {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.recommendations-section h2 {
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.recommendations-section h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #007bff;
}

.recommendation-slider {
    position: relative;
    padding: 0 2rem;
}

.recommendation-card {
    margin: 0 0.5rem;
    transition: transform 0.3s, box-shadow 0.3s;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 100%;
}

.recommendation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.recommendation-card .card-img-top {
    height: 160px;
    object-fit: cover;
}

.recommendation-card .card-body {
    padding: 1rem;
}

.recommendation-card .card-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-card .card-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.recommendation-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: bold;
}

/* Personalization Settings */
.personalization-settings {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.personalization-settings h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.preference-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.preference-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.preference-item label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.preference-item .form-text {
    font-size: 0.85rem;
    color: #6c757d;
}

.preference-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.preference-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.preference-tag:hover {
    background-color: #dee2e6;
}

.preference-tag.selected {
    background-color: #007bff;
    color: white;
}

.preference-tag i {
    margin-left: 0.5rem;
}

/* Recent Orders Section */
.recent-orders {
    margin-bottom: 2rem;
}

.recent-order-item {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.recent-order-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.recent-order-item .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recent-order-item .order-restaurant {
    font-weight: 500;
    font-size: 1.1rem;
}

.recent-order-item .order-date {
    color: #6c757d;
    font-size: 0.85rem;
}

.recent-order-item .order-items {
    margin-bottom: 0.5rem;
    color: #495057;
}

.recent-order-item .order-total {
    font-weight: 500;
    text-align: right;
}

.recent-order-item .order-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
}

/* Favorite Restaurants */
.favorite-restaurants {
    margin-bottom: 2rem;
}

.favorite-restaurant-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 0.75rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.favorite-restaurant-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.favorite-restaurant-item .restaurant-image {
    width: 60px;
    height: 60px;
    border-radius: 0.25rem;
    object-fit: cover;
    margin-right: 1rem;
}

.favorite-restaurant-item .restaurant-info {
    flex: 1;
}

.favorite-restaurant-item .restaurant-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.favorite-restaurant-item .restaurant-category {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.favorite-restaurant-item .restaurant-rating {
    color: #ffc107;
    font-size: 0.85rem;
}

.favorite-restaurant-item .remove-favorite {
    color: #dc3545;
    cursor: pointer;
    padding: 0.5rem;
    margin-left: 0.5rem;
    transition: color 0.3s;
}

.favorite-restaurant-item .remove-favorite:hover {
    color: #bd2130;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .recommendation-card .card-img-top {
        height: 140px;
    }
    
    .favorite-restaurant-item {
        flex-direction: column;
        text-align: center;
    }
    
    .favorite-restaurant-item .restaurant-image {
        margin-right: 0;
        margin-bottom: 0.75rem;
        width: 80px;
        height: 80px;
    }
    
    .favorite-restaurant-item .remove-favorite {
        margin-top: 0.5rem;
        margin-left: 0;
    }
}
