/* 
 * KikaZen Ship - Responsive CSS
 * Peningkatan tampilan responsif untuk semua perangkat
 */

/* Base Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --transition-speed: 0.3s;
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Responsive Typography */
body {
    font-size: 16px;
}

@media (max-width: 768px) {
    body {
        font-size: 14px;
    }
    h1 {
        font-size: 1.8rem;
    }
    h2 {
        font-size: 1.5rem;
    }
    h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    body {
        font-size: 14px;
    }
    h1 {
        font-size: 1.6rem;
    }
    h2 {
        font-size: 1.4rem;
    }
    h3 {
        font-size: 1.2rem;
    }
}

/* Improved Mobile Navigation */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
    }
    
    .dropdown-menu {
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }
}

/* Card Improvements */
.card {
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Restaurant Cards */
.restaurant-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.restaurant-card .card-img-top {
    height: 180px;
    object-fit: cover;
}

@media (max-width: 768px) {
    .restaurant-card .card-img-top {
        height: 150px;
    }
}

/* Menu Item Cards */
.menu-item-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.menu-item-card .card-img-top {
    height: 160px;
    object-fit: cover;
}

@media (max-width: 768px) {
    .menu-item-card .card-img-top {
        height: 130px;
    }
}

/* Button Improvements */
.btn {
    transition: all var(--transition-speed);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Form Improvements */
.form-control {
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
    border-radius: var(--border-radius);
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Custom Checkboxes and Radio Buttons */
.custom-control-input:checked ~ .custom-control-label::before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Improved Tables for Mobile */
@media (max-width: 768px) {
    .table-responsive-mobile {
        display: block;
        width: 100%;
        overflow-x: auto;
    }
    
    .table-responsive-stack {
        display: block;
    }
    
    .table-responsive-stack thead {
        display: none;
    }
    
    .table-responsive-stack tbody tr {
        display: block;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        padding: 0.5rem;
    }
    
    .table-responsive-stack tbody td {
        display: block;
        text-align: right;
        padding: 0.5rem;
        border: none;
    }
    
    .table-responsive-stack tbody td:before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
    }
}

/* Improved Footer for Mobile */
@media (max-width: 768px) {
    footer {
        text-align: center;
    }
    
    footer .col-md-4 {
        margin-bottom: 1.5rem;
    }
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-speed), visibility var(--transition-speed);
}

.spinner-overlay.show {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    min-width: 250px;
    margin-bottom: 10px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(100%);
    transition: opacity var(--transition-speed), transform var(--transition-speed);
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* Improved Maps for Mobile */
@media (max-width: 768px) {
    #map {
        height: 300px;
    }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
    width: 100%;
}

.skeleton-text:last-child {
    width: 80%;
}

.skeleton-image {
    height: 180px;
    width: 100%;
}

/* Improved Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

:focus {
    outline: 3px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body.dark-mode-support {
        background-color: #121212;
        color: #e0e0e0;
    }
    
    body.dark-mode-support .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    body.dark-mode-support .form-control {
        background-color: #2a2a2a;
        border-color: #444;
        color: #e0e0e0;
    }
}
