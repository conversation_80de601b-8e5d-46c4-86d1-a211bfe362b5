/* Main Styles for KikaZen Ship Food Delivery App */

/* General Styles */
body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    line-height: 1.6;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

a {
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
}

.container {
    max-width: 1200px;
}

.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 5px;
    margin: 0 0.2rem;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

footer {
    margin-top: auto;
    background-color: #212529;
    color: #f8f9fa;
}

/* Hero Section */
.hero {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    z-index: 0;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    z-index: 0;
}

.hero h1 {
    font-weight: 800;
    color: #212529;
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.hero p.lead {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.hero img {
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.5s ease;
}

.hero img:hover {
    transform: translateY(-10px) scale(1.02);
}

/* Features Section */
.features {
    padding: 80px 0;
}

.features h2 {
    font-weight: 700;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.features h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: #0d6efd;
}

.features .card {
    transition: all 0.3s ease;
    padding: 2rem 1.5rem;
    border-radius: 15px;
    height: 100%;
}

.features .card:hover {
    transform: translateY(-10px);
    background-color: #f8f9fa;
}

.features .card i {
    color: #0d6efd;
    margin-bottom: 1.5rem;
    transition: transform 0.5s ease;
}

.features .card:hover i {
    transform: scale(1.2);
}

.features .card-title {
    font-weight: 600;
    margin-bottom: 1rem;
}

.features .card-text {
    color: #6c757d;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.how-it-works h2 {
    font-weight: 700;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.how-it-works h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: #0d6efd;
}

.step-number {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    font-weight: bold;
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
    transition: all 0.3s ease;
}

.how-it-works .col-md-3:hover .step-number {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(13, 110, 253, 0.4);
}

.how-it-works h3 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.how-it-works p {
    color: #6c757d;
}

/* Restaurant Cards */
.restaurant-card {
    transition: transform 0.3s ease;
    height: 100%;
}

.restaurant-card:hover {
    transform: translateY(-5px);
}

.restaurant-card img {
    height: 180px;
    object-fit: cover;
}

.restaurant-card .badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Menu Item Cards */
.menu-item-card {
    transition: transform 0.3s ease;
}

.menu-item-card:hover {
    transform: translateY(-5px);
}

.menu-item-card img {
    height: 150px;
    object-fit: cover;
}

/* Order Tracking */
.tracking-step {
    position: relative;
}

.tracking-step::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: -1;
}

.tracking-step:last-child::before {
    display: none;
}

.tracking-step .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    z-index: 1;
    position: relative;
}

.tracking-step.active .step-icon {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.tracking-step.completed .step-icon {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

/* Driver Dashboard */
.status-toggle {
    width: 80px;
}

/* Cart */
.cart-item {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.cart-item:last-child {
    border-bottom: none;
}

/* Login/Register Forms */
.auth-form {
    max-width: 450px;
    margin: 0 auto;
}

.form-control {
    border-radius: 5px;
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 5px;
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.alert {
    border-radius: 5px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

.alert-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
}

/* Profile Page */
.profile-header {
    background-color: #f8f9fa;
    padding: 30px 0;
    margin-bottom: 30px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Order Details */
.order-status-badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero {
        padding: 40px 0;
        text-align: center;
    }

    .hero img {
        margin-top: 30px;
    }

    .tracking-step::before {
        left: 0;
        top: 50%;
        width: 2px;
        height: 100%;
    }
}
