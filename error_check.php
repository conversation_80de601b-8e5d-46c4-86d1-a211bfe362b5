<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Cek koneksi database
require_once 'config/database.php';

try {
    $conn = connectDB();
    echo "Koneksi database berhasil!<br>";
    
    // Cek tabel restaurants
    $stmt = $conn->query("SHOW TABLES LIKE 'restaurants'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel restaurants ditemukan.<br>";
        
        // Cek data di tabel restaurants
        $stmt = $conn->query("SELECT COUNT(*) as count FROM restaurants");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Jumlah restoran: " . $result['count'] . "<br>";
        
        // Cek struktur tabel restaurants
        $stmt = $conn->query("DESCRIBE restaurants");
        echo "Struktur tabel restaurants:<br>";
        echo "<pre>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            print_r($row);
        }
        echo "</pre>";
    } else {
        echo "Tabel restaurants tidak ditemukan!<br>";
    }
    
    // Cek tabel restaurant_owners
    $stmt = $conn->query("SHOW TABLES LIKE 'restaurant_owners'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel restaurant_owners ditemukan.<br>";
    } else {
        echo "Tabel restaurant_owners tidak ditemukan!<br>";
    }
    
    // Cek tabel categories
    $stmt = $conn->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel categories ditemukan.<br>";
    } else {
        echo "Tabel categories tidak ditemukan!<br>";
    }
    
    // Cek tabel menu_items
    $stmt = $conn->query("SHOW TABLES LIKE 'menu_items'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel menu_items ditemukan.<br>";
    } else {
        echo "Tabel menu_items tidak ditemukan!<br>";
    }
    
    // Cek tabel menu_categories
    $stmt = $conn->query("SHOW TABLES LIKE 'menu_categories'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel menu_categories ditemukan.<br>";
    } else {
        echo "Tabel menu_categories tidak ditemukan!<br>";
    }
    
    // Cek tabel orders
    $stmt = $conn->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel orders ditemukan.<br>";
    } else {
        echo "Tabel orders tidak ditemukan!<br>";
    }
    
    // Cek tabel users
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel users ditemukan.<br>";
    } else {
        echo "Tabel users tidak ditemukan!<br>";
    }
    
    // Cek tabel drivers
    $stmt = $conn->query("SHOW TABLES LIKE 'drivers'");
    if ($stmt->rowCount() > 0) {
        echo "Tabel drivers ditemukan.<br>";
    } else {
        echo "Tabel drivers tidak ditemukan!<br>";
    }
    
} catch (PDOException $e) {
    echo "Error koneksi database: " . $e->getMessage() . "<br>";
}

// Cek file yang dibutuhkan
echo "<br>Cek file yang dibutuhkan:<br>";
$files = [
    'config/database.php',
    'includes/auth.php',
    'includes/functions.php',
    'admin/view_restaurant.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "$file ada.<br>";
    } else {
        echo "$file tidak ditemukan!<br>";
    }
}

// Cek session
echo "<br>Cek session:<br>";
session_start();
echo "Session ID: " . session_id() . "<br>";
echo "Session data: <pre>" . print_r($_SESSION, true) . "</pre>";

// Cek PHP info
echo "<br>PHP Version: " . phpversion() . "<br>";
echo "PHP Extensions: <pre>" . print_r(get_loaded_extensions(), true) . "</pre>";
?>
