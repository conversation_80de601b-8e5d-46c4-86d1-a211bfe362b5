<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if program ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: sustainability.php');
    exit;
}

$programId = $_GET['id'];

// Connect to database
$conn = connectDB();

// Get donation program details
$stmt = $conn->prepare("
    SELECT * FROM donation_programs 
    WHERE program_id = :program_id AND is_active = 1 AND end_date >= CURDATE()
");
$stmt->bindParam(':program_id', $programId);
$stmt->execute();
$program = $stmt->fetch(PDO::FETCH_ASSOC);

// If program not found or expired, redirect to sustainability page
if (!$program) {
    header('Location: sustainability.php');
    exit;
}

// Get donation tiers
$stmt = $conn->prepare("
    SELECT * FROM donation_tiers 
    WHERE program_id = :program_id
    ORDER BY amount ASC
");
$stmt->bindParam(':program_id', $programId);
$stmt->execute();
$tiers = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent donors
$stmt = $conn->prepare("
    SELECT d.amount, d.created_at, 
           CASE 
               WHEN d.is_anonymous = 1 THEN 'Anonim'
               WHEN u.user_id IS NOT NULL THEN u.name
               ELSE d.donor_name
           END as donor_name
    FROM donations d
    LEFT JOIN users u ON d.user_id = u.user_id
    WHERE d.program_id = :program_id
    ORDER BY d.created_at DESC
    LIMIT 5
");
$stmt->bindParam(':program_id', $programId);
$stmt->execute();
$recentDonors = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get donation impact
$stmt = $conn->prepare("
    SELECT * FROM donation_impact
    WHERE program_id = :program_id
");
$stmt->bindParam(':program_id', $programId);
$stmt->execute();
$impact = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    $amount = isset($_POST['amount']) ? filter_var($_POST['amount'], FILTER_VALIDATE_FLOAT) : 0;
    $customAmount = isset($_POST['custom_amount']) ? filter_var($_POST['custom_amount'], FILTER_VALIDATE_FLOAT) : 0;
    $donorName = isset($_POST['donor_name']) ? trim($_POST['donor_name']) : '';
    $isAnonymous = isset($_POST['is_anonymous']) ? 1 : 0;
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';
    
    // Use custom amount if provided
    if ($customAmount > 0) {
        $amount = $customAmount;
    }
    
    // Validate amount
    if ($amount <= 0) {
        $error = 'Jumlah donasi harus lebih dari 0.';
    } else {
        try {
            // Insert donation
            $stmt = $conn->prepare("
                INSERT INTO donations (program_id, user_id, donor_name, amount, message, is_anonymous, created_at)
                VALUES (:program_id, :user_id, :donor_name, :amount, :message, :is_anonymous, NOW())
            ");
            
            $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
            
            $stmt->bindParam(':program_id', $programId);
            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':donor_name', $donorName);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':is_anonymous', $isAnonymous);
            
            $stmt->execute();
            
            // Update current amount in donation program
            $stmt = $conn->prepare("
                UPDATE donation_programs
                SET current_amount = current_amount + :amount
                WHERE program_id = :program_id
            ");
            
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':program_id', $programId);
            
            $stmt->execute();
            
            // Set success message
            $success = 'Terima kasih atas donasi Anda! Donasi sebesar Rp ' . number_format($amount) . ' telah berhasil dicatat.';
            
            // Refresh program data
            $stmt = $conn->prepare("
                SELECT * FROM donation_programs 
                WHERE program_id = :program_id
            ");
            $stmt->bindParam(':program_id', $programId);
            $stmt->execute();
            $program = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Refresh recent donors
            $stmt = $conn->prepare("
                SELECT d.amount, d.created_at, 
                       CASE 
                           WHEN d.is_anonymous = 1 THEN 'Anonim'
                           WHEN u.user_id IS NOT NULL THEN u.name
                           ELSE d.donor_name
                       END as donor_name
                FROM donations d
                LEFT JOIN users u ON d.user_id = u.user_id
                WHERE d.program_id = :program_id
                ORDER BY d.created_at DESC
                LIMIT 5
            ");
            $stmt->bindParam(':program_id', $programId);
            $stmt->execute();
            $recentDonors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            $error = 'Terjadi kesalahan saat memproses donasi Anda. Silakan coba lagi.';
        }
    }
}

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);
$userName = '';

if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donasi - <?= $program['title'] ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
        .donation-header {
            background-color: #f8f9fa;
            padding: 40px 0;
            margin-bottom: 40px;
        }
        
        .donation-image {
            width: 100%;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .progress {
            height: 10px;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        
        .donation-tier {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .donation-tier:hover {
            border-color: #28a745;
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .donation-tier.selected {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        
        .donation-tier .tier-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .donation-tier .tier-description {
            margin-top: 10px;
            color: #6c757d;
        }
        
        .impact-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .impact-icon {
            width: 50px;
            height: 50px;
            background-color: #e9ecef;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            color: #28a745;
            font-size: 1.5rem;
        }
        
        .impact-text {
            flex: 1;
        }
        
        .donor-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .donor-item:last-child {
            border-bottom: none;
        }
        
        .donor-name {
            font-weight: 500;
        }
        
        .donor-amount {
            color: #28a745;
            font-weight: 500;
        }
        
        .donor-date {
            color: #6c757d;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="fas fa-shopping-cart me-2"></i>Keranjang
                                <span class="badge bg-danger rounded-pill">
                                    <?php
                                        $cartCount = 0;
                                        if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                                            foreach ($_SESSION['cart'] as $items) {
                                                $cartCount += count($items);
                                            }
                                        }
                                        echo $cartCount;
                                    ?>
                                </span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="driver/orders.php">Pengiriman</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="owner/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-2"></i>Masuk
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-2"></i>Daftar
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Donation Header -->
    <section class="donation-header">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="sustainability.php">Keberlanjutan</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Donasi</li>
                </ol>
            </nav>
            <h1 class="mt-4"><?= $program['title'] ?></h1>
            <p class="lead"><?= $program['subtitle'] ?? 'Bantu kami membuat perbedaan' ?></p>
        </div>
    </section>

    <!-- Content -->
    <div class="container py-5">
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <img src="<?= $program['image_url'] ?>" class="donation-image" alt="<?= $program['title'] ?>">
                
                <div class="mb-4">
                    <h4>Tentang Program Donasi Ini</h4>
                    <p><?= $program['description'] ?></p>
                </div>
                
                <?php 
                $percentage = ($program['current_amount'] / $program['target_amount']) * 100;
                $percentage = min(100, $percentage);
                ?>
                
                <div class="mb-4">
                    <h5>Target: Rp <?= number_format($program['target_amount']) ?></h5>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $percentage ?>%" 
                             aria-valuenow="<?= $percentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Terkumpul: Rp <?= number_format($program['current_amount']) ?></span>
                        <span><?= round($percentage) ?>%</span>
                    </div>
                    <p class="small text-muted mt-2">
                        <i class="fas fa-calendar-alt me-2"></i>Berakhir pada: <?= date('d F Y', strtotime($program['end_date'])) ?>
                    </p>
                </div>
                
                <?php if (!empty($impact)): ?>
                <div class="mb-5">
                    <h4>Dampak Donasi Anda</h4>
                    <?php foreach ($impact as $item): ?>
                        <div class="impact-item">
                            <div class="impact-icon">
                                <i class="<?= $item['icon'] ?? 'fas fa-hand-holding-heart' ?>"></i>
                            </div>
                            <div class="impact-text">
                                <h5><?= $item['title'] ?></h5>
                                <p class="mb-0"><?= $item['description'] ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <form action="donation.php?id=<?= $programId ?>" method="post" id="donation-form">
                    <h4 class="mb-4">Pilih Jumlah Donasi</h4>
                    
                    <?php if (!empty($tiers)): ?>
                        <div class="row row-cols-1 row-cols-md-2 g-3 mb-4">
                            <?php foreach ($tiers as $tier): ?>
                                <div class="col">
                                    <div class="donation-tier" data-amount="<?= $tier['amount'] ?>">
                                        <div class="tier-amount">Rp <?= number_format($tier['amount']) ?></div>
                                        <div class="tier-description"><?= $tier['description'] ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <div class="col">
                                <div class="donation-tier custom-tier">
                                    <div class="tier-amount">Jumlah Lainnya</div>
                                    <div class="tier-description">
                                        <div class="input-group mt-2">
                                            <span class="input-group-text">Rp</span>
                                            <input type="number" class="form-control" id="custom-amount" name="custom_amount" placeholder="Masukkan jumlah">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" name="amount" id="selected-amount" value="0">
                    <?php else: ?>
                        <div class="mb-4">
                            <label for="amount" class="form-label">Jumlah Donasi</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" class="form-control" id="amount" name="amount" placeholder="Masukkan jumlah donasi" required>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="donor_name" class="form-label">Nama Anda</label>
                        <input type="text" class="form-control" id="donor_name" name="donor_name" 
                               value="<?= $isLoggedIn ? $userName : '' ?>" 
                               placeholder="Masukkan nama Anda" required>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_anonymous" name="is_anonymous">
                        <label class="form-check-label" for="is_anonymous">Donasi secara anonim</label>
                    </div>
                    
                    <div class="mb-4">
                        <label for="message" class="form-label">Pesan (opsional)</label>
                        <textarea class="form-control" id="message" name="message" rows="3" placeholder="Tulis pesan Anda di sini"></textarea>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-hand-holding-heart me-2"></i>Donasi Sekarang
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Donatur Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentDonors)): ?>
                            <?php foreach ($recentDonors as $donor): ?>
                                <div class="donor-item">
                                    <div>
                                        <div class="donor-name"><?= $donor['donor_name'] ?></div>
                                        <div class="donor-date"><?= date('d M Y', strtotime($donor['created_at'])) ?></div>
                                    </div>
                                    <div class="donor-amount">Rp <?= number_format($donor['amount']) ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-center text-muted">Belum ada donasi. Jadilah yang pertama!</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Bagikan Program Ini</h5>
                    </div>
                    <div class="card-body">
                        <p>Bantu kami menyebarkan informasi tentang program donasi ini.</p>
                        
                        <div class="d-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>" 
                               target="_blank" class="btn btn-primary flex-grow-1">
                                <i class="fab fa-facebook-f me-2"></i>Facebook
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>&text=<?= urlencode($program['title']) ?>" 
                               target="_blank" class="btn btn-info flex-grow-1">
                                <i class="fab fa-twitter me-2"></i>Twitter
                            </a>
                            <a href="https://api.whatsapp.com/send?text=<?= urlencode($program['title'] . ' - https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>" 
                               target="_blank" class="btn btn-success flex-grow-1">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat dan ramah lingkungan.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="sustainability.php" class="text-white">Keberlanjutan</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
                <p class="small">
                    <i class="fas fa-leaf me-2"></i>Dicetak di atas kertas digital untuk menyelamatkan pohon.
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle donation tier selection
            const donationTiers = document.querySelectorAll('.donation-tier');
            const selectedAmountInput = document.getElementById('selected-amount');
            const customAmountInput = document.getElementById('custom-amount');
            
            donationTiers.forEach(tier => {
                tier.addEventListener('click', function() {
                    // Remove selected class from all tiers
                    donationTiers.forEach(t => t.classList.remove('selected'));
                    
                    // Add selected class to clicked tier
                    this.classList.add('selected');
                    
                    // Update selected amount
                    if (this.classList.contains('custom-tier')) {
                        selectedAmountInput.value = 0;
                        customAmountInput.focus();
                    } else {
                        selectedAmountInput.value = this.dataset.amount;
                        customAmountInput.value = '';
                    }
                });
            });
            
            // Handle anonymous checkbox
            const anonymousCheckbox = document.getElementById('is_anonymous');
            const donorNameInput = document.getElementById('donor_name');
            
            anonymousCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    donorNameInput.disabled = true;
                    donorNameInput.value = 'Anonim';
                } else {
                    donorNameInput.disabled = false;
                    donorNameInput.value = '<?= $isLoggedIn ? $userName : '' ?>';
                }
            });
            
            // Form validation
            const donationForm = document.getElementById('donation-form');
            
            donationForm.addEventListener('submit', function(e) {
                const selectedAmount = parseFloat(selectedAmountInput.value);
                const customAmount = parseFloat(customAmountInput.value || 0);
                
                if (selectedAmount === 0 && customAmount === 0) {
                    e.preventDefault();
                    alert('Silakan pilih atau masukkan jumlah donasi.');
                }
            });
        });
    </script>
</body>
</html>
