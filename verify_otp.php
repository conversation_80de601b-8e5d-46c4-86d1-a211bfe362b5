<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'includes/two_factor_auth.php';

// Check if 2FA verification is in progress
if (!isset($_SESSION['2fa_verification'])) {
    header('Location: login.php');
    exit;
}

$verification = $_SESSION['2fa_verification'];
$userId = $verification['user_id'];
$userType = $verification['user_type'];
$method = $verification['method'];
$email = $verification['email'] ?? '';
$phone = $verification['phone'] ?? '';
$name = $verification['name'] ?? '';

// Connect to database
$conn = connectDB();

// Handle form submission
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otp = $_POST['otp'] ?? '';
    
    if (empty($otp)) {
        $error = 'Kode verifikasi tidak boleh kosong.';
    } else {
        // Verify OTP
        if (verifyOTP($conn, $userId, $otp, $userType)) {
            // OTP is valid, complete login
            
            // Update last login time
            $tableName = '';
            $idColumn = '';
            
            switch ($userType) {
                case 'user':
                    $tableName = 'users';
                    $idColumn = 'user_id';
                    $_SESSION['user_id'] = $userId;
                    $_SESSION['user_name'] = $name;
                    $_SESSION['user_type'] = 'user';
                    break;
                case 'driver':
                    $tableName = 'drivers';
                    $idColumn = 'driver_id';
                    $_SESSION['driver_id'] = $userId;
                    $_SESSION['driver_name'] = $name;
                    $_SESSION['user_type'] = 'driver';
                    break;
                case 'owner':
                    $tableName = 'restaurant_owners';
                    $idColumn = 'owner_id';
                    $_SESSION['owner_id'] = $userId;
                    $_SESSION['owner_name'] = $name;
                    $_SESSION['user_type'] = 'owner';
                    break;
                case 'admin':
                    $tableName = 'admins';
                    $idColumn = 'admin_id';
                    $_SESSION['admin_id'] = $userId;
                    $_SESSION['admin_name'] = $name;
                    $_SESSION['user_type'] = 'admin';
                    break;
            }
            
            $stmt = $conn->prepare("
                UPDATE $tableName 
                SET last_login = NOW(), login_attempts = 0
                WHERE $idColumn = :user_id
            ");
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            
            // Log security event
            logSecurityEvent('login_2fa_success', 'info', ['method' => $method]);
            
            // Clear 2FA verification session
            unset($_SESSION['2fa_verification']);
            
            // Redirect to appropriate dashboard
            switch ($userType) {
                case 'user':
                    header('Location: user/dashboard.php');
                    break;
                case 'driver':
                    header('Location: driver/dashboard.php');
                    break;
                case 'owner':
                    header('Location: owner/dashboard.php');
                    break;
                case 'admin':
                    header('Location: admin/dashboard.php');
                    break;
                default:
                    header('Location: index.php');
            }
            exit;
        } else {
            $error = 'Kode verifikasi tidak valid atau sudah kedaluwarsa. Silakan coba lagi.';
            
            // Log security event
            logSecurityEvent('login_2fa_failed', 'warning', ['method' => $method]);
        }
    }
}

// Resend OTP
if (isset($_GET['resend']) && $_GET['resend'] === '1') {
    // Generate new OTP
    $otp = generateOTP();
    
    // Store OTP in database
    if (storeOTP($conn, $userId, $otp, $userType)) {
        // Send OTP
        $otpSent = false;
        
        if ($method === 'email' && !empty($email)) {
            $otpSent = sendOTPEmail($email, $otp, $name);
        } elseif ($method === 'sms' && !empty($phone)) {
            $otpSent = sendOTPSMS($phone, $otp);
        }
        
        if ($otpSent) {
            $success = 'Kode verifikasi baru telah dikirim.';
        } else {
            $error = 'Gagal mengirim kode verifikasi baru. Silakan coba lagi.';
        }
    } else {
        $error = 'Terjadi kesalahan saat menyimpan kode verifikasi. Silakan coba lagi.';
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifikasi Login - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .verification-container {
            max-width: 500px;
            margin: 100px auto;
        }
        .otp-input {
            letter-spacing: 0.5em;
            text-align: center;
            font-size: 1.5em;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo i {
            font-size: 3em;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="verification-container">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                <h2>KikaZen Ship</h2>
            </div>
            
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Verifikasi Login</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= $success ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= $error ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Verifikasi Diperlukan</h5>
                                <p class="mb-0">
                                    Kami telah mengirimkan kode verifikasi ke 
                                    <?php if ($method === 'email'): ?>
                                        alamat email Anda (<?= substr($email, 0, 3) . '***' . substr($email, strpos($email, '@')) ?>).
                                    <?php else: ?>
                                        nomor telepon Anda (<?= substr($phone, 0, 3) . '***' . substr($phone, -3) ?>).
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <form action="verify_otp.php" method="post">
                        <div class="mb-3">
                            <label for="otp" class="form-label">Kode Verifikasi</label>
                            <input type="text" class="form-control otp-input" id="otp" name="otp" maxlength="6" placeholder="Masukkan kode 6 digit" required autofocus>
                            <div class="form-text">Kode verifikasi berlaku selama 5 menit.</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>Verifikasi
                            </button>
                            <a href="verify_otp.php?resend=1" class="btn btn-outline-secondary">
                                <i class="fas fa-sync me-2"></i>Kirim Ulang Kode
                            </a>
                            <a href="login.php" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-white text-center">
                    <p class="mb-0 small text-muted">Butuh bantuan? <a href="contact.php">Hubungi kami</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Format OTP input
        document.addEventListener('DOMContentLoaded', function() {
            const otpInput = document.getElementById('otp');
            if (otpInput) {
                otpInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
            }
        });
    </script>
</body>
</html>
