<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Mark notification as read if requested
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $notification_id = $_GET['mark_read'];
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE notification_id = :notification_id AND driver_id = :driver_id");
    $stmt->bindParam(':notification_id', $notification_id);
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();

    // Redirect to remove the mark_read parameter
    header('Location: notifications.php');
    exit;
}

// Mark all notifications as read if requested
if (isset($_GET['mark_all_read'])) {
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE driver_id = :driver_id");
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();

    // Redirect to remove the mark_all_read parameter
    header('Location: notifications.php');
    exit;
}

// Get notifications
$stmt = $conn->prepare("
    SELECT * FROM notifications
    WHERE driver_id = :driver_id
    ORDER BY created_at DESC
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$notifications = $stmt->fetchAll();

// Enhance notifications with order details
$enhancedNotifications = [];
foreach ($notifications as $notification) {
    if ($notification['order_id']) {
        // Get order details
        $stmt = $conn->prepare("
            SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
                  r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
                  u.name as customer_name, u.phone as customer_phone
            FROM orders o
            LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
            LEFT JOIN users u ON o.user_id = u.user_id
            WHERE o.order_id = :order_id
        ");
        $stmt->bindParam(':order_id', $notification['order_id']);
        $stmt->execute();
        $orderDetails = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($orderDetails) {
            // Merge order details with notification
            $notification = array_merge($notification, $orderDetails);

            // Get order items
            $stmt = $conn->prepare("
                SELECT oi.*, mi.name as item_name, mi.image_url as item_image
                FROM order_items oi
                JOIN menu_items mi ON oi.item_id = mi.item_id
                WHERE oi.order_id = :order_id
            ");
            $stmt->bindParam(':order_id', $notification['order_id']);
            $stmt->execute();
            $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $notification['order_items'] = $orderItems;
        }
    }
    $enhancedNotifications[] = $notification;
}
$notifications = $enhancedNotifications;

// Debug: Print notification data
echo "<!-- Debug: Enhanced Notifications -->";
echo "<!-- " . print_r($notifications, true) . " -->";

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Helper functions for order status
function getStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'Menunggu';
        case 'confirmed':
            return 'Dikonfirmasi';
        case 'preparing':
            return 'Sedang Dipersiapkan';
        case 'ready_for_pickup':
            return 'Siap Diambil';
        case 'picked_up':
            return 'Diambil';
        case 'on_the_way':
            return 'Dalam Perjalanan';
        case 'delivered':
            return 'Terkirim';
        case 'cancelled':
            return 'Dibatalkan';
        default:
            return $status;
    }
}

function getBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning text-dark';
        case 'confirmed':
            return 'bg-info text-dark';
        case 'preparing':
            return 'bg-primary';
        case 'ready_for_pickup':
            return 'bg-primary';
        case 'picked_up':
            return 'bg-info';
        case 'on_the_way':
            return 'bg-info';
        case 'delivered':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// Count unread notifications
$unread_count = getUnreadNotificationsCount($driver_id);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifikasi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .notification-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .notification-item:hover {
            background-color: rgba(0,0,0,0.03);
        }
        .notification-item.unread {
            border-left-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
        }
        .notification-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .notification-item .card {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .notification-item .card-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .notification-item .card-body {
            padding: 1rem;
        }
        .notification-item h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .notification-item .list-group-item {
            padding-left: 0;
            padding-right: 0;
        }
        .notification-item .btn-group {
            margin-top: 1rem;
        }
        .order-items-list {
            max-height: 150px;
            overflow-y: auto;
            margin-bottom: 1rem;
        }
        .order-total {
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active position-relative" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifikasi
                            <?php if ($unread_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= $unread_count ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="route_optimization.php">Optimasi Rute</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Notifications Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Notifikasi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Notifikasi</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <?php if ($unread_count > 0): ?>
                    <a href="notifications.php?mark_all_read=1" class="btn btn-outline-primary">
                        <i class="fas fa-check-double me-2"></i>Tandai Semua Dibaca
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Daftar Notifikasi</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($notifications)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Notifikasi</h4>
                                <p class="text-muted">Anda belum memiliki notifikasi apapun.</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($notifications as $notification): ?>
                                    <div class="list-group-item notification-item <?= $notification['is_read'] ? '' : 'unread' ?>">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-1">
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-primary me-2">Baru</span>
                                                <?php endif; ?>
                                                <?php
                                                $icon = '';
                                                switch ($notification['type']) {
                                                    case 'ready_for_pickup':
                                                        $icon = 'fa-shopping-bag text-warning';
                                                        break;
                                                    case 'order_assigned':
                                                        $icon = 'fa-check-circle text-success';
                                                        break;
                                                    case 'order_cancelled':
                                                        $icon = 'fa-times-circle text-danger';
                                                        break;
                                                    default:
                                                        $icon = 'fa-bell text-primary';
                                                }
                                                ?>
                                                <i class="fas <?= $icon ?> me-2"></i>
                                                <?= htmlspecialchars($notification['message']) ?>
                                            </h6>
                                            <span class="notification-time">
                                                <?= date('d M Y, H:i', strtotime($notification['created_at'])) ?>
                                            </span>
                                        </div>

                                        <?php
                                        // Debug: Print notification data for this specific notification
                                        echo "<!-- Debug: Specific Notification -->";
                                        echo "<!-- order_id: " . (isset($notification['order_id']) ? $notification['order_id'] : 'not set') . " -->";
                                        echo "<!-- restaurant_name: " . (isset($notification['restaurant_name']) ? $notification['restaurant_name'] : 'not set') . " -->";

                                        if (isset($notification['order_id']) && !empty($notification['order_id']) &&
                                            isset($notification['restaurant_name']) && !empty($notification['restaurant_name'])):
                                        ?>
                                            <div class="card mt-3 mb-3">
                                                <div class="card-header bg-light">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h6 class="mb-0">Detail Pesanan #<?= $notification['order_id'] ?></h6>
                                                        <span class="badge <?= isset($notification['order_status']) ? getBadgeClass($notification['order_status']) : 'bg-secondary' ?>">
                                                            <?= isset($notification['order_status']) ? getStatusText($notification['order_status']) : 'Tidak diketahui' ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <h6><i class="fas fa-store me-2 text-primary"></i>Restoran:</h6>
                                                            <p class="mb-1"><?= $notification['restaurant_name'] ?></p>
                                                            <p class="small text-muted mb-0"><?= $notification['restaurant_address'] ?></p>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <h6><i class="fas fa-user me-2 text-primary"></i>Pelanggan:</h6>
                                                            <p class="mb-1"><?= $notification['customer_name'] ?></p>
                                                            <p class="small text-muted mb-0"><?= $notification['customer_phone'] ?></p>
                                                        </div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <h6><i class="fas fa-map-marker-alt me-2 text-primary"></i>Alamat Pengiriman:</h6>
                                                        <p class="mb-0"><?= $notification['delivery_address'] ?></p>
                                                    </div>

                                                    <?php if (isset($notification['order_items']) && !empty($notification['order_items'])): ?>
                                                        <div class="mb-3">
                                                            <h6><i class="fas fa-utensils me-2 text-primary"></i>Item Pesanan:</h6>
                                                            <ul class="list-group list-group-flush">
                                                                <?php foreach ($notification['order_items'] as $item): ?>
                                                                    <li class="list-group-item px-0 py-2 border-0">
                                                                        <div class="d-flex justify-content-between">
                                                                            <div>
                                                                                <span class="fw-bold"><?= $item['quantity'] ?>x</span> <?= $item['item_name'] ?>
                                                                            </div>
                                                                            <div>
                                                                                <?= isset($item['subtotal']) ? 'Rp' . number_format($item['subtotal'], 0, ',', '.') : '-' ?>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="row">
                                                        <div class="col-6">
                                                            <h6><i class="fas fa-money-bill me-2 text-success"></i>Total:</h6>
                                                            <p class="mb-0 fw-bold"><?= isset($notification['total_amount']) ? 'Rp' . number_format($notification['total_amount'], 0, ',', '.') : '-' ?></p>
                                                        </div>
                                                        <div class="col-6">
                                                            <h6><i class="fas fa-money-bill-wave me-2 text-success"></i>Biaya Antar:</h6>
                                                            <p class="mb-0"><?= isset($notification['delivery_fee']) ? 'Rp' . number_format($notification['delivery_fee'], 0, ',', '.') : '-' ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="mt-2">
                                            <?php if ($notification['order_id']): ?>
                                                <a href="order_detail.php?id=<?= $notification['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>Lihat Detail Lengkap
                                                </a>
                                                <?php if (isset($notification['order_status']) && $notification['order_status'] === 'ready_for_pickup' &&
                                                         (!isset($notification['driver_id']) || $notification['driver_id'] === null)): ?>
                                                    <a href="available_orders.php" class="btn btn-sm btn-success">
                                                        <i class="fas fa-check me-1"></i>Ambil Pesanan
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                            <?php if (!$notification['is_read']): ?>
                                                <a href="notifications.php?mark_read=<?= $notification['notification_id'] ?>" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-check me-1"></i>Tandai Dibaca
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
