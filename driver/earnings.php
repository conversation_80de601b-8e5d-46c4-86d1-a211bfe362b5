<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Get filter parameters
$period = isset($_GET['period']) ? $_GET['period'] : 'all';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Set date range based on period
$where_clause = "WHERE o.driver_id = :driver_id AND o.order_status = 'delivered'";
$params = [':driver_id' => $driver_id];

if ($period === 'today') {
    $where_clause .= " AND DATE(o.updated_at) = CURDATE()";
} elseif ($period === 'yesterday') {
    $where_clause .= " AND DATE(o.updated_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
} elseif ($period === 'this_week') {
    $where_clause .= " AND YEARWEEK(o.updated_at, 1) = YEARWEEK(CURDATE(), 1)";
} elseif ($period === 'last_week') {
    $where_clause .= " AND YEARWEEK(o.updated_at, 1) = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 1 WEEK), 1)";
} elseif ($period === 'this_month') {
    $where_clause .= " AND YEAR(o.updated_at) = YEAR(CURDATE()) AND MONTH(o.updated_at) = MONTH(CURDATE())";
} elseif ($period === 'last_month') {
    $where_clause .= " AND YEAR(o.updated_at) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) AND MONTH(o.updated_at) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))";
} elseif ($period === 'custom' && !empty($start_date) && !empty($end_date)) {
    $where_clause .= " AND DATE(o.updated_at) BETWEEN :start_date AND :end_date";
    $params[':start_date'] = $start_date;
    $params[':end_date'] = $end_date;
}

// Get earnings data
$stmt = $conn->prepare("
    SELECT 
        o.order_id,
        o.total_amount,
        o.delivery_fee,
        o.updated_at,
        r.name as restaurant_name,
        u.name as customer_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    $where_clause
    ORDER BY o.updated_at DESC
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$earnings = $stmt->fetchAll();

// Calculate total earnings
$total_earnings = 0;
$total_orders = count($earnings);

foreach ($earnings as $earning) {
    $total_earnings += $earning['delivery_fee'];
}

// Get earnings summary by day
$stmt = $conn->prepare("
    SELECT 
        DATE(o.updated_at) as date,
        COUNT(*) as order_count,
        SUM(o.delivery_fee) as total_fee
    FROM orders o
    $where_clause
    GROUP BY DATE(o.updated_at)
    ORDER BY DATE(o.updated_at) DESC
    LIMIT 30
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$daily_earnings = $stmt->fetchAll();

// Get period label for display
$period_label = 'Semua Waktu';
if ($period === 'today') {
    $period_label = 'Hari Ini';
} elseif ($period === 'yesterday') {
    $period_label = 'Kemarin';
} elseif ($period === 'this_week') {
    $period_label = 'Minggu Ini';
} elseif ($period === 'last_week') {
    $period_label = 'Minggu Lalu';
} elseif ($period === 'this_month') {
    $period_label = 'Bulan Ini';
} elseif ($period === 'last_month') {
    $period_label = 'Bulan Lalu';
} elseif ($period === 'custom' && !empty($start_date) && !empty($end_date)) {
    $period_label = date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date));
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penghasilan Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item active" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Earnings Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Penghasilan Pengemudi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Penghasilan</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card bg-primary text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Penghasilan</h6>
                                <h2 class="mb-0">Rp<?= number_format($total_earnings * 15000, 0, ',', '.') ?></h2>
                                <p class="mb-0 small"><?= $period_label ?></p>
                            </div>
                            <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card bg-success text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Jumlah Pesanan</h6>
                                <h2 class="mb-0"><?= $total_orders ?></h2>
                                <p class="mb-0 small"><?= $period_label ?></p>
                            </div>
                            <i class="fas fa-shopping-bag fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card bg-info text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Rata-rata per Pesanan</h6>
                                <h2 class="mb-0">
                                    <?php
                                    $avg = $total_orders > 0 ? $total_earnings / $total_orders : 0;
                                    echo 'Rp' . number_format($avg * 15000, 0, ',', '.');
                                    ?>
                                </h2>
                                <p class="mb-0 small"><?= $period_label ?></p>
                            </div>
                            <i class="fas fa-chart-line fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Filter Penghasilan</h5>
                    </div>
                    <div class="card-body">
                        <form action="earnings.php" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="period" class="form-label">Periode</label>
                                <select class="form-select" id="period" name="period" onchange="toggleCustomDateInputs()">
                                    <option value="all" <?= $period === 'all' ? 'selected' : '' ?>>Semua Waktu</option>
                                    <option value="today" <?= $period === 'today' ? 'selected' : '' ?>>Hari Ini</option>
                                    <option value="yesterday" <?= $period === 'yesterday' ? 'selected' : '' ?>>Kemarin</option>
                                    <option value="this_week" <?= $period === 'this_week' ? 'selected' : '' ?>>Minggu Ini</option>
                                    <option value="last_week" <?= $period === 'last_week' ? 'selected' : '' ?>>Minggu Lalu</option>
                                    <option value="this_month" <?= $period === 'this_month' ? 'selected' : '' ?>>Bulan Ini</option>
                                    <option value="last_month" <?= $period === 'last_month' ? 'selected' : '' ?>>Bulan Lalu</option>
                                    <option value="custom" <?= $period === 'custom' ? 'selected' : '' ?>>Kustom</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 custom-date-input" style="display: <?= $period === 'custom' ? 'block' : 'none' ?>;">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                            </div>
                            
                            <div class="col-md-3 custom-date-input" style="display: <?= $period === 'custom' ? 'block' : 'none' ?>;">
                                <label for="end_date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ringkasan Harian</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($daily_earnings)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Data</h4>
                                <p class="text-muted">Tidak ada data penghasilan untuk periode yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Tanggal</th>
                                            <th>Jumlah Pesanan</th>
                                            <th>Total Penghasilan</th>
                                            <th>Rata-rata per Pesanan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($daily_earnings as $day): ?>
                                            <tr>
                                                <td><?= date('d/m/Y (l)', strtotime($day['date'])) ?></td>
                                                <td><?= $day['order_count'] ?></td>
                                                <td>Rp<?= number_format($day['total_fee'] * 15000, 0, ',', '.') ?></td>
                                                <td>
                                                    <?php
                                                    $avg_day = $day['order_count'] > 0 ? $day['total_fee'] / $day['order_count'] : 0;
                                                    echo 'Rp' . number_format($avg_day * 15000, 0, ',', '.');
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Detail Penghasilan</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($earnings)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Data</h4>
                                <p class="text-muted">Tidak ada data penghasilan untuk periode yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID Pesanan</th>
                                            <th>Tanggal</th>
                                            <th>Restoran</th>
                                            <th>Pelanggan</th>
                                            <th>Total Pesanan</th>
                                            <th>Biaya Antar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($earnings as $earning): ?>
                                            <tr>
                                                <td><a href="order_detail.php?id=<?= $earning['order_id'] ?>">#<?= $earning['order_id'] ?></a></td>
                                                <td><?= date('d/m/Y H:i', strtotime($earning['updated_at'])) ?></td>
                                                <td><?= $earning['restaurant_name'] ?></td>
                                                <td><?= $earning['customer_name'] ?></td>
                                                <td>Rp<?= number_format($earning['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>Rp<?= number_format($earning['delivery_fee'] * 15000, 0, ',', '.') ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleCustomDateInputs() {
            const period = document.getElementById('period').value;
            const customDateInputs = document.querySelectorAll('.custom-date-input');
            
            customDateInputs.forEach(input => {
                input.style.display = period === 'custom' ? 'block' : 'none';
            });
        }
    </script>
</body>
</html>
