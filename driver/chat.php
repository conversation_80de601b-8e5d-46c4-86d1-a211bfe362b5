<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if driver is logged in
if (!isDriverLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Get active room ID from URL if present
$active_room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;

// Check if we need to start a new chat
$start_chat = isset($_GET['start_chat']) ? $_GET['start_chat'] : '';
$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;

// If starting a new chat about an order
if ($start_chat === 'order' && $order_id > 0) {
    // Check if a chat room already exists for this order
    $stmt = $conn->prepare("
        SELECT cr.room_id
        FROM chat_rooms cr
        JOIN chat_participants cp ON cr.room_id = cp.room_id
        WHERE cr.order_id = :order_id
        AND cp.user_type = 'driver'
        AND cp.user_id = :driver_id
        LIMIT 1
    ");
    $stmt->bindParam(':order_id', $order_id);
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();
    $existing_room = $stmt->fetch();

    if ($existing_room) {
        // If room exists, set it as active
        $active_room_id = $existing_room['room_id'];
    } else {
        // If no room exists, we'll create one after the page loads using JavaScript
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                // Open the new chat modal
                const newChatModal = new bootstrap.Modal(document.getElementById('newChatModal'));
                newChatModal.show();

                // Set the chat type to 'order'
                document.getElementById('chatType').value = 'order';

                // Trigger the change event to show the order select
                document.getElementById('chatType').dispatchEvent(new Event('change'));

                // Set the order ID
                document.getElementById('orderSelect').value = '$order_id';

                // Set a default message
                document.getElementById('messageText').value = 'Halo, saya ingin bertanya tentang pesanan #$order_id';
            });
        </script>";
    }
}

// Check if the room exists and driver has access
$has_access = false;
if ($active_room_id > 0) {
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM chat_participants
        WHERE room_id = :room_id AND user_type = 'driver' AND user_id = :driver_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();
    $has_access = ($stmt->fetchColumn() > 0);

    if (!$has_access) {
        $active_room_id = 0;
    }
}

// Get chat rooms for this driver
$stmt = $conn->prepare("
    SELECT cr.*,
           o.order_id,
           o.total_amount,
           u.name AS customer_name,
           r.name AS restaurant_name,
           (SELECT COUNT(*) FROM chat_messages cm
            WHERE cm.room_id = cr.room_id
            AND cm.is_read = 0
            AND cm.sender_type != 'driver'
            AND cm.sender_id != :driver_id) AS unread_count,
           (SELECT cm.message_text FROM chat_messages cm
            WHERE cm.room_id = cr.room_id
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message,
           (SELECT cm.created_at FROM chat_messages cm
            WHERE cm.room_id = cr.room_id
            ORDER BY cm.created_at DESC LIMIT 1) AS last_message_time
    FROM chat_rooms cr
    JOIN chat_participants cp ON cr.room_id = cp.room_id
    LEFT JOIN orders o ON cr.order_id = o.order_id
    LEFT JOIN users u ON o.user_id = u.user_id
    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE cp.user_type = 'driver' AND cp.user_id = :driver_id
    ORDER BY cr.updated_at DESC
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$chat_rooms = $stmt->fetchAll();

// If no active room but rooms exist, select the first one
if ($active_room_id == 0 && count($chat_rooms) > 0) {
    $active_room_id = $chat_rooms[0]['room_id'];
    $has_access = true;
}

// Get room details and participants if active room exists
$room_details = null;
$participants = [];
if ($active_room_id > 0) {
    // Get room details
    $stmt = $conn->prepare("
        SELECT cr.*,
               o.order_id,
               o.total_amount,
               u.name AS customer_name,
               u.user_id,
               r.name AS restaurant_name,
               r.restaurant_id
        FROM chat_rooms cr
        LEFT JOIN orders o ON cr.order_id = o.order_id
        LEFT JOIN users u ON o.user_id = u.user_id
        LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
        WHERE cr.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $room_details = $stmt->fetch();

    // Get participants
    $stmt = $conn->prepare("
        SELECT cp.*,
               u.name AS customer_name,
               ro.name AS restaurant_owner_name,
               d.name AS driver_name,
               a.name AS admin_name
        FROM chat_participants cp
        LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
        LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
        LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
        LEFT JOIN admins a ON cp.user_type = 'admin' AND cp.user_id = a.admin_id
        WHERE cp.room_id = :room_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->execute();
    $participants = $stmt->fetchAll();

    // Mark messages as read
    $stmt = $conn->prepare("
        UPDATE chat_messages
        SET is_read = 1
        WHERE room_id = :room_id
        AND sender_type != 'driver'
        AND sender_id != :driver_id
        AND is_read = 0
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();

    // Update last read timestamp
    $stmt = $conn->prepare("
        UPDATE chat_participants
        SET last_read_at = NOW()
        WHERE room_id = :room_id
        AND user_type = 'driver'
        AND user_id = :driver_id
    ");
    $stmt->bindParam(':room_id', $active_room_id);
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/chat.css">
</head>
<body data-user-type="driver" data-user-id="<?= $driver_id ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-motorcycle me-2"></i>KikaZen Ship - Pengemudi
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">Pesanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="earnings.php">Penghasilan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="chat.php">Chat</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $driver['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="vehicle.php">Kendaraan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Chat Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Chat</h1>
                <p class="text-muted">Komunikasi dengan pelanggan dan restoran</p>
            </div>
            <div class="col-md-4 text-md-end">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newChatModal">
                    <i class="fas fa-plus me-2"></i>Mulai Percakapan Baru
                </button>
            </div>
        </div>

        <div class="row">
            <!-- Chat Rooms List -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Percakapan</h5>
                    </div>
                    <div class="list-group list-group-flush" id="chat-rooms-list">
                        <?php if (count($chat_rooms) === 0): ?>
                            <div class="text-center p-3">Belum ada percakapan</div>
                        <?php else: ?>
                            <?php foreach ($chat_rooms as $room): ?>
                                <?php
                                $unreadBadge = $room['unread_count'] > 0 ? '<span class="badge bg-danger rounded-pill">' . $room['unread_count'] . '</span>' : '';
                                $lastMessageTime = $room['last_message_time'] ? date('H:i', strtotime($room['last_message_time'])) : '';

                                $roomTitle = '';
                                if ($room['room_type'] === 'order') {
                                    $roomTitle = 'Pesanan #' . $room['order_id'];
                                } elseif ($room['room_type'] === 'complaint') {
                                    $roomTitle = 'Dukungan Keluhan';
                                } else {
                                    $roomTitle = 'Chat Dukungan';
                                }

                                $participantInfo = '';
                                if ($room['customer_name']) $participantInfo .= '<div class="small text-muted">Pelanggan: ' . $room['customer_name'] . '</div>';
                                if ($room['restaurant_name']) $participantInfo .= '<div class="small text-muted">Restoran: ' . $room['restaurant_name'] . '</div>';
                                ?>
                                <a href="chat.php?room_id=<?= $room['room_id'] ?>" class="chat-room-item list-group-item list-group-item-action d-flex justify-content-between align-items-start <?= $room['room_id'] == $active_room_id ? 'active' : '' ?> <?= $room['status'] === 'closed' ? 'bg-light' : '' ?>" data-room-id="<?= $room['room_id'] ?>">
                                    <div class="ms-2 me-auto">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?= $roomTitle ?></h6>
                                            <small><?= $lastMessageTime ?></small>
                                        </div>
                                        <?= $participantInfo ?>
                                        <p class="mb-1 text-truncate"><?= $room['last_message'] ?: 'Belum ada pesan' ?></p>
                                    </div>
                                    <?= $unreadBadge ?>
                                </a>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="col-md-8">
                <?php if ($active_room_id > 0 && $has_access): ?>
                    <div class="card chat-card">
                        <div class="card-header bg-primary text-white">
                            <?php
                            $headerTitle = '';
                            if ($room_details['room_type'] === 'order') {
                                $headerTitle = 'Pesanan #' . $room_details['order_id'];
                                if ($room_details['customer_name']) $headerTitle .= ' - ' . $room_details['customer_name'];
                                if ($room_details['restaurant_name']) $headerTitle .= ' (' . $room_details['restaurant_name'] . ')';
                            } elseif ($room_details['room_type'] === 'complaint') {
                                $headerTitle = 'Dukungan Keluhan';
                            } else {
                                $headerTitle = 'Chat Dukungan';
                            }
                            ?>
                            <h5 class="card-title mb-0"><?= $headerTitle ?></h5>
                            <?php if ($room_details['status'] === 'closed'): ?>
                                <span class="badge bg-secondary">Ditutup</span>
                            <?php endif; ?>
                        </div>
                        <div class="card-body p-0">
                            <div class="chat-messages" id="chat-messages" data-room-id="<?= $active_room_id ?>">
                                <div class="text-center p-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Memuat...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if ($room_details['status'] === 'active'): ?>
                                <form id="message-form" class="d-flex">
                                    <input type="text" id="message-input" class="form-control me-2" placeholder="Ketik pesan..." required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    <input type="hidden" id="room-id" value="<?= $active_room_id ?>">
                                </form>
                                <div class="mt-2 d-flex">
                                    <button id="emoji-button" class="btn btn-sm btn-outline-secondary me-2" type="button">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <button id="share-location" class="btn btn-sm btn-outline-secondary me-2" type="button">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </button>
                                    <div class="position-relative">
                                        <input type="file" id="file-upload" class="d-none" accept="image/*">
                                        <button id="upload-button" class="btn btn-sm btn-outline-secondary" type="button" onclick="document.getElementById('file-upload').click()">
                                            <i class="fas fa-image"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-secondary mb-0">
                                    Percakapan ini telah ditutup dan tidak dapat menerima pesan baru.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center p-5">
                            <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                            <h3>Tidak ada percakapan yang dipilih</h3>
                            <p class="text-muted">Pilih percakapan dari daftar atau mulai percakapan baru.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship - Portal Pengemudi</h5>
                    <p>Kelola pengiriman Anda dengan mudah.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Modal untuk Mulai Percakapan Baru -->
    <div class="modal fade" id="newChatModal" tabindex="-1" aria-labelledby="newChatModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newChatModalLabel">Mulai Percakapan Baru</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newChatForm">
                        <div class="mb-3">
                            <label for="chatType" class="form-label">Jenis Percakapan</label>
                            <select class="form-select" id="chatType" required>
                                <option value="">Pilih jenis percakapan</option>
                                <option value="support">Dukungan Umum</option>
                                <option value="complaint">Keluhan</option>
                                <?php if (!empty($activeOrders)): ?>
                                <option value="order">Tentang Pesanan</option>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="mb-3" id="orderSelectContainer" style="display: none;">
                            <label for="orderSelect" class="form-label">Pilih Pesanan</label>
                            <select class="form-select" id="orderSelect">
                                <option value="">Pilih pesanan</option>
                                <?php
                                // Get active orders for the driver
                                $stmt = $conn->prepare("
                                    SELECT o.order_id, r.name as restaurant_name, u.name as customer_name, o.created_at
                                    FROM orders o
                                    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                                    JOIN users u ON o.user_id = u.user_id
                                    WHERE o.driver_id = :driver_id
                                    AND o.order_status IN ('confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way')
                                    ORDER BY o.created_at DESC
                                    LIMIT 10
                                ");
                                $stmt->bindParam(':driver_id', $driver_id);
                                $stmt->execute();
                                $activeOrders = $stmt->fetchAll();

                                foreach ($activeOrders as $order):
                                ?>
                                <option value="<?= $order['order_id'] ?>">
                                    Pesanan #<?= $order['order_id'] ?> - <?= $order['restaurant_name'] ?> (<?= $order['customer_name'] ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="messageText" class="form-label">Pesan</label>
                            <textarea class="form-control" id="messageText" rows="3" placeholder="Ketik pesan Anda di sini..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="startChatBtn">Mulai Percakapan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/user-experience.js"></script>
    <script src="../js/chat.js"></script>

    <script>
    // Script untuk menangani pembuatan percakapan baru
    document.addEventListener('DOMContentLoaded', function() {
        const chatTypeSelect = document.getElementById('chatType');
        const orderSelectContainer = document.getElementById('orderSelectContainer');
        const orderSelect = document.getElementById('orderSelect');
        const messageText = document.getElementById('messageText');
        const startChatBtn = document.getElementById('startChatBtn');

        // Tampilkan/sembunyikan pilihan pesanan berdasarkan jenis percakapan
        chatTypeSelect.addEventListener('change', function() {
            if (this.value === 'order') {
                orderSelectContainer.style.display = 'block';
                orderSelect.setAttribute('required', 'required');
            } else {
                orderSelectContainer.style.display = 'none';
                orderSelect.removeAttribute('required');
            }
        });

        // Tangani klik tombol mulai percakapan
        startChatBtn.addEventListener('click', function() {
            const chatType = chatTypeSelect.value;
            const message = messageText.value.trim();

            if (!chatType) {
                alert('Silakan pilih jenis percakapan');
                return;
            }

            if (!message) {
                alert('Silakan masukkan pesan');
                return;
            }

            if (chatType === 'order' && !orderSelect.value) {
                alert('Silakan pilih pesanan');
                return;
            }

            // Siapkan data untuk API
            const data = {
                room_type: chatType,
                message: message
            };

            if (chatType === 'order') {
                data.order_id = orderSelect.value;
            }

            // Kirim permintaan ke API untuk membuat ruang chat baru
            fetch('../api/chat.php?action=create_room', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Tutup modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('newChatModal'));
                    modal.hide();

                    // Arahkan ke ruang chat baru
                    window.location.href = `chat.php?room_id=${data.data.room_id}`;
                } else {
                    alert('Gagal membuat percakapan: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Terjadi kesalahan saat membuat percakapan. Silakan coba lagi nanti.');
            });
        });
    });
    </script>
</body>
</html>
