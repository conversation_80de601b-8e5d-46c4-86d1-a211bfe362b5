<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = $_POST['name'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $vehicle_type = $_POST['vehicle_type'] ?? '';
        $license_plate = $_POST['license_plate'] ?? '';
        
        // Validate inputs
        if (empty($name) || empty($phone) || empty($vehicle_type)) {
            $error = 'Silakan isi semua bidang yang diperlukan';
        } else {
            try {
                // Update driver profile
                $stmt = $conn->prepare("
                    UPDATE drivers SET 
                        name = :name, 
                        phone = :phone, 
                        vehicle_type = :vehicle_type, 
                        license_plate = :license_plate
                    WHERE driver_id = :driver_id
                ");
                
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':phone', $phone);
                $stmt->bindParam(':vehicle_type', $vehicle_type);
                $stmt->bindParam(':license_plate', $license_plate);
                $stmt->bindParam(':driver_id', $driver_id);
                
                if ($stmt->execute()) {
                    $success = 'Profil berhasil diperbarui!';
                    
                    // Refresh driver data
                    $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
                    $stmt->bindParam(':driver_id', $driver_id);
                    $stmt->execute();
                    $driver = $stmt->fetch();
                    
                    // Update session name
                    $_SESSION['driver_name'] = $name;
                } else {
                    $error = 'Gagal memperbarui profil';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validate inputs
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'Silakan isi semua bidang kata sandi';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Kata sandi baru tidak cocok dengan konfirmasi';
        } elseif (strlen($new_password) < 8) {
            $error = 'Kata sandi baru harus minimal 8 karakter';
        } else {
            try {
                // Verify current password
                if (password_verify($current_password, $driver['password'])) {
                    // Hash new password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    
                    // Update password
                    $stmt = $conn->prepare("UPDATE drivers SET password = :password WHERE driver_id = :driver_id");
                    $stmt->bindParam(':password', $hashed_password);
                    $stmt->bindParam(':driver_id', $driver_id);
                    
                    if ($stmt->execute()) {
                        $success = 'Kata sandi berhasil diperbarui!';
                    } else {
                        $error = 'Gagal memperbarui kata sandi';
                    }
                } else {
                    $error = 'Kata sandi saat ini tidak valid';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Profile Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Profil Pengemudi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Profil</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pengemudi</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="avatar-circle mx-auto mb-3">
                                <span class="avatar-initials"><?= substr($driver['name'], 0, 1) ?></span>
                            </div>
                            <h4><?= $driver['name'] ?></h4>
                            <p class="text-muted mb-0">ID: <?= $driver['driver_id'] ?></p>
                        </div>
                        
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-envelope me-2"></i>Email</span>
                                <span class="text-muted"><?= $driver['email'] ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-phone me-2"></i>Telepon</span>
                                <span class="text-muted"><?= $driver['phone'] ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-motorcycle me-2"></i>Kendaraan</span>
                                <span class="text-muted">
                                    <?php
                                    switch ($driver['vehicle_type']) {
                                        case 'motorcycle':
                                            echo 'Sepeda Motor';
                                            break;
                                        case 'car':
                                            echo 'Mobil';
                                            break;
                                        case 'bicycle':
                                            echo 'Sepeda';
                                            break;
                                        default:
                                            echo $driver['vehicle_type'];
                                    }
                                    ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-id-card me-2"></i>Plat Nomor</span>
                                <span class="text-muted"><?= $driver['license_plate'] ?: '-' ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-calendar-alt me-2"></i>Bergabung</span>
                                <span class="text-muted"><?= date('d/m/Y', strtotime($driver['created_at'])) ?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Profil</h5>
                    </div>
                    <div class="card-body">
                        <form action="profile.php" method="post">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Nama Lengkap</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($driver['name']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Alamat Email</label>
                                <input type="email" class="form-control" id="email" value="<?= htmlspecialchars($driver['email']) ?>" readonly>
                                <small class="text-muted">Email tidak dapat diubah</small>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">Nomor Telepon</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($driver['phone']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="vehicle_type" class="form-label">Jenis Kendaraan</label>
                                <select class="form-select" id="vehicle_type" name="vehicle_type" required>
                                    <option value="motorcycle" <?= $driver['vehicle_type'] === 'motorcycle' ? 'selected' : '' ?>>Sepeda Motor</option>
                                    <option value="car" <?= $driver['vehicle_type'] === 'car' ? 'selected' : '' ?>>Mobil</option>
                                    <option value="bicycle" <?= $driver['vehicle_type'] === 'bicycle' ? 'selected' : '' ?>>Sepeda</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="license_plate" class="form-label">Plat Nomor</label>
                                <input type="text" class="form-control" id="license_plate" name="license_plate" value="<?= htmlspecialchars($driver['license_plate']) ?>">
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ubah Kata Sandi</h5>
                    </div>
                    <div class="card-body">
                        <form action="profile.php" method="post">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Kata Sandi Saat Ini</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <small class="text-muted">Minimal 8 karakter</small>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Konfirmasi Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Ubah Kata Sandi</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .avatar-circle {
            width: 100px;
            height: 100px;
            background-color: #007bff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .avatar-initials {
            color: white;
            font-size: 48px;
            font-weight: bold;
            text-transform: uppercase;
        }
    </style>
</body>
</html>
