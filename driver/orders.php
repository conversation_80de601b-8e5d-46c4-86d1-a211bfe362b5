<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Get filter parameters
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Set status filter
$where_clause = "WHERE o.driver_id = :driver_id";
$params = [':driver_id' => $driver_id];

if ($status !== 'all') {
    $where_clause .= " AND o.order_status = :status";
    $params[':status'] = $status;
}

// Set date filter
if (!empty($start_date) && !empty($end_date)) {
    $where_clause .= " AND DATE(o.created_at) BETWEEN :start_date AND :end_date";
    $params[':start_date'] = $start_date;
    $params[':end_date'] = $end_date;
}

// Get orders
$stmt = $conn->prepare("
    SELECT
        o.*,
        r.name as restaurant_name,
        r.address as restaurant_address,
        u.name as customer_name,
        u.phone as customer_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    $where_clause
    ORDER BY
        CASE
            WHEN o.order_status IN ('confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way') THEN 0
            ELSE 1
        END,
        o.created_at DESC
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$orders = $stmt->fetchAll();

// Get available orders count (ready for pickup and not assigned to any driver)
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM orders
    WHERE order_status = 'ready_for_pickup'
    AND driver_id IS NULL
");
$stmt->execute();
$availableOrdersCount = $stmt->fetch()['count'] ?? 0;

// Get unread notifications count
$unreadNotificationsCount = getUnreadNotificationsCount($driver_id);

// Count orders by status
$stmt = $conn->prepare("
    SELECT
        order_status,
        COUNT(*) as count
    FROM orders
    WHERE driver_id = :driver_id
    GROUP BY order_status
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$status_counts = [];

while ($row = $stmt->fetch()) {
    $status_counts[$row['order_status']] = $row['count'];
}

// Get total counts
$active_count = 0;
$completed_count = 0;
$cancelled_count = 0;

foreach ($status_counts as $status => $count) {
    if (in_array($status, ['confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way'])) {
        $active_count += $count;
    } elseif ($status === 'delivered') {
        $completed_count += $count;
    } elseif (in_array($status, ['cancelled', 'rejected'])) {
        $cancelled_count += $count;
    }
}

$total_count = $active_count + $completed_count + $cancelled_count;

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pesanan Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifikasi
                            <?php if ($unreadNotificationsCount > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= $unreadNotificationsCount ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item active" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="available_orders.php">Pesanan Tersedia
                                <?php if ($availableOrdersCount > 0): ?>
                                    <span class="badge bg-danger ms-2"><?= $availableOrdersCount ?></span>
                                <?php endif; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Orders Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Pesanan Pengemudi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pesanan</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Pesanan</h6>
                                <h2 class="mb-0"><?= $total_count ?></h2>
                            </div>
                            <i class="fas fa-shopping-bag fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pesanan Aktif</h6>
                                <h2 class="mb-0"><?= $active_count ?></h2>
                            </div>
                            <i class="fas fa-motorcycle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pesanan Selesai</h6>
                                <h2 class="mb-0"><?= $completed_count ?></h2>
                            </div>
                            <i class="fas fa-check-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-danger text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pesanan Dibatalkan</h6>
                                <h2 class="mb-0"><?= $cancelled_count ?></h2>
                            </div>
                            <i class="fas fa-times-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Filter Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <form action="orders.php" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status Pesanan</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>Semua Status</option>
                                    <option value="confirmed" <?= $status === 'confirmed' ? 'selected' : '' ?>>Dikonfirmasi</option>
                                    <option value="preparing" <?= $status === 'preparing' ? 'selected' : '' ?>>Sedang Disiapkan</option>
                                    <option value="ready_for_pickup" <?= $status === 'ready_for_pickup' ? 'selected' : '' ?>>Siap Diambil</option>
                                    <option value="picked_up" <?= $status === 'picked_up' ? 'selected' : '' ?>>Sudah Diambil</option>
                                    <option value="on_the_way" <?= $status === 'on_the_way' ? 'selected' : '' ?>>Dalam Perjalanan</option>
                                    <option value="delivered" <?= $status === 'delivered' ? 'selected' : '' ?>>Terkirim</option>
                                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>Dibatalkan</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                            </div>

                            <div class="col-md-3">
                                <label for="end_date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Daftar Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Pesanan</h4>
                                <p class="text-muted">Tidak ada pesanan yang sesuai dengan filter yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($orders as $order): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card order-card h-100">
                                            <div class="card-header">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Pesanan #<?= $order['order_id'] ?></h6>
                                                    <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?>"><?= getOrderStatusText($order['order_status']) ?></span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-calendar-alt me-2"></i>Tanggal:</h6>
                                                    <p class="mb-0"><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-store me-2"></i>Restoran:</h6>
                                                    <p class="mb-1"><?= $order['restaurant_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['restaurant_address'] ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-user me-2"></i>Pelanggan:</h6>
                                                    <p class="mb-1"><?= $order['customer_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['customer_phone'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['delivery_address'] ?></p>
                                                </div>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill me-2"></i>Total:</h6>
                                                        <p class="mb-0"><?= formatCurrency($order['total_amount']) ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill-wave me-2"></i>Biaya Antar:</h6>
                                                        <p class="mb-0"><?= formatCurrency($order['delivery_fee']) ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <div class="d-grid gap-2">
                                                    <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-primary">Lihat Detail</a>
                                                    <?php
                                                    // Periksa apakah sudah ada ruang chat untuk pesanan ini
                                                    $stmt = $conn->prepare("
                                                        SELECT cr.room_id,
                                                               (SELECT COUNT(*) FROM chat_messages cm
                                                                WHERE cm.room_id = cr.room_id
                                                                AND cm.sender_type != 'driver'
                                                                AND cm.sender_id != :driver_id
                                                                AND cm.is_read = 0) as unread_count
                                                        FROM chat_rooms cr
                                                        JOIN chat_participants cp ON cr.room_id = cp.room_id
                                                        WHERE cr.order_id = :order_id
                                                        AND cp.user_type = 'driver'
                                                        AND cp.user_id = :driver_id
                                                        LIMIT 1
                                                    ");
                                                    $stmt->bindParam(':order_id', $order['order_id']);
                                                    $stmt->bindParam(':driver_id', $driver_id);
                                                    $stmt->execute();
                                                    $chatRoom = $stmt->fetch();

                                                    $chatBtnClass = "btn btn-outline-success";
                                                    $chatBtnText = "<i class='fas fa-comments me-2'></i>Chat";
                                                    $chatUrl = "chat.php?start_chat=order&order_id={$order['order_id']}";

                                                    if ($chatRoom && $chatRoom['unread_count'] > 0) {
                                                        $chatBtnClass = "btn btn-danger";
                                                        $chatBtnText = "<i class='fas fa-comments me-2'></i>Chat <span class='badge bg-white text-danger'>{$chatRoom['unread_count']}</span>";
                                                        $chatUrl = "chat.php?room_id={$chatRoom['room_id']}";
                                                    } elseif ($chatRoom) {
                                                        $chatUrl = "chat.php?room_id={$chatRoom['room_id']}";
                                                    }
                                                    ?>
                                                    <a href="<?= $chatUrl ?>" class="<?= $chatBtnClass ?>"><?= $chatBtnText ?></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
