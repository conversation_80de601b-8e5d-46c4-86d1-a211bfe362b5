<?php
// Start session
session_start();

// Set cache control headers to prevent caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Handle order assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_order'])) {
    $order_id = $_POST['order_id'];

    // Check if order is still available
    $stmt = $conn->prepare("
        SELECT * FROM orders
        WHERE order_id = :order_id
        AND order_status = 'ready_for_pickup'
        AND driver_id IS NULL
    ");
    $stmt->bindParam(':order_id', $order_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $order = $stmt->fetch();

        try {
            // Begin transaction
            $conn->beginTransaction();

            // Update driver status to busy
            $stmt = $conn->prepare("UPDATE drivers SET status = 'busy' WHERE driver_id = :driver_id");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();

            // Assign driver to order
            $stmt = $conn->prepare("
                UPDATE orders
                SET driver_id = :driver_id, updated_at = NOW()
                WHERE order_id = :order_id
            ");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();

            // Add to order tracking
            $notes = "Pesanan diambil oleh pengemudi: " . $_SESSION['driver_name'];
            $stmt = $conn->prepare("
                INSERT INTO order_tracking (
                    order_id, status, notes, driver_id
                ) VALUES (
                    :order_id, 'assigned_to_driver', :notes, :driver_id
                )
            ");
            $stmt->bindParam(':order_id', $order_id);
            $stmt->bindParam(':notes', $notes);
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();

            // Commit transaction
            $conn->commit();

            // Redirect to order detail
            header("Location: order_detail.php?id=$order_id&success=Pesanan berhasil diambil");
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error = 'Database error: ' . $e->getMessage();
        }
    } else {
        $error = 'Pesanan tidak tersedia atau sudah diambil oleh pengemudi lain';
    }
}

// Get available orders (ready for pickup and not assigned to any driver)
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
           r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
           u.name as customer_name, u.phone as customer_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.order_status = 'ready_for_pickup'
    AND o.driver_id IS NULL
    ORDER BY o.created_at ASC
");
$stmt->execute();
$availableOrders = $stmt->fetchAll();

// Debug: Print available orders
echo "<!-- Debug: Available Orders -->";
echo "<!-- " . print_r($availableOrders, true) . " -->";

// Count unread notifications
$unread_count = getUnreadNotificationsCount($driver_id);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="30">
    <title>Pesanan Tersedia - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifikasi
                            <?php if ($unread_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= $unread_count ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item active" href="available_orders.php">Pesanan Tersedia</a></li>
                            <li><a class="dropdown-item" href="route_optimization.php">Optimasi Rute</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Available Orders Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Pesanan Tersedia</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pesanan Tersedia</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <button type="button" class="btn btn-primary" id="refresh-btn">
                    <i class="fas fa-sync-alt me-2"></i>Refresh
                </button>
            </div>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Halaman ini akan diperbarui secara otomatis setiap 30 detik. Anda juga dapat mengklik tombol "Refresh" untuk memperbarui halaman secara manual.
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Peta Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Daftar Pesanan Siap Diambil</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($availableOrders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Pesanan Tersedia</h4>
                                <p class="text-muted">Saat ini tidak ada pesanan yang siap untuk diambil.</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($availableOrders as $order): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card order-card h-100">
                                            <div class="card-header bg-warning text-dark">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Pesanan #<?= $order['order_id'] ?></h6>
                                                    <span class="badge bg-warning text-dark">Siap Diambil</span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-store me-2"></i>Restoran:</h6>
                                                    <p class="mb-1"><?= $order['restaurant_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['restaurant_address'] ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-user me-2"></i>Pelanggan:</h6>
                                                    <p class="mb-1"><?= $order['customer_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['customer_phone'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['delivery_address'] ?></p>
                                                </div>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill me-2"></i>Total:</h6>
                                                        <p class="mb-0"><?= formatCurrency($order['total_amount']) ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill-wave me-2"></i>Biaya Antar:</h6>
                                                        <p class="mb-0"><?= formatCurrency($order['delivery_fee']) ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <div class="d-flex gap-2 mb-2">
                                                    <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-outline-primary flex-grow-1">
                                                        <i class="fas fa-eye me-2"></i>Lihat Detail
                                                    </a>
                                                    <?php
                                                    // Periksa apakah sudah ada ruang chat untuk pesanan ini
                                                    $stmt = $conn->prepare("
                                                        SELECT cr.room_id,
                                                               (SELECT COUNT(*) FROM chat_messages cm
                                                                WHERE cm.room_id = cr.room_id
                                                                AND cm.sender_type != 'driver'
                                                                AND cm.sender_id != :driver_id
                                                                AND cm.is_read = 0) as unread_count
                                                        FROM chat_rooms cr
                                                        JOIN chat_participants cp ON cr.room_id = cp.room_id
                                                        WHERE cr.order_id = :order_id
                                                        AND cp.user_type = 'driver'
                                                        AND cp.user_id = :driver_id
                                                        LIMIT 1
                                                    ");
                                                    $stmt->bindParam(':order_id', $order['order_id']);
                                                    $stmt->bindParam(':driver_id', $driver_id);
                                                    $stmt->execute();
                                                    $chatRoom = $stmt->fetch();

                                                    $chatBtnClass = "btn btn-outline-success flex-grow-1";
                                                    $chatBtnText = "<i class='fas fa-comments me-2'></i>Chat";
                                                    $chatUrl = "chat.php?start_chat=order&order_id={$order['order_id']}";

                                                    if ($chatRoom && $chatRoom['unread_count'] > 0) {
                                                        $chatBtnClass = "btn btn-danger flex-grow-1";
                                                        $chatBtnText = "<i class='fas fa-comments me-2'></i>Chat <span class='badge bg-white text-danger'>{$chatRoom['unread_count']}</span>";
                                                        $chatUrl = "chat.php?room_id={$chatRoom['room_id']}";
                                                    } elseif ($chatRoom) {
                                                        $chatUrl = "chat.php?room_id={$chatRoom['room_id']}";
                                                    }
                                                    ?>
                                                    <a href="<?= $chatUrl ?>" class="<?= $chatBtnClass ?>"><?= $chatBtnText ?></a>
                                                </div>
                                                <form action="available_orders.php" method="post">
                                                    <input type="hidden" name="order_id" value="<?= $order['order_id'] ?>">
                                                    <input type="hidden" name="assign_order" value="1">
                                                    <button type="submit" class="btn btn-success w-100">
                                                        <i class="fas fa-check me-2"></i>Ambil Pesanan
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const map = L.map('map').setView([-6.2088, 106.8456], 12); // Jakarta coordinates

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add driver marker
        <?php if ($driver['current_latitude'] && $driver['current_longitude']): ?>
            const driverIcon = L.icon({
                iconUrl: '../assets/driver-marker.png',
                iconSize: [40, 40],
                iconAnchor: [20, 40],
                popupAnchor: [0, -40]
            });

            const driverMarker = L.marker([<?= $driver['current_latitude'] ?>, <?= $driver['current_longitude'] ?>], {
                icon: driverIcon
            }).addTo(map);
            driverMarker.bindPopup("<b>Lokasi Anda</b>").openPopup();
        <?php endif; ?>

        // Add order markers
        <?php foreach ($availableOrders as $order): ?>
            <?php if ($order['restaurant_latitude'] && $order['restaurant_longitude']): ?>
                const restaurantIcon = L.icon({
                    iconUrl: '../assets/restaurant-marker.png',
                    iconSize: [40, 40],
                    iconAnchor: [20, 40],
                    popupAnchor: [0, -40]
                });

                const restaurantMarker = L.marker([<?= $order['restaurant_latitude'] ?>, <?= $order['restaurant_longitude'] ?>], {
                    icon: restaurantIcon
                }).addTo(map);
                restaurantMarker.bindPopup("<b><?= $order['restaurant_name'] ?></b><br>Pesanan #<?= $order['order_id'] ?>");
            <?php endif; ?>
        <?php endforeach; ?>

        // Fit bounds to show all markers
        const bounds = [];
        <?php if ($driver['current_latitude'] && $driver['current_longitude']): ?>
            bounds.push([<?= $driver['current_latitude'] ?>, <?= $driver['current_longitude'] ?>]);
        <?php endif; ?>
        <?php foreach ($availableOrders as $order): ?>
            <?php if ($order['restaurant_latitude'] && $order['restaurant_longitude']): ?>
                bounds.push([<?= $order['restaurant_latitude'] ?>, <?= $order['restaurant_longitude'] ?>]);
            <?php endif; ?>
        <?php endforeach; ?>

        if (bounds.length > 0) {
            map.fitBounds(bounds);
        }

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', function() {
            // Add timestamp to URL to prevent caching
            window.location.href = 'available_orders.php?t=' + new Date().getTime();
        });
    </script>
</body>
</html>
