<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_location'])) {
    $latitude = $_POST['latitude'];
    $longitude = $_POST['longitude'];
    $status = $_POST['status'];
    
    // Validate input
    if (empty($latitude) || empty($longitude)) {
        $error = 'Koordinat latitude dan longitude harus diisi';
    } else {
        try {
            // Update driver location
            $stmt = $conn->prepare("
                UPDATE drivers 
                SET current_latitude = :latitude, current_longitude = :longitude, status = :status, location_updated_at = NOW()
                WHERE driver_id = :driver_id
            ");
            $stmt->bindParam(':latitude', $latitude);
            $stmt->bindParam(':longitude', $longitude);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();
            
            $success = 'Lokasi dan status berhasil diperbarui!';
            
            // Refresh driver data
            $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();
            $driver = $stmt->fetch();
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Get unread notifications count
$unreadNotificationsCount = getUnreadNotificationsCount($driver_id);

// Get available orders count
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM orders
    WHERE order_status = 'ready_for_pickup'
    AND driver_id IS NULL
");
$stmt->execute();
$availableOrdersCount = $stmt->fetch()['count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Lokasi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 400px;
            width: 100%;
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifikasi
                            <?php if ($unreadNotificationsCount > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= $unreadNotificationsCount ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="available_orders.php">Pesanan Tersedia
                                <?php if ($availableOrdersCount > 0): ?>
                                    <span class="badge bg-danger ms-2"><?= $availableOrdersCount ?></span>
                                <?php endif; ?>
                            </a></li>
                            <li><a class="dropdown-item active" href="location_settings.php">Lokasi</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Location Settings Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Pengaturan Lokasi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pengaturan Lokasi</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge <?= $driver['status'] === 'available' ? 'bg-success' : 'bg-secondary' ?> status-badge">
                    <?= $driver['status'] === 'available' ? 'Tersedia' : 'Tidak Tersedia' ?>
                </span>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Lokasi Saat Ini</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">
                            Atur lokasi Anda dengan tepat untuk menerima notifikasi pesanan yang siap diambil dari restoran terdekat.
                            Klik pada peta untuk memilih lokasi atau gunakan tombol "Gunakan Lokasi Saya" untuk menggunakan lokasi saat ini.
                        </p>
                        
                        <div id="map"></div>
                        
                        <form action="location_settings.php" method="post">
                            <input type="hidden" name="update_location" value="1">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="text" class="form-control" id="latitude" name="latitude" value="<?= $driver['current_latitude'] ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="text" class="form-control" id="longitude" name="longitude" value="<?= $driver['current_longitude'] ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="available" <?= $driver['status'] === 'available' ? 'selected' : '' ?>>Tersedia (Siap Menerima Pesanan)</option>
                                    <option value="busy" <?= $driver['status'] === 'busy' ? 'selected' : '' ?>>Sibuk (Sedang Mengantar)</option>
                                    <option value="offline" <?= $driver['status'] === 'offline' ? 'selected' : '' ?>>Offline (Tidak Tersedia)</option>
                                </select>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" id="use-my-location" class="btn btn-outline-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i>Gunakan Lokasi Saya
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Lokasi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Petunjuk Pengaturan Lokasi</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Klik pada peta untuk memilih lokasi Anda saat ini.</li>
                            <li>Koordinat latitude dan longitude akan otomatis terisi.</li>
                            <li>Pilih status Anda (Tersedia, Sibuk, atau Offline).</li>
                            <li>Klik tombol "Simpan Lokasi" untuk menyimpan perubahan.</li>
                        </ol>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Penting:</strong> Anda hanya akan menerima notifikasi pesanan yang siap diambil jika:
                            <ul class="mb-0 mt-2">
                                <li>Status Anda diatur ke "Tersedia"</li>
                                <li>Anda berada dalam radius 10km dari restoran</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pengemudi</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px">
                                    <i class="fas fa-user fa-2x text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= $driver['name'] ?></h6>
                                <p class="text-muted mb-0"><?= $driver['phone'] ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-motorcycle me-2 text-primary"></i>Kendaraan:</h6>
                            <p class="mb-0">
                                <?php
                                switch ($driver['vehicle_type']) {
                                    case 'motorcycle':
                                        echo 'Motor';
                                        break;
                                    case 'car':
                                        echo 'Mobil';
                                        break;
                                    case 'bicycle':
                                        echo 'Sepeda';
                                        break;
                                    default:
                                        echo $driver['vehicle_type'];
                                }
                                ?>
                                <?= !empty($driver['license_plate']) ? ' - ' . $driver['license_plate'] : '' ?>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-map-marker-alt me-2 text-primary"></i>Koordinat Saat Ini:</h6>
                            <p class="mb-0">
                                <?php if ($driver['current_latitude'] && $driver['current_longitude']): ?>
                                    Latitude: <?= $driver['current_latitude'] ?><br>
                                    Longitude: <?= $driver['current_longitude'] ?>
                                <?php else: ?>
                                    Belum diatur
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-clock me-2 text-primary"></i>Terakhir Diperbarui:</h6>
                            <p class="mb-0">
                                <?= $driver['location_updated_at'] ? date('d/m/Y H:i', strtotime($driver['location_updated_at'])) : 'Belum pernah' ?>
                            </p>
                        </div>
                        
                        <div class="alert <?= $driver['status'] === 'available' ? 'alert-success' : 'alert-secondary' ?>">
                            <i class="fas <?= $driver['status'] === 'available' ? 'fa-check-circle' : 'fa-times-circle' ?> me-2"></i>
                            Status: <strong><?= $driver['status'] === 'available' ? 'Tersedia' : ($driver['status'] === 'busy' ? 'Sibuk' : 'Offline') ?></strong>
                        </div>
                    </div>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Tersedia</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($availableOrdersCount > 0): ?>
                            <p>Ada <strong><?= $availableOrdersCount ?></strong> pesanan yang siap diambil.</p>
                            <a href="available_orders.php" class="btn btn-warning w-100">
                                <i class="fas fa-shopping-bag me-2"></i>Lihat Pesanan Tersedia
                            </a>
                        <?php else: ?>
                            <p>Tidak ada pesanan yang siap diambil saat ini.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const map = L.map('map').setView([<?= $driver['current_latitude'] ?: '-6.2088' ?>, <?= $driver['current_longitude'] ?: '106.8456' ?>], 15);
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Add marker for driver location
        let marker;
        if (<?= $driver['current_latitude'] && $driver['current_longitude'] ? 'true' : 'false' ?>) {
            marker = L.marker([<?= $driver['current_latitude'] ?: '-6.2088' ?>, <?= $driver['current_longitude'] ?: '106.8456' ?>]).addTo(map);
        }
        
        // Handle map click
        map.on('click', function(e) {
            // Update marker position
            if (marker) {
                marker.setLatLng(e.latlng);
            } else {
                marker = L.marker(e.latlng).addTo(map);
            }
            
            // Update form fields
            document.getElementById('latitude').value = e.latlng.lat.toFixed(8);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(8);
        });
        
        // Handle "Use My Location" button
        document.getElementById('use-my-location').addEventListener('click', function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    // Update map view
                    map.setView([lat, lng], 15);
                    
                    // Update marker position
                    if (marker) {
                        marker.setLatLng([lat, lng]);
                    } else {
                        marker = L.marker([lat, lng]).addTo(map);
                    }
                    
                    // Update form fields
                    document.getElementById('latitude').value = lat.toFixed(8);
                    document.getElementById('longitude').value = lng.toFixed(8);
                }, function(error) {
                    alert('Error getting location: ' + error.message);
                });
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        });
    </script>
</body>
</html>
