<?php
// Test file untuk memverifikasi status pesanan di dashboard driver
session_start();
require_once '../config/database.php';

// Simulasi driver login (ganti dengan driver_id yang sesuai)
$driver_id = 1; // Ganti dengan ID driver yang ada di database

echo "<h2>Test Dashboard Driver - Status Pesanan</h2>";

// Test 1: Cek pesanan aktif
echo "<h3>1. Pesanan Aktif untuk Driver ID: $driver_id</h3>";
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
           u.name as customer_name, u.phone as customer_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.driver_id = :driver_id
    AND o.order_status IN ('confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way')
    ORDER BY o.created_at DESC
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$activeOrders = $stmt->fetchAll();

echo "<p>Jumlah pesanan aktif: " . count($activeOrders) . "</p>";

if (!empty($activeOrders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Order ID</th><th>Status</th><th>Restaurant</th><th>Customer</th><th>Created</th></tr>";
    
    foreach ($activeOrders as $order) {
        $statusText = '';
        switch ($order['order_status']) {
            case 'confirmed':
                $statusText = 'Dikonfirmasi';
                break;
            case 'preparing':
                $statusText = 'Sedang Disiapkan';
                break;
            case 'ready_for_pickup':
                $statusText = 'Siap Diambil';
                break;
            case 'picked_up':
                $statusText = 'Sudah Diambil';
                break;
            case 'on_the_way':
                $statusText = 'Dalam Perjalanan';
                break;
            default:
                $statusText = 'Tidak Diketahui';
        }
        
        echo "<tr>";
        echo "<td>" . $order['order_id'] . "</td>";
        echo "<td style='background-color: " . ($order['order_status'] == 'on_the_way' ? '#fff3cd' : '#f8f9fa') . ";'>" . $order['order_status'] . " (" . $statusText . ")</td>";
        echo "<td>" . $order['restaurant_name'] . "</td>";
        echo "<td>" . $order['customer_name'] . "</td>";
        echo "<td>" . $order['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Tidak ada pesanan aktif ditemukan.</p>";
}

// Test 2: Cek semua pesanan untuk driver ini
echo "<h3>2. Semua Pesanan untuk Driver ID: $driver_id</h3>";
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.driver_id = :driver_id
    ORDER BY o.created_at DESC
    LIMIT 10
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$allOrders = $stmt->fetchAll();

echo "<p>Total pesanan (10 terbaru): " . count($allOrders) . "</p>";

if (!empty($allOrders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Order ID</th><th>Status</th><th>Restaurant</th><th>Created</th></tr>";
    
    foreach ($allOrders as $order) {
        echo "<tr>";
        echo "<td>" . $order['order_id'] . "</td>";
        echo "<td style='background-color: " . ($order['order_status'] == 'on_the_way' ? '#fff3cd' : '#f8f9fa') . ";'>" . $order['order_status'] . "</td>";
        echo "<td>" . $order['restaurant_name'] . "</td>";
        echo "<td>" . $order['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test 3: Cek apakah ada pesanan dengan status 'on_the_way'
echo "<h3>3. Pesanan dengan Status 'on_the_way'</h3>";
$stmt = $conn->prepare("
    SELECT COUNT(*) as count FROM orders
    WHERE driver_id = :driver_id
    AND order_status = 'on_the_way'
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$onTheWayCount = $stmt->fetch()['count'];

echo "<p>Jumlah pesanan 'Dalam Perjalanan': $onTheWayCount</p>";

if ($onTheWayCount > 0) {
    echo "<p style='color: green;'>✓ Ada pesanan dengan status 'on_the_way' yang seharusnya ditampilkan di dashboard.</p>";
} else {
    echo "<p style='color: orange;'>⚠ Tidak ada pesanan dengan status 'on_the_way' saat ini.</p>";
}

// Test 4: Simulasi status display
echo "<h3>4. Simulasi Tampilan Status</h3>";
$testStatuses = ['confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way', 'delivered'];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Status Code</th><th>Status Text</th><th>CSS Class</th><th>Preview</th></tr>";

foreach ($testStatuses as $status) {
    $statusClass = '';
    $statusText = '';
    
    switch ($status) {
        case 'confirmed':
            $statusClass = 'bg-info';
            $statusText = 'Dikonfirmasi';
            break;
        case 'preparing':
            $statusClass = 'bg-primary';
            $statusText = 'Sedang Disiapkan';
            break;
        case 'ready_for_pickup':
            $statusClass = 'bg-warning text-dark';
            $statusText = 'Siap Diambil';
            break;
        case 'picked_up':
            $statusClass = 'bg-success';
            $statusText = 'Sudah Diambil';
            break;
        case 'on_the_way':
            $statusClass = 'bg-warning text-dark';
            $statusText = 'Dalam Perjalanan';
            break;
        case 'delivered':
            $statusClass = 'bg-success';
            $statusText = 'Terkirim';
            break;
        default:
            $statusClass = 'bg-secondary';
            $statusText = 'Tidak Diketahui';
    }
    
    $bgColor = '';
    if (strpos($statusClass, 'bg-warning') !== false) {
        $bgColor = '#fff3cd';
    } elseif (strpos($statusClass, 'bg-success') !== false) {
        $bgColor = '#d1e7dd';
    } elseif (strpos($statusClass, 'bg-info') !== false) {
        $bgColor = '#d1ecf1';
    } elseif (strpos($statusClass, 'bg-primary') !== false) {
        $bgColor = '#cfe2ff';
    } else {
        $bgColor = '#e2e3e5';
    }
    
    echo "<tr>";
    echo "<td>$status</td>";
    echo "<td>$statusText</td>";
    echo "<td>$statusClass</td>";
    echo "<td style='background-color: $bgColor; padding: 5px;'><span style='padding: 3px 8px; border-radius: 3px; background-color: $bgColor;'>$statusText</span></td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";
echo "<p><strong>Catatan:</strong> Status 'Dalam Perjalanan' seharusnya ditampilkan dengan warna kuning (warning) untuk membedakannya dari status lain.</p>";
echo "<p><a href='dashboard.php'>← Kembali ke Dashboard</a></p>";
?>
