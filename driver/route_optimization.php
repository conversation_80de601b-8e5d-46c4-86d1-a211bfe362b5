<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Get active orders assigned to this driver
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
           r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
           u.name as customer_name, u.phone as customer_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.driver_id = :driver_id
    AND o.order_status IN ('confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way')
    ORDER BY
        CASE
            WHEN o.order_status = 'ready_for_pickup' THEN 1
            WHEN o.order_status = 'picked_up' THEN 2
            WHEN o.order_status = 'on_the_way' THEN 3
            WHEN o.order_status = 'preparing' THEN 4
            WHEN o.order_status = 'confirmed' THEN 5
            ELSE 6
        END,
        o.created_at ASC
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$activeOrders = $stmt->fetchAll();

// Get driver's current location
$driverLat = $driver['current_latitude'] ?: -6.2088;  // Default to Jakarta if not set
$driverLng = $driver['current_longitude'] ?: 106.8456;

// Prepare data for route optimization
$waypoints = [];
$orderDetails = [];

// Add driver's current location as the starting point
$waypoints[] = [
    'lat' => $driverLat,
    'lng' => $driverLng,
    'type' => 'driver',
    'name' => 'Lokasi Anda Saat Ini'
];

// Add active orders to waypoints
foreach ($activeOrders as $order) {
    // Add restaurant location if order is not picked up yet
    if (in_array($order['order_status'], ['confirmed', 'preparing', 'ready_for_pickup'])) {
        $waypoints[] = [
            'lat' => $order['restaurant_latitude'],
            'lng' => $order['restaurant_longitude'],
            'type' => 'restaurant',
            'name' => $order['restaurant_name'],
            'order_id' => $order['order_id'],
            'status' => $order['order_status']
        ];
    }

    // Add customer location
    $waypoints[] = [
        'lat' => $order['delivery_latitude'],
        'lng' => $order['delivery_longitude'],
        'type' => 'customer',
        'name' => $order['customer_name'],
        'order_id' => $order['order_id'],
        'status' => $order['order_status'],
        'address' => $order['delivery_address']
    ];

    // Store order details for display
    $orderDetails[$order['order_id']] = [
        'order_id' => $order['order_id'],
        'restaurant_name' => $order['restaurant_name'],
        'restaurant_address' => $order['restaurant_address'],
        'customer_name' => $order['customer_name'],
        'customer_phone' => $order['customer_phone'],
        'delivery_address' => $order['delivery_address'],
        'status' => $order['order_status'],
        'total_amount' => $order['total_amount'],
        'delivery_fee' => $order['delivery_fee']
    ];
}

// Calculate optimal route (simplified version)
// In a real implementation, you would use a more sophisticated algorithm
// or a third-party routing API like Google Maps Directions API
function calculateOptimalRoute($waypoints) {
    // Simple implementation: prioritize by order status
    // Ready for pickup -> Picked up -> On the way -> Preparing -> Confirmed

    $driver = array_shift($waypoints); // Remove driver from waypoints

    // Sort waypoints by priority
    usort($waypoints, function($a, $b) {
        $priorityA = getPriority($a);
        $priorityB = getPriority($b);

        if ($priorityA === $priorityB) {
            // If same priority, restaurants come before customers
            if ($a['type'] !== $b['type']) {
                return $a['type'] === 'restaurant' ? -1 : 1;
            }

            // If both are same type and have order_id, sort by order_id
            if (isset($a['order_id']) && isset($b['order_id'])) {
                return $a['order_id'] - $b['order_id'];
            }
        }

        return $priorityA - $priorityB;
    });

    // Add driver back as the first waypoint
    array_unshift($waypoints, $driver);

    return $waypoints;
}

function getPriority($waypoint) {
    if (!isset($waypoint['status'])) {
        return 999; // Driver or unknown
    }

    switch ($waypoint['status']) {
        case 'ready_for_pickup':
            return $waypoint['type'] === 'restaurant' ? 1 : 4;
        case 'picked_up':
            return $waypoint['type'] === 'customer' ? 2 : 999;
        case 'on_the_way':
            return $waypoint['type'] === 'customer' ? 3 : 999;
        case 'preparing':
            return $waypoint['type'] === 'restaurant' ? 5 : 6;
        case 'confirmed':
            return $waypoint['type'] === 'restaurant' ? 7 : 8;
        default:
            return 999;
    }
}

// Calculate optimal route
$optimalRoute = calculateOptimalRoute($waypoints);

// Calculate estimated times and distances
// In a real implementation, you would use a routing API to get accurate estimates
function calculateEstimates($route) {
    $estimates = [];
    $totalDistance = 0;
    $totalTime = 0;

    for ($i = 0; $i < count($route) - 1; $i++) {
        $from = $route[$i];
        $to = $route[$i + 1];

        // Calculate straight-line distance (Haversine formula)
        $distance = haversineDistance(
            $from['lat'], $from['lng'],
            $to['lat'], $to['lng']
        );

        // Estimate time: assume average speed of 30 km/h in city traffic
        // Convert distance to hours: distance (km) / speed (km/h)
        // Then convert to minutes: hours * 60
        $time = ($distance / 30) * 60;

        $estimates[] = [
            'from' => $from,
            'to' => $to,
            'distance' => $distance,
            'time' => $time
        ];

        $totalDistance += $distance;
        $totalTime += $time;
    }

    return [
        'segments' => $estimates,
        'totalDistance' => $totalDistance,
        'totalTime' => $totalTime
    ];
}

function haversineDistance($lat1, $lon1, $lat2, $lon2) {
    $earthRadius = 6371; // in kilometers

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLon/2) * sin($dLon/2);

    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    $distance = $earthRadius * $c;

    return $distance;
}

// Calculate estimates
$routeEstimates = calculateEstimates($optimalRoute);

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimasi Rute - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet Routing Machine CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 500px;
            width: 100%;
            margin-bottom: 15px;
        }
        .route-card {
            transition: all 0.3s ease;
        }
        .route-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .waypoint-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -30px;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li><a class="dropdown-item active" href="route_optimization.php">Optimasi Rute</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Route Optimization Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Optimasi Rute</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Optimasi Rute</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali ke Dasbor
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if (empty($activeOrders)): ?>
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-route fa-3x text-muted mb-3"></i>
                    <h4>Tidak Ada Pesanan Aktif</h4>
                    <p class="text-muted">Anda tidak memiliki pesanan aktif untuk dioptimalkan rutenya.</p>
                    <a href="dashboard.php" class="btn btn-primary mt-3">Kembali ke Dasbor</a>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Peta Rute Optimal</h5>
                        </div>
                        <div class="card-body">
                            <div id="map"></div>
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">Informasi Rute</h5>
                                        <p class="mb-0">Total jarak: <strong><?= number_format($routeEstimates['totalDistance'], 1) ?> km</strong></p>
                                        <p class="mb-0">Estimasi waktu total: <strong><?= floor($routeEstimates['totalTime'] / 60) ?> jam <?= round($routeEstimates['totalTime'] % 60) ?> menit</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Urutan Rute Optimal</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <?php foreach ($optimalRoute as $index => $waypoint): ?>
                                    <?php
                                    $iconClass = '';
                                    $bgClass = '';

                                    switch ($waypoint['type']) {
                                        case 'driver':
                                            $iconClass = 'fa-user';
                                            $bgClass = 'bg-info';
                                            break;
                                        case 'restaurant':
                                            $iconClass = 'fa-store';
                                            $bgClass = 'bg-success';
                                            break;
                                        case 'customer':
                                            $iconClass = 'fa-home';
                                            $bgClass = 'bg-warning';
                                            break;
                                    }

                                    // Get estimate to next waypoint
                                    $estimateText = '';
                                    if ($index < count($optimalRoute) - 1) {
                                        $segment = $routeEstimates['segments'][$index];
                                        $distance = number_format($segment['distance'], 1);
                                        $minutes = round($segment['time']);
                                        $estimateText = "$distance km • $minutes menit";
                                    }
                                    ?>
                                    <div class="timeline-item">
                                        <div class="d-flex">
                                            <div class="waypoint-icon <?= $bgClass ?> me-3">
                                                <i class="fas <?= $iconClass ?>"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><?= $index + 1 ?>. <?= $waypoint['name'] ?></h6>
                                                <?php if ($waypoint['type'] === 'restaurant' && isset($waypoint['order_id'])): ?>
                                                    <p class="small mb-1">
                                                        <span class="badge bg-primary">Pesanan #<?= $waypoint['order_id'] ?></span>
                                                        <?php
                                                        $statusText = '';
                                                        $statusClass = '';
                                                        switch ($waypoint['status']) {
                                                            case 'confirmed':
                                                                $statusText = 'Dikonfirmasi';
                                                                $statusClass = 'bg-info';
                                                                break;
                                                            case 'preparing':
                                                                $statusText = 'Sedang Disiapkan';
                                                                $statusClass = 'bg-primary';
                                                                break;
                                                            case 'ready_for_pickup':
                                                                $statusText = 'Siap Diambil';
                                                                $statusClass = 'bg-warning text-dark';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                                    </p>
                                                    <p class="small text-muted mb-1">Ambil pesanan di restoran</p>
                                                <?php elseif ($waypoint['type'] === 'customer' && isset($waypoint['order_id'])): ?>
                                                    <p class="small mb-1">
                                                        <span class="badge bg-primary">Pesanan #<?= $waypoint['order_id'] ?></span>
                                                        <?php
                                                        $statusText = '';
                                                        $statusClass = '';
                                                        switch ($waypoint['status']) {
                                                            case 'confirmed':
                                                            case 'preparing':
                                                            case 'ready_for_pickup':
                                                                $statusText = 'Belum Diambil';
                                                                $statusClass = 'bg-secondary';
                                                                break;
                                                            case 'picked_up':
                                                                $statusText = 'Sudah Diambil';
                                                                $statusClass = 'bg-success';
                                                                break;
                                                            case 'on_the_way':
                                                                $statusText = 'Dalam Perjalanan';
                                                                $statusClass = 'bg-primary';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                                    </p>
                                                    <p class="small text-muted mb-1"><?= $waypoint['address'] ?></p>
                                                <?php endif; ?>

                                                <?php if (!empty($estimateText)): ?>
                                                    <div class="d-flex align-items-center mt-2">
                                                        <i class="fas fa-arrow-down text-muted me-2"></i>
                                                        <span class="small text-muted"><?= $estimateText ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Detail Pesanan Aktif</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($activeOrders as $order): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card route-card h-100">
                                            <div class="card-header">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Pesanan #<?= $order['order_id'] ?></h6>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    switch ($order['order_status']) {
                                                        case 'confirmed':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'Dikonfirmasi';
                                                            break;
                                                        case 'preparing':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Sedang Disiapkan';
                                                            break;
                                                        case 'ready_for_pickup':
                                                            $statusClass = 'bg-warning text-dark';
                                                            $statusText = 'Siap Diambil';
                                                            break;
                                                        case 'picked_up':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'Sudah Diambil';
                                                            break;
                                                        case 'on_the_way':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Dalam Perjalanan';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'Tidak Diketahui';
                                                    }
                                                    ?>
                                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-store me-2"></i>Restoran:</h6>
                                                    <p class="mb-1"><?= $order['restaurant_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['restaurant_address'] ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-user me-2"></i>Pelanggan:</h6>
                                                    <p class="mb-1"><?= $order['customer_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['customer_phone'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['delivery_address'] ?></p>
                                                </div>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill me-2"></i>Total:</h6>
                                                        <p class="mb-0">Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill-wave me-2"></i>Biaya Antar:</h6>
                                                        <p class="mb-0">Rp<?= number_format($order['delivery_fee'] * 15000, 0, ',', '.') ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-primary w-100">Lihat Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Leaflet Routing Machine JS -->
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script>
        // Initialize map
        var map = L.map('map');

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Define custom icons
        var driverIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #17a2b8; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;"><i class="fas fa-user"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        var restaurantIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #28a745; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;"><i class="fas fa-store"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        var customerIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #ffc107; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: black;"><i class="fas fa-home"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        // Add waypoints to map
        var waypoints = <?= json_encode($optimalRoute) ?>;
        var routingWaypoints = [];
        var bounds = [];

        waypoints.forEach(function(waypoint, index) {
            var icon;
            var popupContent = '';

            switch (waypoint.type) {
                case 'driver':
                    icon = driverIcon;
                    popupContent = '<b>' + waypoint.name + '</b>';
                    break;
                case 'restaurant':
                    icon = restaurantIcon;
                    popupContent = '<b>Restoran:</b> ' + waypoint.name;
                    if (waypoint.order_id) {
                        popupContent += '<br><span class="badge bg-primary">Pesanan #' + waypoint.order_id + '</span>';
                    }
                    break;
                case 'customer':
                    icon = customerIcon;
                    popupContent = '<b>Pelanggan:</b> ' + waypoint.name;
                    if (waypoint.order_id) {
                        popupContent += '<br><span class="badge bg-primary">Pesanan #' + waypoint.order_id + '</span>';
                    }
                    if (waypoint.address) {
                        popupContent += '<br>' + waypoint.address;
                    }
                    break;
            }

            var marker = L.marker([waypoint.lat, waypoint.lng], {icon: icon}).addTo(map);
            marker.bindPopup(popupContent);

            // Add index number to marker
            var numberIcon = L.divIcon({
                className: 'custom-div-icon',
                html: '<div style="background-color: #007bff; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;">' + (index + 1) + '</div>',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });

            L.marker([waypoint.lat, waypoint.lng], {icon: numberIcon}).addTo(map);

            // Add to routing waypoints
            routingWaypoints.push(L.latLng(waypoint.lat, waypoint.lng));

            // Add to bounds
            bounds.push([waypoint.lat, waypoint.lng]);
        });

        // Create route
        if (routingWaypoints.length > 1) {
            var control = L.Routing.control({
                waypoints: routingWaypoints,
                routeWhileDragging: false,
                showAlternatives: false,
                fitSelectedRoutes: false,
                show: false, // Hide the instruction panel
                lineOptions: {
                    styles: [{color: '#007bff', opacity: 0.8, weight: 5}]
                },
                createMarker: function() { return null; } // Don't create default markers
            }).addTo(map);

            // Fit bounds
            map.fitBounds(bounds, { padding: [50, 50] });
        } else {
            // If only one waypoint, center on it
            if (bounds.length > 0) {
                map.setView(bounds[0], 15);
            } else {
                // Default view if no waypoints
                map.setView([-6.2088, 106.8456], 12);
            }
        }

        // Update driver location
        function updateDriverLocation() {
            if (navigator.geolocation) {
                // Opsi untuk geolocation dengan timeout dan akurasi tinggi
                var options = {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                };

                navigator.geolocation.getCurrentPosition(
                    // Success callback
                    function(position) {
                        var lat = position.coords.latitude;
                        var lng = position.coords.longitude;

                        // Update first waypoint (driver)
                        if (waypoints.length > 0 && waypoints[0].type === 'driver') {
                            waypoints[0].lat = lat;
                            waypoints[0].lng = lng;

                            // Update routing
                            if (routingWaypoints.length > 0) {
                                routingWaypoints[0] = L.latLng(lat, lng);
                                if (typeof control !== 'undefined' && control) {
                                    control.setWaypoints(routingWaypoints);
                                }
                            }
                        }
                    },
                    // Error callback
                    function(error) {
                        var errorMessage = '';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'Akses lokasi ditolak';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'Informasi lokasi tidak tersedia';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'Permintaan lokasi habis waktu';
                                break;
                            case error.UNKNOWN_ERROR:
                                errorMessage = 'Kesalahan tidak diketahui';
                                break;
                        }
                        console.error('Error getting location:', errorMessage);

                        // Tampilkan pesan jika menggunakan HTTP
                        if (window.location.protocol === 'http:' && window.location.hostname !== 'localhost') {
                            console.warn('Geolocation memerlukan HTTPS. Saat ini menggunakan HTTP.');
                        }
                    },
                    options
                );
            }
        }

        // Panggil sekali saat halaman dimuat
        updateDriverLocation();

        // Update driver location every 30 seconds
        var locationUpdateInterval = setInterval(updateDriverLocation, 30000);

        // Bersihkan interval saat halaman ditutup
        window.addEventListener('beforeunload', function() {
            clearInterval(locationUpdateInterval);
        });
    </script>
</body>
</html>