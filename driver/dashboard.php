<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if driver is logged in
if (!isset($_SESSION['driver_id']) || $_SESSION['user_type'] !== 'driver') {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get driver information
$driver_id = $_SESSION['driver_id'];
$stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$driver = $stmt->fetch();

// Update driver status if form submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $newStatus = $_POST['status'];

    // Validate status
    if (in_array($newStatus, ['available', 'busy', 'offline'])) {
        $stmt = $conn->prepare("UPDATE drivers SET status = :status WHERE driver_id = :driver_id");
        $stmt->bindParam(':status', $newStatus);
        $stmt->bindParam(':driver_id', $driver_id);

        if ($stmt->execute()) {
            // Refresh driver data
            $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();
            $driver = $stmt->fetch();

            $success = 'Status berhasil diperbarui!';
        } else {
            $error = 'Gagal memperbarui status';
        }
    } else {
        $error = 'Status tidak valid';
    }
}

// Update location if form submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_location'])) {
    $latitude = $_POST['latitude'];
    $longitude = $_POST['longitude'];

    // Validate coordinates
    if (is_numeric($latitude) && is_numeric($longitude)) {
        $stmt = $conn->prepare("UPDATE drivers SET current_latitude = :latitude, current_longitude = :longitude WHERE driver_id = :driver_id");
        $stmt->bindParam(':latitude', $latitude);
        $stmt->bindParam(':longitude', $longitude);
        $stmt->bindParam(':driver_id', $driver_id);

        if ($stmt->execute()) {
            // Refresh driver data
            $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();
            $driver = $stmt->fetch();

            $success = 'Lokasi berhasil diperbarui!';
        } else {
            $error = 'Gagal memperbarui lokasi';
        }
    } else {
        $error = 'Koordinat tidak valid';
    }
}

// Get active orders assigned to this driver
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
           r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
           u.name as customer_name, u.phone as customer_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.driver_id = :driver_id
    AND o.order_status IN ('confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way')
    ORDER BY o.created_at DESC
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$activeOrders = $stmt->fetchAll();

// Get recent completed orders
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, u.name as customer_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    WHERE o.driver_id = :driver_id
    AND o.order_status = 'delivered'
    ORDER BY o.updated_at DESC
    LIMIT 5
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$recentCompletedOrders = $stmt->fetchAll();

// Get today's earnings
$today = date('Y-m-d');
$stmt = $conn->prepare("
    SELECT SUM(delivery_fee) as total
    FROM orders
    WHERE driver_id = :driver_id
    AND order_status = 'delivered'
    AND DATE(updated_at) = :today
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->bindParam(':today', $today);
$stmt->execute();
$todayEarnings = $stmt->fetch()['total'] ?? 0;

// Get total earnings
$stmt = $conn->prepare("
    SELECT SUM(delivery_fee) as total
    FROM orders
    WHERE driver_id = :driver_id
    AND order_status = 'delivered'
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$totalEarnings = $stmt->fetch()['total'] ?? 0;

// Get total completed orders
$stmt = $conn->prepare("
    SELECT COUNT(*) as total
    FROM orders
    WHERE driver_id = :driver_id
    AND order_status = 'delivered'
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$totalCompletedOrders = $stmt->fetch()['total'] ?? 0;

// Get available orders count (ready for pickup and not assigned to any driver)
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM orders
    WHERE order_status = 'ready_for_pickup'
    AND driver_id IS NULL
");
$stmt->execute();
$availableOrdersCount = $stmt->fetch()['count'] ?? 0;

// Debug: Print available orders
echo "<!-- Debug: Available Orders Count: $availableOrdersCount -->";

// Get unread notifications count
$unreadNotificationsCount = getUnreadNotificationsCount($driver_id);

// Get unread chat messages count
$stmt = $conn->prepare("
    SELECT COUNT(*) as unread_count
    FROM chat_messages cm
    JOIN chat_participants cp ON cm.room_id = cp.room_id
    WHERE cp.user_type = 'driver'
    AND cp.user_id = :driver_id
    AND cm.sender_type != 'driver'
    AND cm.sender_id != :driver_id
    AND cm.is_read = 0
");
$stmt->bindParam(':driver_id', $driver_id);
$stmt->execute();
$unreadChatCount = $stmt->fetchColumn() ?? 0;

// Log untuk debugging
error_log("Driver ID: $driver_id, Unread Chat Count: $unreadChatCount");

// Jika tidak ada pesan yang belum dibaca, periksa apakah ada ruang chat untuk pesanan aktif
if ($unreadChatCount == 0) {
    // Dapatkan pesanan aktif yang memiliki status 'on_the_way'
    $stmt = $conn->prepare("
        SELECT order_id FROM orders
        WHERE driver_id = :driver_id
        AND order_status = 'on_the_way'
    ");
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();
    $activeOrders = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Log untuk debugging
    error_log("Active Orders for Driver $driver_id: " . json_encode($activeOrders));

    // Periksa apakah ada ruang chat untuk pesanan aktif
    if (!empty($activeOrders)) {
        $orderIds = implode(',', $activeOrders);
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM chat_rooms cr
            JOIN chat_participants cp ON cr.room_id = cp.room_id
            WHERE cr.order_id IN ($orderIds)
            AND cp.user_type = 'driver'
            AND cp.user_id = :driver_id
        ");
        $stmt->bindParam(':driver_id', $driver_id);
        $stmt->execute();
        $chatRoomsCount = $stmt->fetchColumn();

        // Log untuk debugging
        error_log("Chat Rooms Count for Active Orders: $chatRoomsCount");

        // Jika tidak ada ruang chat untuk pesanan aktif, buat notifikasi
        if ($chatRoomsCount == 0) {
            $unreadChatCount = 1; // Set notifikasi untuk mengingatkan driver untuk membuat chat
        }
    }
}

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : ($success ?? '');
$error = isset($_GET['error']) ? $_GET['error'] : ($error ?? '');
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dasbor Pengemudi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dasbor Pengemudi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifikasi
                            <?php if ($unreadNotificationsCount > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= $unreadNotificationsCount ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="available_orders.php">Pesanan Tersedia</a></li>
                            <li><a class="dropdown-item" href="location_settings.php">Lokasi</a></li>
                            <li><a class="dropdown-item" href="route_optimization.php">Optimasi Rute</a></li>
                            <li><a class="dropdown-item" href="earnings.php">Penghasilan</a></li>
                            <li>
                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="chat.php">
                                    Chat
                                    <?php if ($unreadChatCount > 0): ?>
                                        <span class="badge bg-danger rounded-pill"><?= $unreadChatCount ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Dasbor Pengemudi</h1>
                <p class="text-muted">Selamat datang, <?= $driver['name'] ?></p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group">
                    <a href="orders.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>Semua Pesanan
                    </a>
                    <a href="available_orders.php" class="btn btn-outline-warning position-relative">
                        <i class="fas fa-shopping-bag me-2"></i>Pesanan Tersedia
                        <?php if ($availableOrdersCount > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= $availableOrdersCount ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="notifications.php" class="btn btn-outline-info position-relative">
                        <i class="fas fa-bell me-2"></i>Notifikasi
                        <?php if ($unreadNotificationsCount > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= $unreadNotificationsCount ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="route_optimization.php" class="btn btn-outline-success">
                        <i class="fas fa-route me-2"></i>Optimasi Rute
                    </a>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php
        // Tampilkan notifikasi untuk driver jika ada pesanan aktif tanpa chat
        if ($unreadChatCount > 0 && empty($activeOrders)) {
            // Periksa apakah ada pesanan dengan status 'on_the_way'
            $stmt = $conn->prepare("
                SELECT COUNT(*) FROM orders
                WHERE driver_id = :driver_id
                AND order_status = 'on_the_way'
            ");
            $stmt->bindParam(':driver_id', $driver_id);
            $stmt->execute();
            $onTheWayOrdersCount = $stmt->fetchColumn();

            if ($onTheWayOrdersCount > 0) {
                echo '<div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Informasi:</strong> Anda memiliki pesanan dalam perjalanan. Jangan lupa untuk berkomunikasi dengan pelanggan melalui fitur chat.
                    <a href="chat.php" class="alert-link">Buka Chat</a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>';
            }
        }
        ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Status Pengemudi</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <?php
                            $statusClass = '';
                            $statusText = '';
                            switch ($driver['status']) {
                                case 'available':
                                    $statusClass = 'bg-success';
                                    $statusText = 'Tersedia';
                                    break;
                                case 'busy':
                                    $statusClass = 'bg-warning text-dark';
                                    $statusText = 'Sibuk';
                                    break;
                                case 'offline':
                                    $statusClass = 'bg-secondary';
                                    $statusText = 'Offline';
                                    break;
                                default:
                                    $statusClass = 'bg-secondary';
                                    $statusText = 'Tidak Diketahui';
                            }
                            ?>
                            <span class="badge <?= $statusClass ?> status-badge"><?= $statusText ?></span>
                        </div>

                        <form action="dashboard.php" method="post">
                            <input type="hidden" name="update_status" value="1">
                            <div class="mb-3">
                                <label for="status" class="form-label">Ubah Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="available" <?= $driver['status'] === 'available' ? 'selected' : '' ?>>Tersedia</option>
                                    <option value="busy" <?= $driver['status'] === 'busy' ? 'selected' : '' ?>>Sibuk</option>
                                    <option value="offline" <?= $driver['status'] === 'offline' ? 'selected' : '' ?>>Offline</option>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Perbarui Status</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Lokasi Saat Ini</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                        <form action="dashboard.php" method="post">
                            <input type="hidden" name="update_location" value="1">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="text" class="form-control" id="latitude" name="latitude" value="<?= $driver['current_latitude'] ?? '' ?>" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="text" class="form-control" id="longitude" name="longitude" value="<?= $driver['current_longitude'] ?? '' ?>" readonly>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" id="get-location" class="btn btn-outline-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i>Gunakan Lokasi Saat Ini
                                </button>
                                <button type="submit" class="btn btn-primary">Perbarui Lokasi</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-primary text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Penghasilan Hari Ini</h6>
                                <h2 class="mb-0">Rp<?= number_format($todayEarnings * 15000, 0, ',', '.') ?></h2>
                            </div>
                            <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="earnings.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card bg-success text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Penghasilan</h6>
                                <h2 class="mb-0">Rp<?= number_format($totalEarnings * 15000, 0, ',', '.') ?></h2>
                            </div>
                            <i class="fas fa-wallet fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="earnings.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card bg-info text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pesanan Selesai</h6>
                                <h2 class="mb-0"><?= $totalCompletedOrders ?></h2>
                            </div>
                            <i class="fas fa-check-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a href="orders.php" class="text-white text-decoration-none small">Lihat Detail</a>
                        <i class="fas fa-angle-right text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Aktif</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activeOrders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-motorcycle fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Pesanan Aktif</h4>
                                <p class="text-muted">Anda tidak memiliki pesanan aktif saat ini.</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($activeOrders as $order): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card order-card h-100">
                                            <div class="card-header">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Pesanan #<?= $order['order_id'] ?></h6>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    switch ($order['order_status']) {
                                                        case 'confirmed':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'Dikonfirmasi';
                                                            break;
                                                        case 'preparing':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Sedang Disiapkan';
                                                            break;
                                                        case 'ready_for_pickup':
                                                            $statusClass = 'bg-warning text-dark';
                                                            $statusText = 'Siap Diambil';
                                                            break;
                                                        case 'picked_up':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'Sudah Diambil';
                                                            break;
                                                        case 'on_the_way':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Dalam Perjalanan';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'Tidak Diketahui';
                                                    }
                                                    ?>
                                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-store me-2"></i>Restoran:</h6>
                                                    <p class="mb-1"><?= $order['restaurant_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['restaurant_address'] ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-user me-2"></i>Pelanggan:</h6>
                                                    <p class="mb-1"><?= $order['customer_name'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['customer_phone'] ?></p>
                                                    <p class="small text-muted mb-0"><?= $order['delivery_address'] ?></p>
                                                </div>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill me-2"></i>Total:</h6>
                                                        <p class="mb-0">Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6><i class="fas fa-money-bill-wave me-2"></i>Biaya Antar:</h6>
                                                        <p class="mb-0">Rp<?= number_format($order['delivery_fee'] * 15000, 0, ',', '.') ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-primary w-100">Lihat Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Terbaru Selesai</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentCompletedOrders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                <h4>Belum Ada Pesanan Selesai</h4>
                                <p class="text-muted">Anda belum memiliki pesanan yang telah selesai.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Restoran</th>
                                            <th>Pelanggan</th>
                                            <th>Total</th>
                                            <th>Biaya Antar</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentCompletedOrders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td><?= $order['customer_name'] ?></td>
                                                <td>Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>Rp<?= number_format($order['delivery_fee'] * 15000, 0, ',', '.') ?></td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['updated_at'])) ?></td>
                                                <td>
                                                    <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="orders.php" class="btn btn-outline-primary">Lihat Semua Pesanan</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        var initialLat = <?= $driver['current_latitude'] ?: -6.2088 ?>;
        var initialLng = <?= $driver['current_longitude'] ?: 106.8456 ?>;

        var map = L.map('map').setView([initialLat, initialLng], 15);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        var marker = L.marker([initialLat, initialLng]).addTo(map);

        // Handle map click
        map.on('click', function(e) {
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker(e.latlng).addTo(map);
            document.getElementById('latitude').value = e.latlng.lat.toFixed(6);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(6);
        });

        // Get current location
        document.getElementById('get-location').addEventListener('click', function() {
            if (navigator.geolocation) {
                // Tampilkan pesan loading
                var locationBtn = document.getElementById('get-location');
                var originalText = locationBtn.innerHTML;
                locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mendapatkan Lokasi...';
                locationBtn.disabled = true;

                // Opsi untuk geolocation dengan timeout dan akurasi tinggi
                var options = {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                };

                navigator.geolocation.getCurrentPosition(
                    // Success callback
                    function(position) {
                        var lat = position.coords.latitude;
                        var lng = position.coords.longitude;

                        if (marker) {
                            map.removeLayer(marker);
                        }

                        marker = L.marker([lat, lng]).addTo(map);
                        map.setView([lat, lng], 15);

                        document.getElementById('latitude').value = lat.toFixed(6);
                        document.getElementById('longitude').value = lng.toFixed(6);

                        // Kembalikan tombol ke keadaan semula
                        locationBtn.innerHTML = originalText;
                        locationBtn.disabled = false;
                    },
                    // Error callback
                    function(error) {
                        // Kembalikan tombol ke keadaan semula
                        locationBtn.innerHTML = originalText;
                        locationBtn.disabled = false;

                        var errorMessage = '';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'Anda menolak permintaan akses lokasi. Silakan izinkan akses lokasi di pengaturan browser Anda.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'Informasi lokasi tidak tersedia. Pastikan GPS atau layanan lokasi Anda aktif.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'Permintaan lokasi habis waktu. Silakan coba lagi.';
                                break;
                            case error.UNKNOWN_ERROR:
                                errorMessage = 'Terjadi kesalahan yang tidak diketahui saat mendapatkan lokasi.';
                                break;
                        }

                        // Tampilkan pesan error yang lebih informatif
                        alert('Tidak dapat mengakses lokasi Anda: ' + errorMessage);

                        // Tambahkan petunjuk untuk HTTPS
                        if (window.location.protocol === 'http:' && window.location.hostname !== 'localhost') {
                            alert('Catatan: Fitur geolocation memerlukan koneksi HTTPS yang aman. Saat ini Anda menggunakan HTTP. Silakan hubungi administrator untuk mengaktifkan HTTPS.');
                        }
                    },
                    options
                );
            } else {
                alert('Geolocation tidak didukung oleh browser Anda. Silakan gunakan browser yang lebih baru.');
            }
        });
    </script>
</body>
</html>
