<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$userName = $_SESSION['user_name'];

// Connect to database
$conn = connectDB();

// Get user preferences
$stmt = $conn->prepare("
    SELECT p.preference_id, p.name 
    FROM user_preferences up
    JOIN preferences p ON up.preference_id = p.preference_id
    WHERE up.user_id = :user_id
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$preferences = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user's order history
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, COUNT(o.order_id) as order_count
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.user_id = :user_id
    GROUP BY r.restaurant_id
    ORDER BY order_count DESC
    LIMIT 5
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$orderHistory = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get personalized recommendations based on order history and preferences
$recommendationQuery = "
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address, 
           AVG(rv.rating) as avg_rating, COUNT(rv.review_id) as review_count,
           (
               CASE 
                   WHEN EXISTS (
                       SELECT 1 FROM orders o 
                       WHERE o.user_id = :user_id AND o.restaurant_id = r.restaurant_id
                   ) THEN 3
                   WHEN r.cuisine_type IN (
                       SELECT cuisine_type FROM restaurants 
                       WHERE restaurant_id IN (
                           SELECT restaurant_id FROM orders 
                           WHERE user_id = :user_id
                       )
                   ) THEN 2
                   ELSE 1
               END
           ) as relevance_score
    FROM restaurants r
    LEFT JOIN reviews rv ON r.restaurant_id = rv.restaurant_id
    WHERE r.is_active = 1
    GROUP BY r.restaurant_id
    ORDER BY relevance_score DESC, avg_rating DESC
    LIMIT 10
";

$stmt = $conn->prepare($recommendationQuery);
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$recommendations = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get trending restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address, 
           AVG(rv.rating) as avg_rating, COUNT(rv.review_id) as review_count,
           COUNT(o.order_id) as order_count
    FROM restaurants r
    LEFT JOIN reviews rv ON r.restaurant_id = rv.restaurant_id
    LEFT JOIN orders o ON r.restaurant_id = o.restaurant_id AND o.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
    WHERE r.is_active = 1
    GROUP BY r.restaurant_id
    ORDER BY order_count DESC, avg_rating DESC
    LIMIT 10
");
$stmt->execute();
$trendingRestaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get new restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address, 
           AVG(rv.rating) as avg_rating, COUNT(rv.review_id) as review_count
    FROM restaurants r
    LEFT JOIN reviews rv ON r.restaurant_id = rv.restaurant_id
    WHERE r.is_active = 1 AND r.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY r.restaurant_id
    ORDER BY r.created_at DESC
    LIMIT 10
");
$stmt->execute();
$newRestaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get favorite restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address
    FROM favorites f
    JOIN restaurants r ON f.restaurant_id = r.restaurant_id
    WHERE f.user_id = :user_id
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$favoriteRestaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rekomendasi untuk Anda - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/personalization.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                            <span class="badge bg-danger rounded-pill">
                                <?php
                                    $cartCount = 0;
                                    if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                                        foreach ($_SESSION['cart'] as $items) {
                                            $cartCount += count($items);
                                        }
                                    }
                                    echo $cartCount;
                                ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $userName ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="user/preferences.php">Preferensi</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Rekomendasi untuk Anda</h1>
                <p class="text-muted">Temukan restoran yang mungkin Anda sukai berdasarkan preferensi dan riwayat pesanan Anda.</p>
            </div>
        </div>

        <!-- Personalized Recommendations -->
        <section class="mb-5">
            <h2 class="mb-4">Rekomendasi Khusus untuk Anda</h2>
            
            <?php if (empty($recommendations)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Kami belum memiliki cukup data untuk memberikan rekomendasi yang dipersonalisasi. Cobalah memesan dari beberapa restoran untuk mendapatkan rekomendasi yang lebih baik.
                </div>
            <?php else: ?>
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    <?php foreach ($recommendations as $restaurant): ?>
                        <div class="col">
                            <div class="card restaurant-card h-100">
                                <?php if (in_array($restaurant['restaurant_id'], array_column($orderHistory, 'restaurant_id'))): ?>
                                    <div class="recommendation-badge">
                                        <i class="fas fa-history me-1"></i> Anda pernah memesan
                                    </div>
                                <?php endif; ?>
                                
                                <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" 
                                     class="card-img-top" alt="<?= $restaurant['name'] ?>">
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                    <p class="card-text">
                                        <i class="fas fa-utensils me-2"></i><?= $restaurant['cuisine_type'] ?>
                                    </p>
                                    <p class="card-text">
                                        <i class="fas fa-map-marker-alt me-2"></i><?= $restaurant['address'] ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <?php if (isset($restaurant['avg_rating'])): ?>
                                                <span class="text-warning">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <?php if ($i <= round($restaurant['avg_rating'])): ?>
                                                            <i class="fas fa-star"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </span>
                                                <small class="text-muted">(<?= $restaurant['review_count'] ?>)</small>
                                            <?php else: ?>
                                                <span class="text-muted">Belum ada ulasan</span>
                                            <?php endif; ?>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary favorite-toggle" 
                                                data-restaurant-id="<?= $restaurant['restaurant_id'] ?>"
                                                title="<?= in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id')) ? 'Hapus dari Favorit' : 'Tambahkan ke Favorit' ?>">
                                            <?php if (in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id'))): ?>
                                                <i class="fas fa-heart"></i>
                                            <?php else: ?>
                                                <i class="far fa-heart"></i>
                                            <?php endif; ?>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-footer bg-white border-top-0">
                                    <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-primary w-100">
                                        <i class="fas fa-utensils me-2"></i>Lihat Menu
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </section>

        <!-- Trending Restaurants -->
        <section class="mb-5">
            <h2 class="mb-4">Sedang Populer</h2>
            
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php foreach ($trendingRestaurants as $restaurant): ?>
                    <div class="col">
                        <div class="card restaurant-card h-100">
                            <div class="recommendation-badge bg-danger">
                                <i class="fas fa-fire me-1"></i> Populer
                            </div>
                            
                            <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" 
                                 class="card-img-top" alt="<?= $restaurant['name'] ?>">
                            
                            <div class="card-body">
                                <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                <p class="card-text">
                                    <i class="fas fa-utensils me-2"></i><?= $restaurant['cuisine_type'] ?>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <?php if (isset($restaurant['avg_rating'])): ?>
                                            <span class="text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= round($restaurant['avg_rating'])): ?>
                                                        <i class="fas fa-star"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </span>
                                            <small class="text-muted">(<?= $restaurant['review_count'] ?>)</small>
                                        <?php else: ?>
                                            <span class="text-muted">Belum ada ulasan</span>
                                        <?php endif; ?>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary favorite-toggle" 
                                            data-restaurant-id="<?= $restaurant['restaurant_id'] ?>"
                                            title="<?= in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id')) ? 'Hapus dari Favorit' : 'Tambahkan ke Favorit' ?>">
                                        <?php if (in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id'))): ?>
                                            <i class="fas fa-heart"></i>
                                        <?php else: ?>
                                            <i class="far fa-heart"></i>
                                        <?php endif; ?>
                                    </button>
                                </div>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-utensils me-2"></i>Lihat Menu
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- New Restaurants -->
        <section class="mb-5">
            <h2 class="mb-4">Restoran Baru</h2>
            
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php foreach ($newRestaurants as $restaurant): ?>
                    <div class="col">
                        <div class="card restaurant-card h-100">
                            <div class="recommendation-badge bg-success">
                                <i class="fas fa-certificate me-1"></i> Baru
                            </div>
                            
                            <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" 
                                 class="card-img-top" alt="<?= $restaurant['name'] ?>">
                            
                            <div class="card-body">
                                <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                <p class="card-text">
                                    <i class="fas fa-utensils me-2"></i><?= $restaurant['cuisine_type'] ?>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <?php if (isset($restaurant['avg_rating'])): ?>
                                            <span class="text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= round($restaurant['avg_rating'])): ?>
                                                        <i class="fas fa-star"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </span>
                                            <small class="text-muted">(<?= $restaurant['review_count'] ?>)</small>
                                        <?php else: ?>
                                            <span class="text-muted">Belum ada ulasan</span>
                                        <?php endif; ?>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary favorite-toggle" 
                                            data-restaurant-id="<?= $restaurant['restaurant_id'] ?>"
                                            title="<?= in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id')) ? 'Hapus dari Favorit' : 'Tambahkan ke Favorit' ?>">
                                        <?php if (in_array($restaurant['restaurant_id'], array_column($favoriteRestaurants, 'restaurant_id'))): ?>
                                            <i class="fas fa-heart"></i>
                                        <?php else: ?>
                                            <i class="far fa-heart"></i>
                                        <?php endif; ?>
                                    </button>
                                </div>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-utensils me-2"></i>Lihat Menu
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="spinner-overlay">
        <div class="spinner"></div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/user-experience.js"></script>
    <script src="js/personalization.js"></script>
</body>
</html>
