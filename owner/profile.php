<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = $_POST['name'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';
        
        // Validate inputs
        if (empty($name) || empty($phone)) {
            $error = 'Silakan isi semua bidang yang diperlukan';
        } else {
            try {
                // Update owner profile
                $stmt = $conn->prepare("
                    UPDATE restaurant_owners SET 
                        name = :name, 
                        phone = :phone, 
                        address = :address
                    WHERE owner_id = :owner_id
                ");
                
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':phone', $phone);
                $stmt->bindParam(':address', $address);
                $stmt->bindParam(':owner_id', $owner_id);
                
                if ($stmt->execute()) {
                    $success = 'Profil berhasil diperbarui!';
                    
                    // Refresh owner data
                    $stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
                    $stmt->bindParam(':owner_id', $owner_id);
                    $stmt->execute();
                    $owner = $stmt->fetch();
                    
                    // Update session name
                    $_SESSION['owner_name'] = $name;
                } else {
                    $error = 'Gagal memperbarui profil';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validate inputs
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'Silakan isi semua bidang kata sandi';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Kata sandi baru tidak cocok dengan konfirmasi';
        } elseif (strlen($new_password) < 8) {
            $error = 'Kata sandi baru harus minimal 8 karakter';
        } else {
            try {
                // Verify current password
                if (password_verify($current_password, $owner['password'])) {
                    // Hash new password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    
                    // Update password
                    $stmt = $conn->prepare("UPDATE restaurant_owners SET password = :password WHERE owner_id = :owner_id");
                    $stmt->bindParam(':password', $hashed_password);
                    $stmt->bindParam(':owner_id', $owner_id);
                    
                    if ($stmt->execute()) {
                        $success = 'Kata sandi berhasil diperbarui!';
                    } else {
                        $error = 'Gagal memperbarui kata sandi';
                    }
                } else {
                    $error = 'Kata sandi saat ini tidak valid';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get owner's restaurants
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE owner_id = :owner_id ORDER BY name");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Pemilik Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Profile Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Profil Pemilik Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Profil</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pemilik</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="avatar-circle mx-auto mb-3">
                                <span class="avatar-initials"><?= substr($owner['name'], 0, 1) ?></span>
                            </div>
                            <h4><?= $owner['name'] ?></h4>
                            <p class="text-muted mb-0">ID: <?= $owner['owner_id'] ?></p>
                        </div>
                        
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-envelope me-2"></i>Email</span>
                                <span class="text-muted"><?= $owner['email'] ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-phone me-2"></i>Telepon</span>
                                <span class="text-muted"><?= $owner['phone'] ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-map-marker-alt me-2"></i>Alamat</span>
                                <span class="text-muted"><?= $owner['address'] ?: '-' ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-calendar-alt me-2"></i>Bergabung</span>
                                <span class="text-muted"><?= date('d/m/Y', strtotime($owner['created_at'])) ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-store me-2"></i>Jumlah Restoran</span>
                                <span class="badge bg-primary rounded-pill"><?= count($restaurants) ?></span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Restoran Saya</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($restaurants)): ?>
                            <div class="text-center py-3">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <p>Anda belum memiliki restoran.</p>
                                <a href="add_restaurant.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Tambah Restoran
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($restaurants as $restaurant): ?>
                                    <a href="edit_restaurant.php?id=<?= $restaurant['restaurant_id'] ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?= $restaurant['name'] ?></h6>
                                            <small class="text-<?= $restaurant['is_open'] ? 'success' : 'danger' ?>">
                                                <?= $restaurant['is_open'] ? 'Buka' : 'Tutup' ?>
                                            </small>
                                        </div>
                                        <small class="text-muted"><?= $restaurant['address'] ?></small>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <div class="mt-3">
                                <a href="add_restaurant.php" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-plus me-2"></i>Tambah Restoran Baru
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Profil</h5>
                    </div>
                    <div class="card-body">
                        <form action="profile.php" method="post">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Nama Lengkap</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($owner['name']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Alamat Email</label>
                                <input type="email" class="form-control" id="email" value="<?= htmlspecialchars($owner['email']) ?>" readonly>
                                <small class="text-muted">Email tidak dapat diubah</small>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">Nomor Telepon</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($owner['phone']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Alamat</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($owner['address']) ?></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ubah Kata Sandi</h5>
                    </div>
                    <div class="card-body">
                        <form action="profile.php" method="post">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Kata Sandi Saat Ini</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <small class="text-muted">Minimal 8 karakter</small>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Konfirmasi Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Ubah Kata Sandi</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .avatar-circle {
            width: 100px;
            height: 100px;
            background-color: #007bff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .avatar-initials {
            color: white;
            font-size: 48px;
            font-weight: bold;
            text-transform: uppercase;
        }
    </style>
</body>
</html>
