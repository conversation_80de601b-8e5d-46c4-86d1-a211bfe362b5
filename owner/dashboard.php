<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Get owner's restaurants
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();

// Get pending orders for all owner's restaurants
$restaurantIds = array_column($restaurants, 'restaurant_id');
$pendingOrders = [];

// Get unread chat messages count
$stmt = $conn->prepare("
    SELECT COUNT(*) as unread_count
    FROM chat_messages cm
    JOIN chat_participants cp ON cm.room_id = cp.room_id
    WHERE cp.user_type = 'restaurant_owner'
    AND cp.user_id = :owner_id
    AND cm.sender_type != 'restaurant_owner'
    AND cm.sender_id != :owner_id
    AND cm.is_read = 0
");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$unreadChatCount = $stmt->fetch()['unread_count'] ?? 0;

if (!empty($restaurantIds)) {
    $placeholders = str_repeat('?,', count($restaurantIds) - 1) . '?';
    $stmt = $conn->prepare("
        SELECT o.*, r.name as restaurant_name, u.name as customer_name, u.phone as customer_phone
        FROM orders o
        JOIN restaurants r ON o.restaurant_id = r.restaurant_id
        JOIN users u ON o.user_id = u.user_id
        WHERE o.restaurant_id IN ($placeholders)
        AND o.order_status IN ('pending', 'confirmed', 'preparing')
        ORDER BY o.created_at DESC
    ");

    foreach ($restaurantIds as $index => $id) {
        $stmt->bindValue($index + 1, $id);
    }

    $stmt->execute();
    $pendingOrders = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dasbor Pemilik Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="analytics.php">Analitik</a></li>
                            <li><a class="dropdown-item" href="location_settings.php">Lokasi</a></li>
                            <li>
                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="chat.php">
                                    Chat
                                    <?php if ($unreadChatCount > 0): ?>
                                        <span class="badge bg-danger rounded-pill"><?= $unreadChatCount ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Dasbor Pemilik Restoran</h1>
                <p class="text-muted">Selamat datang, <?= $owner['name'] ?></p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex gap-2 justify-content-md-end">
                    <a href="analytics.php" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>Analitik
                    </a>
                    <a href="promotions.php" class="btn btn-outline-success">
                        <i class="fas fa-tag me-2"></i>Promosi
                    </a>
                    <a href="chat.php" class="btn btn-outline-secondary position-relative">
                        <i class="fas fa-comments me-2"></i>Chat
                        <?php if ($unreadChatCount > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= $unreadChatCount ?>
                                <span class="visually-hidden">pesan belum dibaca</span>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="add_restaurant.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Restoran
                    </a>
                </div>
            </div>
        </div>

        <!-- Restaurant Cards -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Restoran Saya</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($restaurants)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <h4>Anda belum memiliki restoran</h4>
                                <p class="text-muted">Klik tombol "Tambah Restoran Baru" untuk mulai menambahkan restoran Anda.</p>
                            </div>
                        <?php else: ?>
                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                                <?php foreach ($restaurants as $restaurant): ?>
                                    <div class="col">
                                        <div class="card h-100 restaurant-card">
                                            <img src="<?= $restaurant['banner_url'] ?? '../assets/restaurant-placeholder.png' ?>" class="card-img-top" alt="<?= $restaurant['name'] ?>" style="height: 120px; object-fit: cover;">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-2">
                                                    <div class="rounded-circle me-2 d-flex align-items-center justify-content-center bg-primary text-white" style="width: 40px; height: 40px; font-weight: bold;"><?= substr($restaurant['name'], 0, 1) ?></div>
                                                    <h5 class="card-title mb-0"><?= $restaurant['name'] ?></h5>
                                                </div>
                                                <p class="card-text small text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i> <?= $restaurant['address'] ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="badge <?= $restaurant['is_open'] ? 'bg-success' : 'bg-danger' ?>">
                                                        <?= $restaurant['is_open'] ? 'Buka' : 'Tutup' ?>
                                                    </span>
                                                    <span class="text-muted small">
                                                        <i class="fas fa-star me-1"></i> <?= number_format($restaurant['rating'], 1) ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-white border-top-0">
                                                <div class="d-flex gap-2">
                                                    <a href="edit_restaurant.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-sm btn-outline-primary flex-grow-1">
                                                        <i class="fas fa-edit me-1"></i> Edit
                                                    </a>
                                                    <a href="manage_menu.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-sm btn-outline-success flex-grow-1">
                                                        <i class="fas fa-utensils me-1"></i> Menu
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan yang Menunggu</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pendingOrders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                <h4>Tidak ada pesanan yang menunggu</h4>
                                <p class="text-muted">Semua pesanan telah diproses.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID Pesanan</th>
                                            <th>Restoran</th>
                                            <th>Pelanggan</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Waktu</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pendingOrders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td>
                                                    <?= $order['customer_name'] ?><br>
                                                    <small class="text-muted"><?= $order['customer_phone'] ?></small>
                                                </td>
                                                <td><?= formatCurrency($order['total_amount']) ?></td>
                                                <td>
                                                    <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?>">
                                                        <?= getOrderStatusText($order['order_status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="order_detail.php?id=<?= $order['order_id'] ?>&action=confirm" class="btn btn-outline-success">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
