<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Get owner's restaurants
$stmt = $conn->prepare("SELECT restaurant_id, name FROM restaurants WHERE owner_id = :owner_id ORDER BY name");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();

// Handle form submission for new promotion
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'create') {
        // Validate inputs
        $restaurant_id = $_POST['restaurant_id'] ?? '';
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $discount_type = $_POST['discount_type'] ?? '';
        $discount_value = $_POST['discount_value'] ?? '';
        $min_order_amount = $_POST['min_order_amount'] ?? 0;
        $start_date = $_POST['start_date'] ?? '';
        $end_date = $_POST['end_date'] ?? '';
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $usage_limit = !empty($_POST['usage_limit']) ? $_POST['usage_limit'] : null;
        $promo_code = $_POST['promo_code'] ?? '';
        $applies_to = $_POST['applies_to'] ?? 'all_items';

        // Validate restaurant ownership
        $stmt = $conn->prepare("SELECT COUNT(*) FROM restaurants WHERE restaurant_id = :restaurant_id AND owner_id = :owner_id");
        $stmt->bindParam(':restaurant_id', $restaurant_id);
        $stmt->bindParam(':owner_id', $owner_id);
        $stmt->execute();

        if ($stmt->fetchColumn() === 0) {
            $error = 'Anda tidak memiliki akses ke restoran ini';
        } elseif (empty($name) || empty($discount_type) || empty($discount_value) || empty($start_date) || empty($end_date)) {
            $error = 'Semua field yang wajib harus diisi';
        } else {
            try {
                $conn->beginTransaction();

                // Insert promotion
                $stmt = $conn->prepare("
                    INSERT INTO promotions (
                        restaurant_id, name, description, discount_type, discount_value,
                        min_order_amount, start_date, end_date, is_active, usage_limit,
                        promo_code, applies_to
                    ) VALUES (
                        :restaurant_id, :name, :description, :discount_type, :discount_value,
                        :min_order_amount, :start_date, :end_date, :is_active, :usage_limit,
                        :promo_code, :applies_to
                    )
                ");

                $stmt->bindParam(':restaurant_id', $restaurant_id);
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':description', $description);
                $stmt->bindParam(':discount_type', $discount_type);
                $stmt->bindParam(':discount_value', $discount_value);
                $stmt->bindParam(':min_order_amount', $min_order_amount);
                $stmt->bindParam(':start_date', $start_date);
                $stmt->bindParam(':end_date', $end_date);
                $stmt->bindParam(':is_active', $is_active);
                $stmt->bindParam(':usage_limit', $usage_limit);
                $stmt->bindParam(':promo_code', $promo_code);
                $stmt->bindParam(':applies_to', $applies_to);

                $stmt->execute();
                $promotion_id = $conn->lastInsertId();

                // Handle specific items if applies_to is 'specific_items'
                if ($applies_to === 'specific_items' && isset($_POST['items']) && is_array($_POST['items'])) {
                    $stmt = $conn->prepare("
                        INSERT INTO promotion_items (promotion_id, item_id)
                        VALUES (:promotion_id, :item_id)
                    ");

                    foreach ($_POST['items'] as $item_id) {
                        $stmt->bindParam(':promotion_id', $promotion_id);
                        $stmt->bindParam(':item_id', $item_id);
                        $stmt->execute();
                    }
                }

                // Handle specific categories if applies_to is 'specific_categories'
                if ($applies_to === 'specific_categories' && isset($_POST['categories']) && is_array($_POST['categories'])) {
                    $stmt = $conn->prepare("
                        INSERT INTO promotion_categories (promotion_id, category_id)
                        VALUES (:promotion_id, :category_id)
                    ");

                    foreach ($_POST['categories'] as $category_id) {
                        $stmt->bindParam(':promotion_id', $promotion_id);
                        $stmt->bindParam(':category_id', $category_id);
                        $stmt->execute();
                    }
                }

                $conn->commit();
                $success = 'Promosi berhasil dibuat!';
            } catch (PDOException $e) {
                $conn->rollBack();
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['promotion_id'])) {
        $promotion_id = $_POST['promotion_id'];

        // Verify ownership
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM promotions p
            JOIN restaurants r ON p.restaurant_id = r.restaurant_id
            WHERE p.promotion_id = :promotion_id AND r.owner_id = :owner_id
        ");
        $stmt->bindParam(':promotion_id', $promotion_id);
        $stmt->bindParam(':owner_id', $owner_id);
        $stmt->execute();

        if ($stmt->fetchColumn() === 0) {
            $error = 'Anda tidak memiliki akses untuk menghapus promosi ini';
        } else {
            try {
                $stmt = $conn->prepare("DELETE FROM promotions WHERE promotion_id = :promotion_id");
                $stmt->bindParam(':promotion_id', $promotion_id);
                $stmt->execute();

                $success = 'Promosi berhasil dihapus!';
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'toggle' && isset($_POST['promotion_id'])) {
        $promotion_id = $_POST['promotion_id'];
        $is_active = $_POST['is_active'] === '1' ? 0 : 1;

        // Verify ownership
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM promotions p
            JOIN restaurants r ON p.restaurant_id = r.restaurant_id
            WHERE p.promotion_id = :promotion_id AND r.owner_id = :owner_id
        ");
        $stmt->bindParam(':promotion_id', $promotion_id);
        $stmt->bindParam(':owner_id', $owner_id);
        $stmt->execute();

        if ($stmt->fetchColumn() === 0) {
            $error = 'Anda tidak memiliki akses untuk mengubah promosi ini';
        } else {
            try {
                $stmt = $conn->prepare("UPDATE promotions SET is_active = :is_active WHERE promotion_id = :promotion_id");
                $stmt->bindParam(':is_active', $is_active);
                $stmt->bindParam(':promotion_id', $promotion_id);
                $stmt->execute();

                $success = 'Status promosi berhasil diperbarui!';
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get restaurant filter
$restaurant_id = isset($_GET['restaurant_id']) ? $_GET['restaurant_id'] : 'all';

// Get promotions
$where_clause = "WHERE r.owner_id = :owner_id";
$params = [':owner_id' => $owner_id];

if ($restaurant_id !== 'all') {
    $where_clause .= " AND p.restaurant_id = :restaurant_id";
    $params[':restaurant_id'] = $restaurant_id;
}

$stmt = $conn->prepare("
    SELECT p.*, r.name as restaurant_name
    FROM promotions p
    JOIN restaurants r ON p.restaurant_id = r.restaurant_id
    $where_clause
    ORDER BY p.created_at DESC
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$promotions = $stmt->fetchAll();

// Get categories for filter
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Promosi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .promotion-card {
            transition: all 0.3s ease;
        }
        .promotion-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="analytics.php">Analitik</a></li>
                            <li><a class="dropdown-item active" href="promotions.php">Promosi</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Promotions Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Kelola Promosi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Promosi</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPromotionModal">
                    <i class="fas fa-plus me-2"></i>Tambah Promosi Baru
                </button>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Filter Promosi</h5>
                    </div>
                    <div class="card-body">
                        <form action="promotions.php" method="get" class="row g-3">
                            <div class="col-md-6">
                                <label for="restaurant_id" class="form-label">Restoran</label>
                                <select class="form-select" id="restaurant_id" name="restaurant_id">
                                    <option value="all" <?= $restaurant_id === 'all' ? 'selected' : '' ?>>Semua Restoran</option>
                                    <?php foreach ($restaurants as $rest): ?>
                                        <option value="<?= $rest['restaurant_id'] ?>" <?= $restaurant_id == $rest['restaurant_id'] ? 'selected' : '' ?>>
                                            <?= $rest['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <?php if (empty($promotions)): ?>
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-tag fa-3x text-muted mb-3"></i>
                            <h4>Belum Ada Promosi</h4>
                            <p class="text-muted">Anda belum membuat promosi apa pun. Klik tombol "Tambah Promosi Baru" untuk mulai membuat promosi.</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($promotions as $promotion): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 shadow-sm promotion-card">
                            <div class="card-header bg-<?= $promotion['is_active'] ? 'success' : 'secondary' ?> text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><?= $promotion['name'] ?></h5>
                                    <span class="badge bg-<?= $promotion['is_active'] ? 'light text-success' : 'light text-secondary' ?>">
                                        <?= $promotion['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small mb-2">Restoran: <?= $promotion['restaurant_name'] ?></p>

                                <div class="mb-3">
                                    <h6 class="mb-1">Diskon:</h6>
                                    <p class="mb-0 fw-bold">
                                        <?php if ($promotion['discount_type'] === 'percentage'): ?>
                                            <?= number_format($promotion['discount_value'], 0) ?>%
                                        <?php else: ?>
                                            Rp<?= number_format($promotion['discount_value'] * 15000, 0, ',', '.') ?>
                                        <?php endif; ?>
                                    </p>
                                </div>

                                <?php if (!empty($promotion['description'])): ?>
                                    <div class="mb-3">
                                        <h6 class="mb-1">Deskripsi:</h6>
                                        <p class="mb-0"><?= $promotion['description'] ?></p>
                                    </div>
                                <?php endif; ?>

                                <div class="mb-3">
                                    <h6 class="mb-1">Periode:</h6>
                                    <p class="mb-0">
                                        <?= date('d/m/Y', strtotime($promotion['start_date'])) ?> -
                                        <?= date('d/m/Y', strtotime($promotion['end_date'])) ?>
                                    </p>
                                </div>

                                <?php if ($promotion['min_order_amount'] > 0): ?>
                                    <div class="mb-3">
                                        <h6 class="mb-1">Min. Pembelian:</h6>
                                        <p class="mb-0">Rp<?= number_format($promotion['min_order_amount'] * 15000, 0, ',', '.') ?></p>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($promotion['promo_code'])): ?>
                                    <div class="mb-3">
                                        <h6 class="mb-1">Kode Promo:</h6>
                                        <p class="mb-0 fw-bold"><?= $promotion['promo_code'] ?></p>
                                    </div>
                                <?php endif; ?>

                                <div class="mb-3">
                                    <h6 class="mb-1">Berlaku untuk:</h6>
                                    <p class="mb-0">
                                        <?php
                                        switch ($promotion['applies_to']) {
                                            case 'all_items':
                                                echo 'Semua Menu';
                                                break;
                                            case 'specific_items':
                                                echo 'Menu Tertentu';
                                                break;
                                            case 'specific_categories':
                                                echo 'Kategori Tertentu';
                                                break;
                                        }
                                        ?>
                                    </p>
                                </div>

                                <?php if ($promotion['usage_limit']): ?>
                                    <div class="mb-3">
                                        <h6 class="mb-1">Batas Penggunaan:</h6>
                                        <p class="mb-0"><?= $promotion['usage_count'] ?> / <?= $promotion['usage_limit'] ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <div class="d-flex justify-content-between">
                                    <form action="promotions.php" method="post">
                                        <input type="hidden" name="action" value="toggle">
                                        <input type="hidden" name="promotion_id" value="<?= $promotion['promotion_id'] ?>">
                                        <input type="hidden" name="is_active" value="<?= $promotion['is_active'] ?>">
                                        <button type="submit" class="btn btn-sm btn-<?= $promotion['is_active'] ? 'warning' : 'success' ?>">
                                            <i class="fas fa-<?= $promotion['is_active'] ? 'pause' : 'play' ?> me-1"></i>
                                            <?= $promotion['is_active'] ? 'Nonaktifkan' : 'Aktifkan' ?>
                                        </button>
                                    </form>
                                    <form action="promotions.php" method="post" onsubmit="return confirm('Apakah Anda yakin ingin menghapus promosi ini?');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="promotion_id" value="<?= $promotion['promotion_id'] ?>">
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash me-1"></i> Hapus
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Promotion Modal -->
    <div class="modal fade" id="addPromotionModal" tabindex="-1" aria-labelledby="addPromotionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addPromotionModalLabel">Tambah Promosi Baru</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="promotions.php" method="post" id="addPromotionForm">
                        <input type="hidden" name="action" value="create">

                        <div class="mb-3">
                            <label for="restaurant_id_modal" class="form-label">Restoran <span class="text-danger">*</span></label>
                            <select class="form-select" id="restaurant_id_modal" name="restaurant_id" required>
                                <option value="">Pilih Restoran</option>
                                <?php foreach ($restaurants as $rest): ?>
                                    <option value="<?= $rest['restaurant_id'] ?>"><?= $rest['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nama Promosi <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="discount_type" class="form-label">Jenis Diskon <span class="text-danger">*</span></label>
                                <select class="form-select" id="discount_type" name="discount_type" required>
                                    <option value="percentage">Persentase (%)</option>
                                    <option value="fixed_amount">Jumlah Tetap (Rp)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="discount_value" class="form-label">Nilai Diskon <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="discount_value" name="discount_value" min="0" step="0.01" required>
                                    <span class="input-group-text" id="discount_symbol">%</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="min_order_amount" class="form-label">Minimal Pembelian (Rp)</label>
                            <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" min="0" step="0.01" value="0">
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="start_date" name="start_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label">Tanggal Berakhir <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="promo_code" class="form-label">Kode Promo</label>
                            <input type="text" class="form-control" id="promo_code" name="promo_code">
                            <div class="form-text">Biarkan kosong jika diskon otomatis diterapkan tanpa kode promo.</div>
                        </div>

                        <div class="mb-3">
                            <label for="usage_limit" class="form-label">Batas Penggunaan</label>
                            <input type="number" class="form-control" id="usage_limit" name="usage_limit" min="1">
                            <div class="form-text">Biarkan kosong jika tidak ada batas penggunaan.</div>
                        </div>

                        <div class="mb-3">
                            <label for="applies_to" class="form-label">Berlaku Untuk <span class="text-danger">*</span></label>
                            <select class="form-select" id="applies_to" name="applies_to" required>
                                <option value="all_items">Semua Menu</option>
                                <option value="specific_items">Menu Tertentu</option>
                                <option value="specific_categories">Kategori Tertentu</option>
                            </select>
                        </div>

                        <div id="specific_items_container" class="mb-3 d-none">
                            <label for="items" class="form-label">Pilih Menu</label>
                            <select class="form-select" id="items" name="items[]" multiple>
                                <!-- Items will be loaded dynamically based on selected restaurant -->
                            </select>
                        </div>

                        <div id="specific_categories_container" class="mb-3 d-none">
                            <label for="categories" class="form-label">Pilih Kategori</label>
                            <select class="form-select" id="categories" name="categories[]" multiple>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['category_id'] ?>"><?= $category['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">Aktifkan Promosi</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" form="addPromotionForm" class="btn btn-primary">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('#items, #categories').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });

            // Toggle discount symbol based on discount type
            $('#discount_type').change(function() {
                if ($(this).val() === 'percentage') {
                    $('#discount_symbol').text('%');
                } else {
                    $('#discount_symbol').text('Rp');
                }
            });

            // Toggle specific items/categories containers based on applies_to
            $('#applies_to').change(function() {
                const value = $(this).val();

                if (value === 'specific_items') {
                    $('#specific_items_container').removeClass('d-none');
                    $('#specific_categories_container').addClass('d-none');
                } else if (value === 'specific_categories') {
                    $('#specific_items_container').addClass('d-none');
                    $('#specific_categories_container').removeClass('d-none');
                } else {
                    $('#specific_items_container').addClass('d-none');
                    $('#specific_categories_container').addClass('d-none');
                }
            });

            // Load menu items when restaurant is selected
            $('#restaurant_id_modal').change(function() {
                const restaurantId = $(this).val();

                if (restaurantId) {
                    // Clear current options
                    $('#items').empty();

                    // Load menu items via AJAX
                    $.ajax({
                        url: '../api/menu_items.php',
                        type: 'GET',
                        data: { restaurant_id: restaurantId },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success && response.data) {
                                $.each(response.data, function(i, item) {
                                    $('#items').append(new Option(item.name, item.item_id));
                                });
                            }
                        }
                    });
                }
            });

            // Set default dates
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const nextMonth = new Date(now);
            nextMonth.setMonth(nextMonth.getMonth() + 1);

            $('#start_date').val(now.toISOString().slice(0, 16));
            $('#end_date').val(nextMonth.toISOString().slice(0, 16));
        });
    </script>
</body>
</html>