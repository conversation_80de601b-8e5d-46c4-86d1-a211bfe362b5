<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
require_once '../includes/functions.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Get filter parameters
$restaurant_id = isset($_GET['restaurant_id']) ? $_GET['restaurant_id'] : 'all';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Get owner's restaurants for filter dropdown
$stmt = $conn->prepare("SELECT restaurant_id, name FROM restaurants WHERE owner_id = :owner_id ORDER BY name");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();

// Set restaurant filter
$where_clause = "WHERE r.owner_id = :owner_id";
$params = [':owner_id' => $owner_id];

if ($restaurant_id !== 'all') {
    $where_clause .= " AND o.restaurant_id = :restaurant_id";
    $params[':restaurant_id'] = $restaurant_id;
}

// Set status filter
if ($status !== 'all') {
    $where_clause .= " AND o.order_status = :status";
    $params[':status'] = $status;
}

// Set date filter
if (!empty($start_date) && !empty($end_date)) {
    $where_clause .= " AND DATE(o.created_at) BETWEEN :start_date AND :end_date";
    $params[':start_date'] = $start_date;
    $params[':end_date'] = $end_date;
}

// Get orders
$stmt = $conn->prepare("
    SELECT
        o.*,
        r.name as restaurant_name,
        u.name as customer_name,
        u.phone as customer_phone,
        d.name as driver_name,
        d.phone as driver_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    $where_clause
    ORDER BY
        CASE
            WHEN o.order_status IN ('pending', 'confirmed', 'preparing', 'ready_for_pickup') THEN 0
            ELSE 1
        END,
        o.created_at DESC
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$orders = $stmt->fetchAll();

// Count orders by status
$stmt = $conn->prepare("
    SELECT
        o.order_status,
        COUNT(*) as count
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE r.owner_id = :owner_id
    " . ($restaurant_id !== 'all' ? "AND o.restaurant_id = :restaurant_id" : "") . "
    GROUP BY o.order_status
");
$stmt->bindParam(':owner_id', $owner_id);
if ($restaurant_id !== 'all') {
    $stmt->bindParam(':restaurant_id', $restaurant_id);
}
$stmt->execute();
$status_counts = [];

while ($row = $stmt->fetch()) {
    $status_counts[$row['order_status']] = $row['count'];
}

// Get total counts
$pending_count = $status_counts['pending'] ?? 0;
$processing_count = 0;
$completed_count = $status_counts['delivered'] ?? 0;
$cancelled_count = ($status_counts['cancelled'] ?? 0) + ($status_counts['rejected'] ?? 0);

foreach (['confirmed', 'preparing', 'ready_for_pickup', 'picked_up', 'on_the_way'] as $status) {
    $processing_count += $status_counts[$status] ?? 0;
}

$total_count = $pending_count + $processing_count + $completed_count + $cancelled_count;

// Get success or error message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($_GET['error']) ? $_GET['error'] : '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pesanan Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item active" href="orders.php">Pesanan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Orders Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Pesanan Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pesanan</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Pesanan</h6>
                                <h2 class="mb-0"><?= $total_count ?></h2>
                            </div>
                            <i class="fas fa-shopping-bag fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Menunggu</h6>
                                <h2 class="mb-0"><?= $pending_count ?></h2>
                            </div>
                            <i class="fas fa-clock fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Diproses</h6>
                                <h2 class="mb-0"><?= $processing_count ?></h2>
                            </div>
                            <i class="fas fa-spinner fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Selesai</h6>
                                <h2 class="mb-0"><?= $completed_count ?></h2>
                            </div>
                            <i class="fas fa-check-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Filter Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <form action="orders.php" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="restaurant_id" class="form-label">Restoran</label>
                                <select class="form-select" id="restaurant_id" name="restaurant_id">
                                    <option value="all" <?= $restaurant_id === 'all' ? 'selected' : '' ?>>Semua Restoran</option>
                                    <?php foreach ($restaurants as $rest): ?>
                                        <option value="<?= $rest['restaurant_id'] ?>" <?= $restaurant_id == $rest['restaurant_id'] ? 'selected' : '' ?>>
                                            <?= $rest['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="status" class="form-label">Status Pesanan</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>Semua Status</option>
                                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Menunggu</option>
                                    <option value="confirmed" <?= $status === 'confirmed' ? 'selected' : '' ?>>Dikonfirmasi</option>
                                    <option value="preparing" <?= $status === 'preparing' ? 'selected' : '' ?>>Sedang Disiapkan</option>
                                    <option value="ready_for_pickup" <?= $status === 'ready_for_pickup' ? 'selected' : '' ?>>Siap Diambil</option>
                                    <option value="picked_up" <?= $status === 'picked_up' ? 'selected' : '' ?>>Sudah Diambil</option>
                                    <option value="on_the_way" <?= $status === 'on_the_way' ? 'selected' : '' ?>>Dalam Perjalanan</option>
                                    <option value="delivered" <?= $status === 'delivered' ? 'selected' : '' ?>>Terkirim</option>
                                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>Dibatalkan</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                            </div>

                            <div class="col-md-2">
                                <label for="end_date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Daftar Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Pesanan</h4>
                                <p class="text-muted">Tidak ada pesanan yang sesuai dengan filter yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Tanggal</th>
                                            <th>Restoran</th>
                                            <th>Pelanggan</th>
                                            <th>Pengemudi</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td>
                                                    <?= $order['customer_name'] ?>
                                                    <small class="d-block text-muted"><?= $order['customer_phone'] ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($order['driver_id']): ?>
                                                        <?= $order['driver_name'] ?>
                                                        <small class="d-block text-muted"><?= $order['driver_phone'] ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">Belum ditugaskan</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= formatCurrency($order['total_amount']) ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    switch ($order['order_status']) {
                                                        case 'pending':
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'Menunggu';
                                                            break;
                                                        case 'confirmed':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'Dikonfirmasi';
                                                            break;
                                                        case 'preparing':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Sedang Disiapkan';
                                                            break;
                                                        case 'ready_for_pickup':
                                                            $statusClass = 'bg-warning text-dark';
                                                            $statusText = 'Siap Diambil';
                                                            break;
                                                        case 'picked_up':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'Sudah Diambil';
                                                            break;
                                                        case 'on_the_way':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'Dalam Perjalanan';
                                                            break;
                                                        case 'delivered':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'Terkirim';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'bg-danger';
                                                            $statusText = 'Dibatalkan';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'bg-danger';
                                                            $statusText = 'Ditolak';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'Tidak Diketahui';
                                                    }
                                                    ?>
                                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                                </td>
                                                <td>
                                                    <a href="order_detail.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
