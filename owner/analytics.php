<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Get owner's restaurants
$stmt = $conn->prepare("SELECT restaurant_id, name FROM restaurants WHERE owner_id = :owner_id ORDER BY name");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$restaurants = $stmt->fetchAll();

// Get filter parameters
$restaurant_id = isset($_GET['restaurant_id']) ? $_GET['restaurant_id'] : 'all';
$period = isset($_GET['period']) ? $_GET['period'] : 'week';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Set default date range if not provided
if (empty($start_date) || empty($end_date)) {
    switch ($period) {
        case 'today':
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d');
            break;
        case 'week':
            $start_date = date('Y-m-d', strtotime('-7 days'));
            $end_date = date('Y-m-d');
            break;
        case 'month':
            $start_date = date('Y-m-d', strtotime('-30 days'));
            $end_date = date('Y-m-d');
            break;
        case 'year':
            $start_date = date('Y-m-d', strtotime('-365 days'));
            $end_date = date('Y-m-d');
            break;
        default:
            $start_date = date('Y-m-d', strtotime('-7 days'));
            $end_date = date('Y-m-d');
    }
}

// Set restaurant filter
$where_clause = "WHERE r.owner_id = :owner_id";
$params = [':owner_id' => $owner_id];

if ($restaurant_id !== 'all') {
    $where_clause .= " AND o.restaurant_id = :restaurant_id";
    $params[':restaurant_id'] = $restaurant_id;
}

// Set date filter
$where_clause .= " AND DATE(o.created_at) BETWEEN :start_date AND :end_date";
$params[':start_date'] = $start_date;
$params[':end_date'] = $end_date;

// Get order statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(*) as total_orders,
        SUM(o.total_amount) as total_revenue,
        AVG(o.total_amount) as avg_order_value,
        COUNT(CASE WHEN o.order_status = 'delivered' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN o.order_status = 'cancelled' THEN 1 END) as cancelled_orders,
        COUNT(DISTINCT o.user_id) as unique_customers
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    $where_clause
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$stats = $stmt->fetch();

// Get orders by status
$stmt = $conn->prepare("
    SELECT
        o.order_status,
        COUNT(*) as count
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    $where_clause
    GROUP BY o.order_status
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$ordersByStatus = [];
while ($row = $stmt->fetch()) {
    $ordersByStatus[$row['order_status']] = $row['count'];
}

// Get orders by day
$stmt = $conn->prepare("
    SELECT
        DATE(o.created_at) as order_date,
        COUNT(*) as order_count,
        SUM(o.total_amount) as daily_revenue
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    $where_clause
    GROUP BY DATE(o.created_at)
    ORDER BY order_date
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$ordersByDay = $stmt->fetchAll();

// Get top menu items
$stmt = $conn->prepare("
    SELECT
        m.name as item_name,
        COUNT(oi.order_item_id) as order_count,
        SUM(oi.quantity) as total_quantity
    FROM order_items oi
    JOIN menu_items m ON oi.item_id = m.item_id
    JOIN orders o ON oi.order_id = o.order_id
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    $where_clause
    GROUP BY m.item_id
    ORDER BY total_quantity DESC
    LIMIT 10
");

foreach ($params as $key => $value) {
    $stmt->bindParam($key, $value);
}

$stmt->execute();
$topItems = $stmt->fetchAll();

// Prepare data for charts
$dates = [];
$orderCounts = [];
$revenues = [];

foreach ($ordersByDay as $day) {
    $dates[] = date('d/m', strtotime($day['order_date']));
    $orderCounts[] = $day['order_count'];
    $revenues[] = $day['daily_revenue'];
}

$statusLabels = [];
$statusData = [];
$statusColors = [
    'pending' => '#6c757d',
    'confirmed' => '#17a2b8',
    'preparing' => '#007bff',
    'ready_for_pickup' => '#ffc107',
    'picked_up' => '#17a2b8',
    'on_the_way' => '#007bff',
    'delivered' => '#28a745',
    'cancelled' => '#dc3545'
];

foreach ($ordersByStatus as $status => $count) {
    switch ($status) {
        case 'pending':
            $statusLabels[] = 'Menunggu';
            break;
        case 'confirmed':
            $statusLabels[] = 'Dikonfirmasi';
            break;
        case 'preparing':
            $statusLabels[] = 'Sedang Disiapkan';
            break;
        case 'ready_for_pickup':
            $statusLabels[] = 'Siap Diambil';
            break;
        case 'picked_up':
            $statusLabels[] = 'Sudah Diambil';
            break;
        case 'on_the_way':
            $statusLabels[] = 'Dalam Perjalanan';
            break;
        case 'delivered':
            $statusLabels[] = 'Terkirim';
            break;
        case 'cancelled':
            $statusLabels[] = 'Dibatalkan';
            break;
        default:
            $statusLabels[] = $status;
    }
    $statusData[] = $count;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analitik Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item active" href="analytics.php">Analitik</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Analytics Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Analitik Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analitik</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="orders.php" class="btn btn-outline-primary">
                    <i class="fas fa-shopping-bag me-2"></i>Lihat Pesanan
                </a>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Filter Analitik</h5>
                    </div>
                    <div class="card-body">
                        <form action="analytics.php" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="restaurant_id" class="form-label">Restoran</label>
                                <select class="form-select" id="restaurant_id" name="restaurant_id">
                                    <option value="all" <?= $restaurant_id === 'all' ? 'selected' : '' ?>>Semua Restoran</option>
                                    <?php foreach ($restaurants as $rest): ?>
                                        <option value="<?= $rest['restaurant_id'] ?>" <?= $restaurant_id == $rest['restaurant_id'] ? 'selected' : '' ?>>
                                            <?= $rest['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="period" class="form-label">Periode</label>
                                <select class="form-select" id="period" name="period">
                                    <option value="today" <?= $period === 'today' ? 'selected' : '' ?>>Hari Ini</option>
                                    <option value="week" <?= $period === 'week' ? 'selected' : '' ?>>7 Hari Terakhir</option>
                                    <option value="month" <?= $period === 'month' ? 'selected' : '' ?>>30 Hari Terakhir</option>
                                    <option value="year" <?= $period === 'year' ? 'selected' : '' ?>>1 Tahun Terakhir</option>
                                    <option value="custom" <?= $period === 'custom' ? 'selected' : '' ?>>Kustom</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                            </div>

                            <div class="col-md-2">
                                <label for="end_date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white shadow-sm h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Pesanan</h6>
                                <h2 class="mb-0"><?= $stats['total_orders'] ?: 0 ?></h2>
                            </div>
                            <i class="fas fa-shopping-bag fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white shadow-sm h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Total Pendapatan</h6>
                                <h2 class="mb-0">Rp<?= number_format(($stats['total_revenue'] ?: 0) * 15000, 0, ',', '.') ?></h2>
                            </div>
                            <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white shadow-sm h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Rata-rata Pesanan</h6>
                                <h2 class="mb-0">Rp<?= number_format(($stats['avg_order_value'] ?: 0) * 15000, 0, ',', '.') ?></h2>
                            </div>
                            <i class="fas fa-chart-line fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark shadow-sm h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase">Pelanggan Unik</h6>
                                <h2 class="mb-0"><?= $stats['unique_customers'] ?: 0 ?></h2>
                            </div>
                            <i class="fas fa-users fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tren Pesanan & Pendapatan</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="ordersRevenueChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Status Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="orderStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Items -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Menu Terlaris</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($topItems)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                                <h4>Tidak Ada Data</h4>
                                <p class="text-muted">Belum ada data pesanan untuk periode yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Nama Item</th>
                                            <th>Jumlah Pesanan</th>
                                            <th>Total Kuantitas</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $i = 1; foreach ($topItems as $item): ?>
                                            <tr>
                                                <td><?= $i++ ?></td>
                                                <td><?= $item['item_name'] ?></td>
                                                <td><?= $item['order_count'] ?></td>
                                                <td><?= $item['total_quantity'] ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Period selector
        document.getElementById('period').addEventListener('change', function() {
            const customPeriod = this.value === 'custom';
            document.getElementById('start_date').disabled = !customPeriod;
            document.getElementById('end_date').disabled = !customPeriod;

            if (!customPeriod) {
                document.querySelector('form').submit();
            }
        });

        // Initialize date fields
        const periodSelect = document.getElementById('period');
        const customPeriod = periodSelect.value === 'custom';
        document.getElementById('start_date').disabled = !customPeriod;
        document.getElementById('end_date').disabled = !customPeriod;

        // Orders & Revenue Chart
        const ordersRevenueCtx = document.getElementById('ordersRevenueChart').getContext('2d');
        const ordersRevenueChart = new Chart(ordersRevenueCtx, {
            type: 'line',
            data: {
                labels: <?= json_encode($dates) ?>,
                datasets: [
                    {
                        label: 'Jumlah Pesanan',
                        data: <?= json_encode($orderCounts) ?>,
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Pendapatan (Rp)',
                        data: <?= json_encode(array_map(function($val) { return $val * 15000; }, $revenues)) ?>,
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Jumlah Pesanan'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Pendapatan (Rp)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Order Status Chart
        const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
        const orderStatusChart = new Chart(orderStatusCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode($statusLabels) ?>,
                datasets: [{
                    data: <?= json_encode($statusData) ?>,
                    backgroundColor: [
                        '#6c757d', // pending
                        '#17a2b8', // confirmed
                        '#007bff', // preparing
                        '#ffc107', // ready_for_pickup
                        '#17a2b8', // picked_up
                        '#007bff', // on_the_way
                        '#28a745', // delivered
                        '#dc3545'  // cancelled
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    </script>
</body>
</html>