<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = ?");
$stmt->execute([$owner_id]);
$owner = $stmt->fetch();

// Get restaurant information
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE owner_id = ?");
$stmt->execute([$owner_id]);
$restaurant = $stmt->fetch();

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_location'])) {
    $latitude = $_POST['latitude'];
    $longitude = $_POST['longitude'];
    $address = $_POST['address'];
    
    // Validate input
    if (empty($latitude) || empty($longitude) || empty($address)) {
        $error = 'Semua field harus diisi';
    } else {
        try {
            // Update restaurant location
            $stmt = $conn->prepare("
                UPDATE restaurants 
                SET latitude = :latitude, longitude = :longitude, address = :address 
                WHERE restaurant_id = :restaurant_id
            ");
            $stmt->bindParam(':latitude', $latitude);
            $stmt->bindParam(':longitude', $longitude);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':restaurant_id', $restaurant['restaurant_id']);
            $stmt->execute();
            
            $success = 'Lokasi restoran berhasil diperbarui!';
            
            // Refresh restaurant data
            $stmt = $conn->prepare("SELECT * FROM restaurants WHERE owner_id = ?");
            $stmt->execute([$owner_id]);
            $restaurant = $stmt->fetch();
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Lokasi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 400px;
            width: 100%;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="manage_menu.php">Menu</a></li>
                            <li><a class="dropdown-item active" href="location_settings.php">Lokasi</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Location Settings Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Pengaturan Lokasi Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pengaturan Lokasi</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Lokasi Restoran</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">
                            Atur lokasi restoran Anda dengan tepat untuk memastikan driver dapat menemukan restoran Anda dengan mudah.
                            Klik pada peta untuk memilih lokasi atau gunakan tombol "Gunakan Lokasi Saya" untuk menggunakan lokasi saat ini.
                        </p>
                        
                        <div id="map"></div>
                        
                        <form action="location_settings.php" method="post">
                            <input type="hidden" name="update_location" value="1">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="text" class="form-control" id="latitude" name="latitude" value="<?= $restaurant['latitude'] ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="text" class="form-control" id="longitude" name="longitude" value="<?= $restaurant['longitude'] ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Alamat</label>
                                <textarea class="form-control" id="address" name="address" rows="3" required><?= $restaurant['address'] ?></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" id="use-my-location" class="btn btn-outline-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i>Gunakan Lokasi Saya
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Lokasi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Petunjuk Pengaturan Lokasi</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Klik pada peta untuk memilih lokasi restoran Anda.</li>
                            <li>Koordinat latitude dan longitude akan otomatis terisi.</li>
                            <li>Isi alamat lengkap restoran Anda.</li>
                            <li>Klik tombol "Simpan Lokasi" untuk menyimpan perubahan.</li>
                        </ol>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Lokasi yang akurat sangat penting untuk memastikan driver dapat menemukan restoran Anda dengan mudah dan menerima notifikasi pesanan yang siap diambil.
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Restoran</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <?php if (!empty($restaurant['logo_url'])): ?>
                                    <img src="<?= $restaurant['logo_url'] ?>" alt="<?= $restaurant['name'] ?>" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px">
                                        <i class="fas fa-store fa-2x text-primary"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= $restaurant['name'] ?></h6>
                                <p class="text-muted mb-0"><?= $restaurant['phone'] ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-map-marker-alt me-2 text-primary"></i>Alamat Saat Ini:</h6>
                            <p class="mb-0"><?= $restaurant['address'] ?: 'Belum diatur' ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-globe me-2 text-primary"></i>Koordinat Saat Ini:</h6>
                            <p class="mb-0">
                                <?php if ($restaurant['latitude'] && $restaurant['longitude']): ?>
                                    Latitude: <?= $restaurant['latitude'] ?><br>
                                    Longitude: <?= $restaurant['longitude'] ?>
                                <?php else: ?>
                                    Belum diatur
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const map = L.map('map').setView([<?= $restaurant['latitude'] ?: '-6.2088' ?>, <?= $restaurant['longitude'] ?: '106.8456' ?>], 15);
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Add marker for restaurant location
        let marker;
        if (<?= $restaurant['latitude'] && $restaurant['longitude'] ? 'true' : 'false' ?>) {
            marker = L.marker([<?= $restaurant['latitude'] ?: '-6.2088' ?>, <?= $restaurant['longitude'] ?: '106.8456' ?>]).addTo(map);
        }
        
        // Handle map click
        map.on('click', function(e) {
            // Update marker position
            if (marker) {
                marker.setLatLng(e.latlng);
            } else {
                marker = L.marker(e.latlng).addTo(map);
            }
            
            // Update form fields
            document.getElementById('latitude').value = e.latlng.lat.toFixed(8);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(8);
            
            // Reverse geocode to get address (optional)
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${e.latlng.lat}&lon=${e.latlng.lng}`)
                .then(response => response.json())
                .then(data => {
                    if (data.display_name) {
                        document.getElementById('address').value = data.display_name;
                    }
                })
                .catch(error => console.error('Error:', error));
        });
        
        // Handle "Use My Location" button
        document.getElementById('use-my-location').addEventListener('click', function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    // Update map view
                    map.setView([lat, lng], 15);
                    
                    // Update marker position
                    if (marker) {
                        marker.setLatLng([lat, lng]);
                    } else {
                        marker = L.marker([lat, lng]).addTo(map);
                    }
                    
                    // Update form fields
                    document.getElementById('latitude').value = lat.toFixed(8);
                    document.getElementById('longitude').value = lng.toFixed(8);
                    
                    // Reverse geocode to get address (optional)
                    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.display_name) {
                                document.getElementById('address').value = data.display_name;
                            }
                        })
                        .catch(error => console.error('Error:', error));
                }, function(error) {
                    alert('Error getting location: ' + error.message);
                });
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        });
    </script>
</body>
</html>
