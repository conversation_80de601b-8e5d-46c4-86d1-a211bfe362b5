<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Check if owner account is active
if ($owner['status'] !== 'active') {
    $error = 'Akun Anda belum diaktifkan oleh admin. Silakan hubungi admin untuk informasi lebih lanjut.';
} else {
    $error = '';
    $success = '';

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';
        $description = $_POST['description'] ?? '';
        $latitude = $_POST['latitude'] ?? 0;
        $longitude = $_POST['longitude'] ?? 0;
        $opening_time = $_POST['opening_time'] ?? null;
        $closing_time = $_POST['closing_time'] ?? null;
        $min_order_amount = $_POST['min_order_amount'] ?? 0;
        $delivery_fee = $_POST['delivery_fee'] ?? 0;

        // Validate inputs
        if (empty($name) || empty($email) || empty($phone) || empty($address)) {
            $error = 'Silakan isi semua bidang yang diperlukan';
        } else {
            try {
                // Insert restaurant
                $stmt = $conn->prepare("
                    INSERT INTO restaurants (
                        owner_id, name, email, phone, address,
                        latitude, longitude, description,
                        opening_time, closing_time,
                        min_order_amount, delivery_fee
                    ) VALUES (
                        :owner_id, :name, :email, :phone, :address,
                        :latitude, :longitude, :description,
                        :opening_time, :closing_time,
                        :min_order_amount, :delivery_fee
                    )
                ");

                $stmt->bindParam(':owner_id', $owner_id);
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':phone', $phone);
                $stmt->bindParam(':address', $address);
                $stmt->bindParam(':latitude', $latitude);
                $stmt->bindParam(':longitude', $longitude);
                $stmt->bindParam(':description', $description);
                $stmt->bindParam(':opening_time', $opening_time);
                $stmt->bindParam(':closing_time', $closing_time);
                $stmt->bindParam(':min_order_amount', $min_order_amount);
                $stmt->bindParam(':delivery_fee', $delivery_fee);

                if ($stmt->execute()) {
                    $restaurant_id = $conn->lastInsertId();
                    $success = 'Restoran berhasil ditambahkan!';

                    // Redirect to restaurant management page after 2 seconds
                    header('refresh:2;url=dashboard.php');
                } else {
                    $error = 'Gagal menambahkan restoran';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Add Restaurant Form -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Tambah Restoran Baru</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Tambah Restoran</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($owner['status'] === 'active'): ?>
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <form action="add_restaurant.php" method="post">
                                <h5 class="mb-4">Informasi Dasar</h5>

                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Restoran <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Nomor Telepon <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Alamat <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Lokasi Restoran <span class="text-danger">*</span></label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> Anda dapat menggunakan GPS perangkat untuk menentukan lokasi restoran secara otomatis dengan mengklik tombol "Gunakan Lokasi Saat Ini" di bawah.
                                    </div>
                                    <div id="map"></div>
                                    <button type="button" id="get-location" class="btn btn-primary mb-3">
                                        <i class="fas fa-map-marker-alt me-2"></i>Gunakan Lokasi Saat Ini
                                    </button>
                                    <div id="location-status" class="mb-3" style="display: none;"></div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="latitude" class="form-label">Latitude</label>
                                            <input type="text" class="form-control" id="latitude" name="latitude" value="0" readonly>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="longitude" class="form-label">Longitude</label>
                                            <input type="text" class="form-control" id="longitude" name="longitude" value="0" readonly>
                                        </div>
                                    </div>
                                    <small class="text-muted">Klik pada peta untuk menentukan lokasi secara manual atau gunakan tombol "Gunakan Lokasi Saat Ini" untuk menggunakan GPS perangkat Anda.</small>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Deskripsi Restoran</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                </div>

                                <h5 class="mb-4 mt-5">Jam Operasional & Biaya</h5>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="opening_time" class="form-label">Jam Buka</label>
                                        <input type="time" class="form-control" id="opening_time" name="opening_time">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="closing_time" class="form-label">Jam Tutup</label>
                                        <input type="time" class="form-control" id="closing_time" name="closing_time">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="min_order_amount" class="form-label">Minimal Pemesanan (Rp)</label>
                                        <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" value="0" min="0" step="0.01">
                                        <small class="text-muted">Masukkan dalam satuan 0.01 (Rp15.000 = 1)</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="delivery_fee" class="form-label">Biaya Pengiriman (Rp)</label>
                                        <input type="number" class="form-control" id="delivery_fee" name="delivery_fee" value="0" min="0" step="0.01">
                                        <small class="text-muted">Masukkan dalam satuan 0.01 (Rp15.000 = 1)</small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <a href="dashboard.php" class="btn btn-outline-secondary me-md-2">Batal</a>
                                    <button type="submit" class="btn btn-primary">Simpan Restoran</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Petunjuk</h5>
                        </div>
                        <div class="card-body">
                            <p>Isi formulir ini untuk menambahkan restoran baru ke platform KikaZen Ship.</p>
                            <ul>
                                <li>Semua bidang dengan tanda <span class="text-danger">*</span> wajib diisi.</li>
                                <li>Pastikan nomor telepon dan alamat email valid.</li>
                                <li>Anda dapat menambahkan logo dan banner restoran setelah restoran dibuat.</li>
                                <li>Untuk koordinat lokasi (latitude/longitude), Anda dapat menggunakan Google Maps untuk mendapatkan koordinat yang tepat.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        var map = L.map('map').setView([-6.2088, 106.8456], 13); // Default to Jakarta

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        var marker;

        // Handle map click
        map.on('click', function(e) {
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker(e.latlng).addTo(map);
            document.getElementById('latitude').value = e.latlng.lat.toFixed(6);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(6);
        });

        // Get current location
        document.getElementById('get-location').addEventListener('click', function() {
            const locationStatus = document.getElementById('location-status');
            locationStatus.style.display = 'block';
            locationStatus.className = 'alert alert-info mb-3';
            locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Sedang mendapatkan lokasi Anda...';

            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        var lat = position.coords.latitude;
                        var lng = position.coords.longitude;

                        if (marker) {
                            map.removeLayer(marker);
                        }

                        marker = L.marker([lat, lng]).addTo(map);
                        map.setView([lat, lng], 15);

                        document.getElementById('latitude').value = lat.toFixed(6);
                        document.getElementById('longitude').value = lng.toFixed(6);

                        locationStatus.className = 'alert alert-success mb-3';
                        locationStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i> Lokasi berhasil ditemukan dan diterapkan!';

                        // Highlight the coordinates
                        const latInput = document.getElementById('latitude');
                        const lngInput = document.getElementById('longitude');
                        latInput.classList.add('is-valid');
                        lngInput.classList.add('is-valid');

                        setTimeout(function() {
                            latInput.classList.remove('is-valid');
                            lngInput.classList.remove('is-valid');
                        }, 3000);
                    },
                    function(error) {
                        locationStatus.className = 'alert alert-danger mb-3';
                        let errorMessage = 'Tidak dapat mengakses lokasi Anda: ';

                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage += 'Izin penggunaan GPS ditolak. Silakan aktifkan izin lokasi di browser Anda.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage += 'Informasi lokasi tidak tersedia. Pastikan GPS perangkat Anda aktif.';
                                break;
                            case error.TIMEOUT:
                                errorMessage += 'Waktu permintaan lokasi habis. Silakan coba lagi.';
                                break;
                            default:
                                errorMessage += error.message;
                        }

                        locationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> ' + errorMessage;
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    }
                );
            } else {
                locationStatus.className = 'alert alert-danger mb-3';
                locationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Geolocation tidak didukung oleh browser Anda. Silakan gunakan browser yang mendukung GPS.';
            }
        });
    </script>
</body>
</html>
