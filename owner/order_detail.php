<?php
// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Check if order ID is provided
if (!isset($_GET['id'])) {
    header('Location: orders.php');
    exit;
}

$order_id = $_GET['id'];

// Check if the order belongs to one of the owner's restaurants
$stmt = $conn->prepare("
    SELECT o.* FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.order_id = :order_id AND r.owner_id = :owner_id
");
$stmt->bindParam(':order_id', $order_id);
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    header('Location: orders.php?error=Pesanan tidak ditemukan');
    exit;
}

$order = $stmt->fetch();

// Get restaurant information
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id");
$stmt->bindParam(':restaurant_id', $order['restaurant_id']);
$stmt->execute();
$restaurant = $stmt->fetch();

// Get customer information
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $order['user_id']);
$stmt->execute();
$customer = $stmt->fetch();

// Get driver information if assigned
$driver = null;
if ($order['driver_id']) {
    $stmt = $conn->prepare("SELECT * FROM drivers WHERE driver_id = :driver_id");
    $stmt->bindParam(':driver_id', $order['driver_id']);
    $stmt->execute();
    $driver = $stmt->fetch();
}

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, m.name as item_name, m.price as item_price
    FROM order_items oi
    JOIN menu_items m ON oi.item_id = m.item_id
    WHERE oi.order_id = :order_id
");
$stmt->bindParam(':order_id', $order_id);
$stmt->execute();
$orderItems = $stmt->fetchAll();

// Get order tracking
$stmt = $conn->prepare("
    SELECT * FROM order_tracking
    WHERE order_id = :order_id
    ORDER BY timestamp ASC
");
$stmt->bindParam(':order_id', $order_id);
$stmt->execute();
$orderTracking = $stmt->fetchAll();

// Handle status update
$success = '';
$error = '';

// Handle quick confirm action from dashboard
if (isset($_GET['action']) && $_GET['action'] === 'confirm' && $order['order_status'] === 'pending') {
    $newStatus = 'confirmed';
    $notes = 'Dikonfirmasi dari dashboard';

    try {
        // Begin transaction
        $conn->beginTransaction();

        // Update order status
        $stmt = $conn->prepare("
            UPDATE orders
            SET order_status = :status, updated_at = NOW()
            WHERE order_id = :order_id
        ");
        $stmt->bindParam(':status', $newStatus);
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        // Add to order tracking
        $stmt = $conn->prepare("
            INSERT INTO order_tracking (
                order_id, status, notes
            ) VALUES (
                :order_id, :status, :notes
            )
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->bindParam(':status', $newStatus);
        $stmt->bindParam(':notes', $notes);
        $stmt->execute();

        // Commit transaction
        $conn->commit();

        $success = 'Status pesanan berhasil diperbarui menjadi Dikonfirmasi!';

        // Refresh order data
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = :order_id");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        $order = $stmt->fetch();

        // Refresh order tracking
        $stmt = $conn->prepare("
            SELECT * FROM order_tracking
            WHERE order_id = :order_id
            ORDER BY timestamp ASC
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        $orderTracking = $stmt->fetchAll();
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        $error = 'Database error: ' . $e->getMessage();
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $newStatus = $_POST['status'];
    $notes = $_POST['notes'] ?? '';

    // Validate status transition
    $validTransition = false;

    switch ($order['order_status']) {
        case 'pending':
            $validTransition = in_array($newStatus, ['confirmed', 'cancelled']);
            break;
        case 'confirmed':
            $validTransition = in_array($newStatus, ['preparing', 'cancelled']);
            break;
        case 'preparing':
            $validTransition = in_array($newStatus, ['ready_for_pickup', 'cancelled']);
            break;
        default:
            $validTransition = false;
    }

    if (!$validTransition) {
        $error = 'Transisi status tidak valid';
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();

            // Update order status
            $stmt = $conn->prepare("
                UPDATE orders
                SET order_status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            ");
            $stmt->bindParam(':status', $newStatus);
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();

            // Add to order tracking
            $stmt = $conn->prepare("
                INSERT INTO order_tracking (
                    order_id, status, notes
                ) VALUES (
                    :order_id, :status, :notes
                )
            ");
            $stmt->bindParam(':order_id', $order_id);
            $stmt->bindParam(':status', $newStatus);
            $stmt->bindParam(':notes', $notes);
            $stmt->execute();

            // If status is ready_for_pickup, notify available drivers
            if ($newStatus === 'ready_for_pickup') {
                $message = "Pesanan #$order_id dari restoran {$restaurant['name']} siap untuk diambil!";
                notifyAvailableDrivers($order_id, $order['restaurant_id'], 'ready_for_pickup', $message);
                $success = 'Status pesanan berhasil diperbarui dan notifikasi telah dikirim ke pengemudi!';
            } else {
                $success = 'Status pesanan berhasil diperbarui!';
            }

            // Commit transaction
            $conn->commit();

            // Refresh order data
            $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = :order_id");
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();
            $order = $stmt->fetch();

            // Refresh order tracking
            $stmt = $conn->prepare("
                SELECT * FROM order_tracking
                WHERE order_id = :order_id
                ORDER BY timestamp ASC
            ");
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();
            $orderTracking = $stmt->fetchAll();
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Pesanan #<?= $order_id ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -30px;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #007bff;
        }
        .timeline-item.active::before {
            background-color: #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Order Detail Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Detail Pesanan #<?= $order_id ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="orders.php">Pesanan</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Detail Pesanan #<?= $order_id ?></li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <?php
                ?>
                <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?> fs-6 p-2">
                    <?= getOrderStatusText($order['order_status']) ?>
                </span>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar-alt me-2"></i>Tanggal Pesanan:</h6>
                                <p><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock me-2"></i>Terakhir Diperbarui:</h6>
                                <p><?= date('d/m/Y H:i', strtotime($order['updated_at'])) ?></p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-store me-2"></i>Restoran:</h6>
                                <p class="mb-1"><?= $restaurant['name'] ?></p>
                                <p class="small text-muted mb-0"><?= $restaurant['address'] ?></p>
                                <p class="small text-muted mb-0"><?= $restaurant['phone'] ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-user me-2"></i>Pelanggan:</h6>
                                <p class="mb-1"><?= $customer['name'] ?></p>
                                <p class="small text-muted mb-0"><?= $customer['phone'] ?></p>
                                <p class="small text-muted mb-0"><?= $customer['email'] ?></p>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-map-marker-alt me-2"></i>Alamat Pengiriman:</h6>
                            <p><?= $order['delivery_address'] ?></p>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-sticky-note me-2"></i>Catatan:</h6>
                            <p><?= !empty($order['notes']) ? $order['notes'] : 'Tidak ada catatan' ?></p>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-money-bill-wave me-2"></i>Metode Pembayaran:</h6>
                            <p><?= $order['payment_method'] === 'cash' ? 'Tunai' : ucfirst($order['payment_method']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Item Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Harga</th>
                                        <th>Jumlah</th>
                                        <th class="text-end">Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orderItems as $item): ?>
                                        <tr>
                                            <td><?= $item['item_name'] ?></td>
                                            <td><?= formatCurrency($item['item_price']) ?></td>
                                            <td><?= $item['quantity'] ?></td>
                                            <td class="text-end"><?= formatCurrency($item['item_price'] * $item['quantity']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>Subtotal</strong></td>
                                        <td class="text-end"><?= formatCurrency($order['subtotal']) ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>Biaya Pengiriman</strong></td>
                                        <td class="text-end"><?= formatCurrency($order['delivery_fee']) ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>Total</strong></td>
                                        <td class="text-end"><strong><?= formatCurrency($order['total_amount']) ?></strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Peta Lokasi</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <?php if (in_array($order['order_status'], ['pending', 'confirmed', 'preparing'])): ?>
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Perbarui Status</h5>
                        </div>
                        <div class="card-body">
                            <form action="order_detail.php?id=<?= $order_id ?>" method="post">
                                <input type="hidden" name="update_status" value="1">

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status Baru</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <?php if ($order['order_status'] === 'pending'): ?>
                                            <option value="confirmed">Dikonfirmasi</option>
                                            <option value="cancelled">Dibatalkan</option>
                                        <?php elseif ($order['order_status'] === 'confirmed'): ?>
                                            <option value="preparing">Sedang Disiapkan</option>
                                            <option value="cancelled">Dibatalkan</option>
                                        <?php elseif ($order['order_status'] === 'preparing'): ?>
                                            <option value="ready_for_pickup">Siap Diambil</option>
                                            <option value="cancelled">Dibatalkan</option>
                                        <?php endif; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Catatan (opsional)</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">Perbarui Status</button>
                                    <a href="chat.php?start_chat=order&order_id=<?= $order_id ?>" class="btn btn-success">
                                        <i class="fas fa-comments me-2"></i>Chat Tentang Pesanan
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($driver): ?>
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Informasi Pengemudi</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px">
                                        <i class="fas fa-user fa-2x text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0"><?= $driver['name'] ?></h6>
                                    <p class="text-muted mb-0">
                                        <?php
                                        switch ($driver['vehicle_type']) {
                                            case 'motorcycle':
                                                echo '<i class="fas fa-motorcycle me-1"></i> Motor';
                                                break;
                                            case 'car':
                                                echo '<i class="fas fa-car me-1"></i> Mobil';
                                                break;
                                            case 'bicycle':
                                                echo '<i class="fas fa-bicycle me-1"></i> Sepeda';
                                                break;
                                            default:
                                                echo $driver['vehicle_type'];
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-phone me-2 text-primary"></i> <?= $driver['phone'] ?>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-envelope me-2 text-primary"></i> <?= $driver['email'] ?>
                            </div>
                            <?php if (!empty($driver['license_plate'])): ?>
                                <div class="mb-2">
                                    <i class="fas fa-id-card me-2 text-primary"></i> <?= $driver['license_plate'] ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Riwayat Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php if (empty($orderTracking)): ?>
                                <p class="text-muted">Belum ada riwayat status</p>
                            <?php else: ?>
                                <?php foreach ($orderTracking as $tracking): ?>
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    $statusText = '';

                                    switch ($tracking['status']) {
                                        case 'pending':
                                            $statusClass = 'text-secondary';
                                            $statusIcon = 'fa-clock';
                                            $statusText = 'Menunggu';
                                            break;
                                        case 'confirmed':
                                            $statusClass = 'text-info';
                                            $statusIcon = 'fa-check';
                                            $statusText = 'Dikonfirmasi';
                                            break;
                                        case 'preparing':
                                            $statusClass = 'text-primary';
                                            $statusIcon = 'fa-utensils';
                                            $statusText = 'Sedang Disiapkan';
                                            break;
                                        case 'ready_for_pickup':
                                            $statusClass = 'text-warning';
                                            $statusIcon = 'fa-box';
                                            $statusText = 'Siap Diambil';
                                            break;
                                        case 'picked_up':
                                            $statusClass = 'text-info';
                                            $statusIcon = 'fa-shopping-bag';
                                            $statusText = 'Sudah Diambil';
                                            break;
                                        case 'on_the_way':
                                            $statusClass = 'text-primary';
                                            $statusIcon = 'fa-motorcycle';
                                            $statusText = 'Dalam Perjalanan';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'text-success';
                                            $statusIcon = 'fa-check-circle';
                                            $statusText = 'Terkirim';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'text-danger';
                                            $statusIcon = 'fa-times-circle';
                                            $statusText = 'Dibatalkan';
                                            break;
                                        default:
                                            $statusClass = 'text-secondary';
                                            $statusIcon = 'fa-question-circle';
                                            $statusText = 'Tidak Diketahui';
                                    }

                                    $isActive = $tracking['status'] === $order['order_status'];
                                    ?>
                                    <div class="timeline-item <?= $isActive ? 'active' : '' ?>">
                                        <div class="card">
                                            <div class="card-body py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0 <?= $statusClass ?>">
                                                        <i class="fas <?= $statusIcon ?> me-2"></i><?= $statusText ?>
                                                    </h6>
                                                    <small class="text-muted"><?= date('d/m/Y H:i', strtotime($tracking['timestamp'])) ?></small>
                                                </div>
                                                <?php if (!empty($tracking['notes'])): ?>
                                                    <p class="small text-muted mb-0 mt-1"><?= $tracking['notes'] ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        var restaurantLat = <?= $restaurant['latitude'] ?: 0 ?>;
        var restaurantLng = <?= $restaurant['longitude'] ?: 0 ?>;
        var customerLat = <?= $order['delivery_latitude'] ?: 0 ?>;
        var customerLng = <?= $order['delivery_longitude'] ?: 0 ?>;

        var map = L.map('map');

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add restaurant marker
        var restaurantMarker = L.marker([restaurantLat, restaurantLng]).addTo(map);
        restaurantMarker.bindPopup("<b>Restoran:</b><br>" + <?= json_encode($restaurant['name']) ?>).openPopup();

        // Add customer marker
        var customerMarker = L.marker([customerLat, customerLng]).addTo(map);
        customerMarker.bindPopup("<b>Pelanggan:</b><br>" + <?= json_encode($order['delivery_address']) ?>);

        // Fit bounds to show both markers
        var bounds = L.latLngBounds([
            [restaurantLat, restaurantLng],
            [customerLat, customerLng]
        ]);
        map.fitBounds(bounds, { padding: [50, 50] });
    </script>
</body>
</html>