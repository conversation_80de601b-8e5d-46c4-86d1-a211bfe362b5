<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Check if restaurant ID is provided
if (!isset($_GET['id'])) {
    header('Location: dashboard.php');
    exit;
}

$restaurant_id = $_GET['id'];

// Check if the restaurant belongs to the logged-in owner
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id AND owner_id = :owner_id");
$stmt->bindParam(':restaurant_id', $restaurant_id);
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    header('Location: dashboard.php');
    exit;
}

$restaurant = $stmt->fetch();
$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_info') {
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';
        $description = $_POST['description'] ?? '';
        $latitude = $_POST['latitude'] ?? 0;
        $longitude = $_POST['longitude'] ?? 0;
        $opening_time = $_POST['opening_time'] ?? null;
        $closing_time = $_POST['closing_time'] ?? null;
        $min_order_amount = $_POST['min_order_amount'] ?? 0;
        $delivery_fee = $_POST['delivery_fee'] ?? 0;
        $is_open = isset($_POST['is_open']) ? 1 : 0;

        // Validate inputs
        if (empty($name) || empty($email) || empty($phone) || empty($address)) {
            $error = 'Silakan isi semua bidang yang diperlukan';
        } else {
            try {
                // Update restaurant
                $stmt = $conn->prepare("
                    UPDATE restaurants SET
                        name = :name,
                        email = :email,
                        phone = :phone,
                        address = :address,
                        latitude = :latitude,
                        longitude = :longitude,
                        description = :description,
                        opening_time = :opening_time,
                        closing_time = :closing_time,
                        min_order_amount = :min_order_amount,
                        delivery_fee = :delivery_fee,
                        is_open = :is_open
                    WHERE restaurant_id = :restaurant_id AND owner_id = :owner_id
                ");

                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':phone', $phone);
                $stmt->bindParam(':address', $address);
                $stmt->bindParam(':latitude', $latitude);
                $stmt->bindParam(':longitude', $longitude);
                $stmt->bindParam(':description', $description);
                $stmt->bindParam(':opening_time', $opening_time);
                $stmt->bindParam(':closing_time', $closing_time);
                $stmt->bindParam(':min_order_amount', $min_order_amount);
                $stmt->bindParam(':delivery_fee', $delivery_fee);
                $stmt->bindParam(':is_open', $is_open);
                $stmt->bindParam(':restaurant_id', $restaurant_id);
                $stmt->bindParam(':owner_id', $owner_id);

                if ($stmt->execute()) {
                    $success = 'Informasi restoran berhasil diperbarui!';

                    // Refresh restaurant data
                    $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id");
                    $stmt->bindParam(':restaurant_id', $restaurant_id);
                    $stmt->execute();
                    $restaurant = $stmt->fetch();
                } else {
                    $error = 'Gagal memperbarui informasi restoran';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'upload_logo') {
        // Handle logo upload
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $maxSize = 2 * 1024 * 1024; // 2MB

            if (!in_array($_FILES['logo']['type'], $allowedTypes)) {
                $error = 'Hanya file JPG, PNG, dan GIF yang diperbolehkan untuk logo';
            } elseif ($_FILES['logo']['size'] > $maxSize) {
                $error = 'Ukuran file logo tidak boleh lebih dari 2MB';
            } else {
                $uploadDir = '../uploads/restaurants/';

                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $fileName = 'logo_' . $restaurant_id . '_' . time() . '_' . basename($_FILES['logo']['name']);
                $uploadPath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['logo']['tmp_name'], $uploadPath)) {
                    // Update logo URL in database
                    $logoUrl = 'uploads/restaurants/' . $fileName;
                    $stmt = $conn->prepare("UPDATE restaurants SET logo_url = :logo_url WHERE restaurant_id = :restaurant_id");
                    $stmt->bindParam(':logo_url', $logoUrl);
                    $stmt->bindParam(':restaurant_id', $restaurant_id);

                    if ($stmt->execute()) {
                        $success = 'Logo restoran berhasil diunggah!';

                        // Refresh restaurant data
                        $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id");
                        $stmt->bindParam(':restaurant_id', $restaurant_id);
                        $stmt->execute();
                        $restaurant = $stmt->fetch();
                    } else {
                        $error = 'Gagal memperbarui logo restoran di database';
                    }
                } else {
                    $error = 'Gagal mengunggah logo restoran';
                }
            }
        } else {
            $error = 'Silakan pilih file logo untuk diunggah';
        }
    } elseif ($action === 'upload_banner') {
        // Handle banner upload
        if (isset($_FILES['banner']) && $_FILES['banner']['error'] === UPLOAD_ERR_OK) {
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $maxSize = 5 * 1024 * 1024; // 5MB

            if (!in_array($_FILES['banner']['type'], $allowedTypes)) {
                $error = 'Hanya file JPG, PNG, dan GIF yang diperbolehkan untuk banner';
            } elseif ($_FILES['banner']['size'] > $maxSize) {
                $error = 'Ukuran file banner tidak boleh lebih dari 5MB';
            } else {
                $uploadDir = '../uploads/restaurants/';

                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $fileName = 'banner_' . $restaurant_id . '_' . time() . '_' . basename($_FILES['banner']['name']);
                $uploadPath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['banner']['tmp_name'], $uploadPath)) {
                    // Update banner URL in database
                    $bannerUrl = 'uploads/restaurants/' . $fileName;
                    $stmt = $conn->prepare("UPDATE restaurants SET banner_url = :banner_url WHERE restaurant_id = :restaurant_id");
                    $stmt->bindParam(':banner_url', $bannerUrl);
                    $stmt->bindParam(':restaurant_id', $restaurant_id);

                    if ($stmt->execute()) {
                        $success = 'Banner restoran berhasil diunggah!';

                        // Refresh restaurant data
                        $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id");
                        $stmt->bindParam(':restaurant_id', $restaurant_id);
                        $stmt->execute();
                        $restaurant = $stmt->fetch();
                    } else {
                        $error = 'Gagal memperbarui banner restoran di database';
                    }
                } else {
                    $error = 'Gagal mengunggah banner restoran';
                }
            }
        } else {
            $error = 'Silakan pilih file banner untuk diunggah';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
        .image-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            margin-bottom: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Edit Restaurant Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Edit Restoran</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit Restoran</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <!-- Restaurant Information Form -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Restoran</h5>
                    </div>
                    <div class="card-body p-4">
                        <form action="edit_restaurant.php?id=<?= $restaurant_id ?>" method="post">
                            <input type="hidden" name="action" value="update_info">

                            <div class="mb-3">
                                <label for="name" class="form-label">Nama Restoran <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($restaurant['name']) ?>" required>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars($restaurant['email']) ?>" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Nomor Telepon <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($restaurant['phone']) ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Alamat <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="address" name="address" rows="2" required><?= htmlspecialchars($restaurant['address']) ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Lokasi Restoran <span class="text-danger">*</span></label>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Anda dapat menggunakan GPS perangkat untuk menentukan lokasi restoran secara otomatis dengan mengklik tombol "Gunakan Lokasi Saat Ini" di bawah.
                                </div>
                                <div id="map"></div>
                                <div class="d-flex justify-content-between mb-3">
                                    <button type="button" id="get-location" class="btn btn-primary">
                                        <i class="fas fa-map-marker-alt me-2"></i>Gunakan Lokasi Saat Ini
                                    </button>
                                    <a href="location_settings.php" class="btn btn-outline-primary">
                                        <i class="fas fa-cog me-2"></i>Pengaturan Lokasi Lanjutan
                                    </a>
                                </div>
                                <div id="location-status" class="mb-3" style="display: none;"></div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="latitude" class="form-label">Latitude</label>
                                        <input type="text" class="form-control" id="latitude" name="latitude" value="<?= $restaurant['latitude'] ?>" readonly>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="longitude" class="form-label">Longitude</label>
                                        <input type="text" class="form-control" id="longitude" name="longitude" value="<?= $restaurant['longitude'] ?>" readonly>
                                    </div>
                                </div>
                                <small class="text-muted">Klik pada peta untuk menentukan lokasi secara manual atau gunakan tombol "Gunakan Lokasi Saat Ini" untuk menggunakan GPS perangkat Anda. Untuk pengaturan lokasi yang lebih detail, gunakan "Pengaturan Lokasi Lanjutan".</small>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Deskripsi Restoran</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($restaurant['description'] ?? '') ?></textarea>
                            </div>

                            <h5 class="mb-4 mt-5">Jam Operasional & Biaya</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="opening_time" class="form-label">Jam Buka</label>
                                    <input type="time" class="form-control" id="opening_time" name="opening_time" value="<?= $restaurant['opening_time'] ?>">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="closing_time" class="form-label">Jam Tutup</label>
                                    <input type="time" class="form-control" id="closing_time" name="closing_time" value="<?= $restaurant['closing_time'] ?>">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="min_order_amount" class="form-label">Minimal Pemesanan (Rp)</label>
                                    <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" value="<?= $restaurant['min_order_amount'] ?>" min="0" step="0.01">
                                    <small class="text-muted">Masukkan dalam satuan 0.01 (Rp15.000 = 1)</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="delivery_fee" class="form-label">Biaya Pengiriman (Rp)</label>
                                    <input type="number" class="form-control" id="delivery_fee" name="delivery_fee" value="<?= $restaurant['delivery_fee'] ?>" min="0" step="0.01">
                                    <small class="text-muted">Masukkan dalam satuan 0.01 (Rp15.000 = 1)</small>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_open" name="is_open" <?= $restaurant['is_open'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_open">Restoran Buka</label>
                                <small class="form-text text-muted d-block">Centang jika restoran Anda saat ini buka untuk menerima pesanan.</small>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <a href="dashboard.php" class="btn btn-outline-secondary me-md-2">Kembali</a>
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Restaurant Images -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Logo Restoran</h5>
                    </div>
                    <div class="card-body p-4">
                        <img src="<?= !empty($restaurant['logo_url']) ? '../' . $restaurant['logo_url'] : '../assets/restaurant-placeholder.png' ?>" alt="Logo Restoran" class="image-preview">

                        <form action="edit_restaurant.php?id=<?= $restaurant_id ?>" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_logo">

                            <div class="mb-3">
                                <label for="logo" class="form-label">Unggah Logo Baru</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/jpeg,image/png,image/gif" required>
                                <small class="form-text text-muted">Format: JPG, PNG, GIF. Maks: 2MB.</small>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Unggah Logo</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Banner Restoran</h5>
                    </div>
                    <div class="card-body p-4">
                        <img src="<?= !empty($restaurant['banner_url']) ? '../' . $restaurant['banner_url'] : '../assets/restaurant-placeholder.png' ?>" alt="Banner Restoran" class="image-preview">

                        <form action="edit_restaurant.php?id=<?= $restaurant_id ?>" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_banner">

                            <div class="mb-3">
                                <label for="banner" class="form-label">Unggah Banner Baru</label>
                                <input type="file" class="form-control" id="banner" name="banner" accept="image/jpeg,image/png,image/gif" required>
                                <small class="form-text text-muted">Format: JPG, PNG, GIF. Maks: 5MB. Ukuran yang disarankan: 1200x400px.</small>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Unggah Banner</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Menu Restoran</h5>
                    </div>
                    <div class="card-body p-4">
                        <p>Kelola menu restoran Anda untuk ditampilkan kepada pelanggan.</p>
                        <div class="d-grid">
                            <a href="manage_menu.php?id=<?= $restaurant_id ?>" class="btn btn-primary">
                                <i class="fas fa-utensils me-2"></i>Kelola Menu
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map with restaurant location
        var initialLat = <?= $restaurant['latitude'] ?: -6.2088 ?>;
        var initialLng = <?= $restaurant['longitude'] ?: 106.8456 ?>;

        var map = L.map('map').setView([initialLat, initialLng], 15);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        var marker = L.marker([initialLat, initialLng]).addTo(map);

        // Handle map click
        map.on('click', function(e) {
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker(e.latlng).addTo(map);
            document.getElementById('latitude').value = e.latlng.lat.toFixed(6);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(6);
        });

        // Get current location
        document.getElementById('get-location').addEventListener('click', function() {
            const locationStatus = document.getElementById('location-status');
            locationStatus.style.display = 'block';
            locationStatus.className = 'alert alert-info mb-3';
            locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Sedang mendapatkan lokasi Anda...';

            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        var lat = position.coords.latitude;
                        var lng = position.coords.longitude;

                        if (marker) {
                            map.removeLayer(marker);
                        }

                        marker = L.marker([lat, lng]).addTo(map);
                        map.setView([lat, lng], 15);

                        document.getElementById('latitude').value = lat.toFixed(6);
                        document.getElementById('longitude').value = lng.toFixed(6);

                        locationStatus.className = 'alert alert-success mb-3';
                        locationStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i> Lokasi berhasil ditemukan dan diterapkan!';

                        // Highlight the coordinates
                        const latInput = document.getElementById('latitude');
                        const lngInput = document.getElementById('longitude');
                        latInput.classList.add('is-valid');
                        lngInput.classList.add('is-valid');

                        setTimeout(function() {
                            latInput.classList.remove('is-valid');
                            lngInput.classList.remove('is-valid');
                        }, 3000);
                    },
                    function(error) {
                        locationStatus.className = 'alert alert-danger mb-3';
                        let errorMessage = 'Tidak dapat mengakses lokasi Anda: ';

                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage += 'Izin penggunaan GPS ditolak. Silakan aktifkan izin lokasi di browser Anda.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage += 'Informasi lokasi tidak tersedia. Pastikan GPS perangkat Anda aktif.';
                                break;
                            case error.TIMEOUT:
                                errorMessage += 'Waktu permintaan lokasi habis. Silakan coba lagi.';
                                break;
                            default:
                                errorMessage += error.message;
                        }

                        locationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> ' + errorMessage;
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    }
                );
            } else {
                locationStatus.className = 'alert alert-danger mb-3';
                locationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Geolocation tidak didukung oleh browser Anda. Silakan gunakan browser yang mendukung GPS.';
            }
        });

        // Preview uploaded images
        document.getElementById('logo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = this.parentElement.parentElement.previousElementSibling;
                    preview.src = e.target.result;
                }.bind(this);
                reader.readAsDataURL(file);
            }
        });

        document.getElementById('banner').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = this.parentElement.parentElement.previousElementSibling;
                    preview.src = e.target.result;
                }.bind(this);
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
