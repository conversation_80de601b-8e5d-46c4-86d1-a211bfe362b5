<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include required files
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if restaurant owner is logged in
if (!isRestaurantOwnerLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get owner information
$owner_id = $_SESSION['owner_id'];
$stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();
$owner = $stmt->fetch();

// Check if restaurant ID is provided
if (!isset($_GET['id'])) {
    header('Location: dashboard.php');
    exit;
}

$restaurant_id = $_GET['id'];

// Check if the restaurant belongs to the logged-in owner
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :restaurant_id AND owner_id = :owner_id");
$stmt->bindParam(':restaurant_id', $restaurant_id);
$stmt->bindParam(':owner_id', $owner_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    header('Location: dashboard.php');
    exit;
}

$restaurant = $stmt->fetch();
$error = '';
$success = '';

// Get categories
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();

// Get menu items for this restaurant
$stmt = $conn->prepare("
    SELECT m.*, c.name as category_name
    FROM menu_items m
    LEFT JOIN categories c ON m.category_id = c.category_id
    WHERE m.restaurant_id = :restaurant_id
    ORDER BY m.category_id, m.name
");
$stmt->bindParam(':restaurant_id', $restaurant_id);
$stmt->execute();
$menuItems = $stmt->fetchAll();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_item') {
        $name = $_POST['name'] ?? '';
        $category_id = !empty($_POST['category_id']) ? $_POST['category_id'] : null;
        $price = $_POST['price'] ?? 0;
        $description = $_POST['description'] ?? '';
        $is_available = isset($_POST['is_available']) ? 1 : 0;
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;

        // Validate inputs
        if (empty($name) || empty($price)) {
            $error = 'Silakan isi nama dan harga menu';
        } else {
            try {
                // Handle image upload
                $image_url = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    // Use the uploadImage helper function
                    $uploadResult = uploadImage($_FILES['image'], 'uploads/menu', 'menu_' . $restaurant_id);

                    if ($uploadResult['success']) {
                        $image_url = $uploadResult['file_path'];
                    } else {
                        $error = $uploadResult['message'];
                        // Add debug information
                        if (isset($uploadResult['upload_path']) && isset($uploadResult['tmp_name'])) {
                            error_log("Upload failed. Path: " . $uploadResult['upload_path'] . ", Temp: " . $uploadResult['tmp_name']);
                        }
                    }
                }

                if (empty($error)) {
                    // Insert menu item
                    $stmt = $conn->prepare("
                        INSERT INTO menu_items (
                            restaurant_id, category_id, name, description,
                            price, image_url, is_available, is_featured
                        ) VALUES (
                            :restaurant_id, :category_id, :name, :description,
                            :price, :image_url, :is_available, :is_featured
                        )
                    ");

                    $stmt->bindParam(':restaurant_id', $restaurant_id);
                    $stmt->bindParam(':category_id', $category_id);
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':description', $description);
                    $stmt->bindParam(':price', $price);
                    $stmt->bindParam(':image_url', $image_url);
                    $stmt->bindParam(':is_available', $is_available);
                    $stmt->bindParam(':is_featured', $is_featured);

                    if ($stmt->execute()) {
                        $success = 'Menu berhasil ditambahkan!';

                        // Refresh menu items
                        $stmt = $conn->prepare("
                            SELECT m.*, c.name as category_name
                            FROM menu_items m
                            LEFT JOIN categories c ON m.category_id = c.category_id
                            WHERE m.restaurant_id = :restaurant_id
                            ORDER BY m.category_id, m.name
                        ");
                        $stmt->bindParam(':restaurant_id', $restaurant_id);
                        $stmt->execute();
                        $menuItems = $stmt->fetchAll();
                    } else {
                        $error = 'Gagal menambahkan menu';
                    }
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'edit_item') {
        $item_id = $_POST['item_id'] ?? 0;
        $name = $_POST['name'] ?? '';
        $category_id = !empty($_POST['category_id']) ? $_POST['category_id'] : null;
        $price = $_POST['price'] ?? 0;
        $description = $_POST['description'] ?? '';
        $is_available = isset($_POST['is_available']) ? 1 : 0;
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;

        // Validate inputs
        if (empty($name) || empty($price) || empty($item_id)) {
            $error = 'Silakan isi nama dan harga menu';
        } else {
            try {
                // Check if item belongs to this restaurant
                $stmt = $conn->prepare("
                    SELECT * FROM menu_items
                    WHERE item_id = :item_id AND restaurant_id = :restaurant_id
                ");
                $stmt->bindParam(':item_id', $item_id);
                $stmt->bindParam(':restaurant_id', $restaurant_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $error = 'Menu tidak ditemukan';
                } else {
                    $currentItem = $stmt->fetch();

                    // Handle image upload
                    $image_url = $currentItem['image_url'];
                    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                        // Use the uploadImage helper function
                        $uploadResult = uploadImage($_FILES['image'], 'uploads/menu', 'menu_' . $restaurant_id);

                        if ($uploadResult['success']) {
                            $image_url = $uploadResult['file_path'];
                        } else {
                            $error = $uploadResult['message'];
                            // Add debug information
                            if (isset($uploadResult['upload_path']) && isset($uploadResult['tmp_name'])) {
                                error_log("Upload failed. Path: " . $uploadResult['upload_path'] . ", Temp: " . $uploadResult['tmp_name']);
                            }
                        }
                    }

                    if (empty($error)) {
                        // Update menu item
                        $stmt = $conn->prepare("
                            UPDATE menu_items SET
                                category_id = :category_id,
                                name = :name,
                                description = :description,
                                price = :price,
                                image_url = :image_url,
                                is_available = :is_available,
                                is_featured = :is_featured
                            WHERE item_id = :item_id AND restaurant_id = :restaurant_id
                        ");

                        $stmt->bindParam(':category_id', $category_id);
                        $stmt->bindParam(':name', $name);
                        $stmt->bindParam(':description', $description);
                        $stmt->bindParam(':price', $price);
                        $stmt->bindParam(':image_url', $image_url);
                        $stmt->bindParam(':is_available', $is_available);
                        $stmt->bindParam(':is_featured', $is_featured);
                        $stmt->bindParam(':item_id', $item_id);
                        $stmt->bindParam(':restaurant_id', $restaurant_id);

                        if ($stmt->execute()) {
                            $success = 'Menu berhasil diperbarui!';

                            // Refresh menu items
                            $stmt = $conn->prepare("
                                SELECT m.*, c.name as category_name
                                FROM menu_items m
                                LEFT JOIN categories c ON m.category_id = c.category_id
                                WHERE m.restaurant_id = :restaurant_id
                                ORDER BY m.category_id, m.name
                            ");
                            $stmt->bindParam(':restaurant_id', $restaurant_id);
                            $stmt->execute();
                            $menuItems = $stmt->fetchAll();
                        } else {
                            $error = 'Gagal memperbarui menu';
                        }
                    }
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'delete_item') {
        $item_id = $_POST['item_id'] ?? 0;

        if (empty($item_id)) {
            $error = 'ID menu tidak valid';
        } else {
            try {
                // Check if item belongs to this restaurant
                $stmt = $conn->prepare("
                    SELECT * FROM menu_items
                    WHERE item_id = :item_id AND restaurant_id = :restaurant_id
                ");
                $stmt->bindParam(':item_id', $item_id);
                $stmt->bindParam(':restaurant_id', $restaurant_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $error = 'Menu tidak ditemukan';
                } else {
                    // Delete menu item
                    $stmt = $conn->prepare("
                        DELETE FROM menu_items
                        WHERE item_id = :item_id AND restaurant_id = :restaurant_id
                    ");
                    $stmt->bindParam(':item_id', $item_id);
                    $stmt->bindParam(':restaurant_id', $restaurant_id);

                    if ($stmt->execute()) {
                        $success = 'Menu berhasil dihapus!';

                        // Refresh menu items
                        $stmt = $conn->prepare("
                            SELECT m.*, c.name as category_name
                            FROM menu_items m
                            LEFT JOIN categories c ON m.category_id = c.category_id
                            WHERE m.restaurant_id = :restaurant_id
                            ORDER BY m.category_id, m.name
                        ");
                        $stmt->bindParam(':restaurant_id', $restaurant_id);
                        $stmt->execute();
                        $menuItems = $stmt->fetchAll();
                    } else {
                        $error = 'Gagal menghapus menu';
                    }
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Menu - <?= $restaurant['name'] ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .menu-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        .menu-item-row:hover {
            background-color: rgba(0,0,0,0.03);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dasbor Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Manage Menu Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Kelola Menu</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item"><a href="edit_restaurant.php?id=<?= $restaurant_id ?>">Edit Restoran</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Kelola Menu</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMenuModal">
                    <i class="fas fa-plus me-2"></i>Tambah Menu Baru
                </button>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Menu Restoran <?= $restaurant['name'] ?></h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($menuItems)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                                <h4>Belum ada menu</h4>
                                <p class="text-muted">Klik tombol "Tambah Menu Baru" untuk mulai menambahkan menu restoran Anda.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Gambar</th>
                                            <th>Nama</th>
                                            <th>Kategori</th>
                                            <th>Harga</th>
                                            <th>Status</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($menuItems as $item): ?>
                                            <tr class="menu-item-row">
                                                <td>
                                                    <img src="<?= !empty($item['image_url']) ? '../' . $item['image_url'] : '../assets/restaurant-placeholder.png' ?>" alt="<?= $item['name'] ?>" class="menu-image">
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($item['name']) ?></strong>
                                                    <?php if (!empty($item['description'])): ?>
                                                        <p class="small text-muted mb-0"><?= htmlspecialchars(substr($item['description'], 0, 50)) ?><?= strlen($item['description']) > 50 ? '...' : '' ?></p>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= htmlspecialchars($item['category_name'] ?? 'Tidak Berkategori') ?></td>
                                                <td><?= formatCurrency($item['price']) ?></td>
                                                <td>
                                                    <?php if ($item['is_available']): ?>
                                                        <span class="badge bg-success">Tersedia</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Tidak Tersedia</span>
                                                    <?php endif; ?>

                                                    <?php if ($item['is_featured']): ?>
                                                        <span class="badge bg-warning text-dark">Unggulan</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-menu-btn"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#editMenuModal"
                                                                data-id="<?= $item['item_id'] ?>"
                                                                data-name="<?= htmlspecialchars($item['name']) ?>"
                                                                data-category="<?= $item['category_id'] ?? '' ?>"
                                                                data-price="<?= $item['price'] ?>"
                                                                data-description="<?= htmlspecialchars($item['description'] ?? '') ?>"
                                                                data-available="<?= $item['is_available'] ?>"
                                                                data-featured="<?= $item['is_featured'] ?>"
                                                                data-image="<?= $item['image_url'] ?? '' ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-menu-btn"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#deleteMenuModal"
                                                                data-id="<?= $item['item_id'] ?>"
                                                                data-name="<?= htmlspecialchars($item['name']) ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Menu Modal -->
    <div class="modal fade" id="addMenuModal" tabindex="-1" aria-labelledby="addMenuModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form action="manage_menu.php?id=<?= $restaurant_id ?>" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_item">

                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="addMenuModalLabel">Tambah Menu Baru</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Menu <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>

                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Kategori</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">-- Pilih Kategori --</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['category_id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="price" class="form-label">Harga (Rp) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="0" required>
                                    <small class="text-muted">Masukkan harga dalam Rupiah (contoh: 15000 untuk Rp15.000)</small>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Deskripsi</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="image" class="form-label">Gambar Menu</label>
                                    <input type="file" class="form-control" id="image" name="image" accept="image/jpeg,image/png,image/gif">
                                    <small class="text-muted">Format: JPG, PNG, GIF. Maks: 5MB.</small>
                                    <div class="mt-2">
                                        <img id="image-preview" src="../assets/restaurant-placeholder.png" alt="Preview" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_available" name="is_available" checked>
                                    <label class="form-check-label" for="is_available">Menu Tersedia</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured">
                                    <label class="form-check-label" for="is_featured">Jadikan Menu Unggulan</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan Menu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Menu Modal -->
    <div class="modal fade" id="editMenuModal" tabindex="-1" aria-labelledby="editMenuModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form action="manage_menu.php?id=<?= $restaurant_id ?>" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="edit_item">
                    <input type="hidden" name="item_id" id="edit_item_id">

                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="editMenuModalLabel">Edit Menu</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_name" class="form-label">Nama Menu <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_name" name="name" required>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_category_id" class="form-label">Kategori</label>
                                    <select class="form-select" id="edit_category_id" name="category_id">
                                        <option value="">-- Pilih Kategori --</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['category_id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_price" class="form-label">Harga (Rp) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="edit_price" name="price" min="0" required>
                                    <small class="text-muted">Masukkan harga dalam Rupiah (contoh: 15000 untuk Rp15.000)</small>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_description" class="form-label">Deskripsi</label>
                                    <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_image" class="form-label">Gambar Menu</label>
                                    <input type="file" class="form-control" id="edit_image" name="image" accept="image/jpeg,image/png,image/gif">
                                    <small class="text-muted">Format: JPG, PNG, GIF. Maks: 5MB. Biarkan kosong jika tidak ingin mengubah gambar.</small>
                                    <div class="mt-2">
                                        <img id="edit_image_preview" src="../assets/restaurant-placeholder.png" alt="Preview" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="edit_is_available" name="is_available">
                                    <label class="form-check-label" for="edit_is_available">Menu Tersedia</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="edit_is_featured" name="is_featured">
                                    <label class="form-check-label" for="edit_is_featured">Jadikan Menu Unggulan</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Menu Modal -->
    <div class="modal fade" id="deleteMenuModal" tabindex="-1" aria-labelledby="deleteMenuModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="manage_menu.php?id=<?= $restaurant_id ?>" method="post">
                    <input type="hidden" name="action" value="delete_item">
                    <input type="hidden" name="item_id" id="delete_item_id">

                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteMenuModalLabel">Hapus Menu</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Apakah Anda yakin ingin menghapus menu <strong id="delete_item_name"></strong>?</p>
                        <p class="text-danger">Tindakan ini tidak dapat dibatalkan.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-danger">Hapus Menu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Image preview for add menu
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('image-preview').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Image preview for edit menu
        document.getElementById('edit_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('edit_image_preview').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Edit menu modal
        document.querySelectorAll('.edit-menu-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const category = this.getAttribute('data-category');
                const price = this.getAttribute('data-price');
                const description = this.getAttribute('data-description');
                const available = this.getAttribute('data-available') === '1';
                const featured = this.getAttribute('data-featured') === '1';
                const image = this.getAttribute('data-image');

                document.getElementById('edit_item_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_category_id').value = category;
                document.getElementById('edit_price').value = price;
                document.getElementById('edit_description').value = description;
                document.getElementById('edit_is_available').checked = available;
                document.getElementById('edit_is_featured').checked = featured;

                if (image) {
                    document.getElementById('edit_image_preview').src = '../' + image;
                } else {
                    document.getElementById('edit_image_preview').src = '../assets/restaurant-placeholder.png';
                }
            });
        });

        // Delete menu modal
        document.querySelectorAll('.delete-menu-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');

                document.getElementById('delete_item_id').value = id;
                document.getElementById('delete_item_name').textContent = name;
            });
        });
    </script>
</body>
</html>
