<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'config/database.php';

try {
    $conn = connectDB();
    echo "Koneksi database berhasil!<br><br>";
    
    // Daftar tabel yang akan diperiksa
    $tables = [
        'restaurants',
        'restaurant_owners',
        'categories',
        'menu_items',
        'menu_categories',
        'orders',
        'users',
        'drivers',
        'admins'
    ];
    
    foreach ($tables as $table) {
        echo "<h3>Tabel: $table</h3>";
        
        // Cek apakah tabel ada
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() === 0) {
            echo "Tabel $table tidak ditemukan!<br><br>";
            continue;
        }
        
        // Tampilkan struktur tabel
        $stmt = $conn->query("DESCRIBE $table");
        echo "<h4>Struktur Tabel:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Tampilkan jumlah data
        $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Jumlah data: " . $result['count'] . "</p>";
        
        // Tampilkan beberapa data
        $stmt = $conn->query("SELECT * FROM $table LIMIT 3");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            echo "<h4>Contoh Data:</h4>";
            echo "<table border='1' cellpadding='5'>";
            
            // Header tabel
            echo "<tr>";
            foreach (array_keys($rows[0]) as $key) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            
            // Data
            foreach ($rows as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . (is_null($value) ? "NULL" : $value) . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>Tidak ada data dalam tabel.</p>";
        }
        
        echo "<hr>";
    }
    
} catch (PDOException $e) {
    echo "Error database: " . $e->getMessage();
}
?>
