<?php
/**
 * API endpoint for menu items
 */

header('Content-Type: application/json');

// Allow from any origin
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database connection
require_once __DIR__ . '/../config/database.php';
$conn = connectDB();

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get menu items
        if (isset($_GET['id'])) {
            // Get specific menu item
            $stmt = $conn->prepare("SELECT * FROM menu_items WHERE item_id = :id");
            $stmt->bindParam(':id', $_GET['id']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $item = $stmt->fetch();
                echo json_encode(['success' => true, 'data' => $item]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Menu item not found']);
            }
        } elseif (isset($_GET['restaurant_id'])) {
            // Get menu items for a specific restaurant
            $query = "SELECT * FROM menu_items WHERE restaurant_id = :restaurant_id";
            $params = [':restaurant_id' => $_GET['restaurant_id']];
            
            // Filter by category
            if (isset($_GET['category_id'])) {
                $query .= " AND category_id = :category_id";
                $params[':category_id'] = $_GET['category_id'];
            }
            
            // Filter by availability
            if (isset($_GET['is_available'])) {
                $query .= " AND is_available = :is_available";
                $params[':is_available'] = $_GET['is_available'];
            }
            
            // Add sorting
            $query .= " ORDER BY name ASC";
            
            $stmt = $conn->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            
            $items = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $items]);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Restaurant ID is required']);
        }
        break;
        
    case 'POST':
        // Create new menu item (restaurant owner/admin only)
        // In a real app, you would check authentication here
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Check required fields
        if (!isset($data['restaurant_id']) || !isset($data['name']) || !isset($data['price'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }
        
        // Insert menu item
        $stmt = $conn->prepare("
            INSERT INTO menu_items (restaurant_id, category_id, name, description, 
                                   price, image_url, is_available, is_featured)
            VALUES (:restaurant_id, :category_id, :name, :description, 
                   :price, :image_url, :is_available, :is_featured)
        ");
        
        $stmt->bindParam(':restaurant_id', $data['restaurant_id']);
        $stmt->bindParam(':category_id', $data['category_id'] ?? null);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description'] ?? null);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':image_url', $data['image_url'] ?? null);
        $stmt->bindParam(':is_available', $data['is_available'] ?? true);
        $stmt->bindParam(':is_featured', $data['is_featured'] ?? false);
        
        if ($stmt->execute()) {
            $item_id = $conn->lastInsertId();
            http_response_code(201);
            echo json_encode(['success' => true, 'message' => 'Menu item created successfully', 'item_id' => $item_id]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create menu item']);
        }
        break;
        
    case 'PUT':
        // Update menu item (restaurant owner/admin only)
        // In a real app, you would check authentication here
        
        // Get item ID from URL
        $url_components = parse_url($_SERVER['REQUEST_URI']);
        parse_str($url_components['query'] ?? '', $params);
        
        if (!isset($params['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Menu item ID is required']);
            exit;
        }
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Build update query
        $query = "UPDATE menu_items SET ";
        $updateParams = [];
        $allowedFields = [
            'category_id', 'name', 'description', 'price', 
            'image_url', 'is_available', 'is_featured'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $query .= "$field = :$field, ";
                $updateParams[":$field"] = $data[$field];
            }
        }
        
        // Remove trailing comma and space
        $query = rtrim($query, ', ');
        
        // Add WHERE clause
        $query .= " WHERE item_id = :id";
        $updateParams[':id'] = $params['id'];
        
        // Execute update
        $stmt = $conn->prepare($query);
        foreach ($updateParams as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Menu item updated successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update menu item']);
        }
        break;
        
    case 'DELETE':
        // Delete menu item (restaurant owner/admin only)
        // In a real app, you would check authentication here
        
        // Get item ID from URL
        $url_components = parse_url($_SERVER['REQUEST_URI']);
        parse_str($url_components['query'] ?? '', $params);
        
        if (!isset($params['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Menu item ID is required']);
            exit;
        }
        
        // Delete menu item
        $stmt = $conn->prepare("DELETE FROM menu_items WHERE item_id = :id");
        $stmt->bindParam(':id', $params['id']);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Menu item deleted successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete menu item']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
