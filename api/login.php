<?php
/**
 * API endpoint for user login
 */

header('Content-Type: application/json');

// Allow from any origin
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$data = json_decode(file_get_contents('php://input'), true);

// Check if all required fields are present
if (!isset($data['email']) || !isset($data['password']) || !isset($data['user_type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

// Include auth functions
require_once __DIR__ . '/../includes/auth.php';

// Login based on user type
$result = ['success' => false, 'message' => 'Invalid user type'];

switch ($data['user_type']) {
    case 'customer':
        $result = loginUser($data['email'], $data['password']);
        break;
    case 'driver':
        $result = loginDriver($data['email'], $data['password']);
        break;
    case 'admin':
        $result = loginAdmin($data['email'], $data['password']);
        break;
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid user type']);
        exit;
}

if ($result['success']) {
    http_response_code(200);
    // Remove password from response
    if (isset($result['user'])) {
        unset($result['user']['password']);
    } elseif (isset($result['driver'])) {
        unset($result['driver']['password']);
    } elseif (isset($result['admin'])) {
        unset($result['admin']['password']);
    }
    echo json_encode(['success' => true, 'message' => 'Login successful', 'data' => $result]);
} else {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => $result['message']]);
}
