<?php
/**
 * API endpoint for restaurants
 */

header('Content-Type: application/json');

// Allow from any origin
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database connection
require_once __DIR__ . '/../config/database.php';
$conn = connectDB();

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get restaurants
        if (isset($_GET['id'])) {
            // Get specific restaurant
            $stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :id");
            $stmt->bindParam(':id', $_GET['id']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $restaurant = $stmt->fetch();
                
                // Get restaurant categories
                $stmt = $conn->prepare("
                    SELECT DISTINCT c.* 
                    FROM categories c
                    JOIN menu_items m ON c.category_id = m.category_id
                    WHERE m.restaurant_id = :restaurant_id
                ");
                $stmt->bindParam(':restaurant_id', $_GET['id']);
                $stmt->execute();
                $categories = $stmt->fetchAll();
                
                $restaurant['categories'] = $categories;
                
                echo json_encode(['success' => true, 'data' => $restaurant]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Restaurant not found']);
            }
        } else {
            // Get all restaurants with optional filtering
            $query = "SELECT * FROM restaurants WHERE 1=1";
            $params = [];
            
            // Filter by name
            if (isset($_GET['name'])) {
                $query .= " AND name LIKE :name";
                $params[':name'] = '%' . $_GET['name'] . '%';
            }
            
            // Filter by is_open
            if (isset($_GET['is_open'])) {
                $query .= " AND is_open = :is_open";
                $params[':is_open'] = $_GET['is_open'];
            }
            
            // Add sorting
            $query .= " ORDER BY name ASC";
            
            $stmt = $conn->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            
            $restaurants = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $restaurants]);
        }
        break;
        
    case 'POST':
        // Create new restaurant (admin only)
        // In a real app, you would check admin authentication here
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Check required fields
        if (!isset($data['name']) || !isset($data['email']) || !isset($data['phone']) || 
            !isset($data['address']) || !isset($data['latitude']) || !isset($data['longitude'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }
        
        // Insert restaurant
        $stmt = $conn->prepare("
            INSERT INTO restaurants (name, email, phone, address, latitude, longitude, 
                                    logo_url, banner_url, description, opening_time, 
                                    closing_time, min_order_amount, delivery_fee)
            VALUES (:name, :email, :phone, :address, :latitude, :longitude, 
                    :logo_url, :banner_url, :description, :opening_time, 
                    :closing_time, :min_order_amount, :delivery_fee)
        ");
        
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':latitude', $data['latitude']);
        $stmt->bindParam(':longitude', $data['longitude']);
        $stmt->bindParam(':logo_url', $data['logo_url'] ?? null);
        $stmt->bindParam(':banner_url', $data['banner_url'] ?? null);
        $stmt->bindParam(':description', $data['description'] ?? null);
        $stmt->bindParam(':opening_time', $data['opening_time'] ?? null);
        $stmt->bindParam(':closing_time', $data['closing_time'] ?? null);
        $stmt->bindParam(':min_order_amount', $data['min_order_amount'] ?? 0);
        $stmt->bindParam(':delivery_fee', $data['delivery_fee'] ?? 0);
        
        if ($stmt->execute()) {
            $restaurant_id = $conn->lastInsertId();
            http_response_code(201);
            echo json_encode(['success' => true, 'message' => 'Restaurant created successfully', 'restaurant_id' => $restaurant_id]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create restaurant']);
        }
        break;
        
    case 'PUT':
        // Update restaurant (admin only)
        // In a real app, you would check admin authentication here
        
        // Get restaurant ID from URL
        $url_components = parse_url($_SERVER['REQUEST_URI']);
        parse_str($url_components['query'] ?? '', $params);
        
        if (!isset($params['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Restaurant ID is required']);
            exit;
        }
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Build update query
        $query = "UPDATE restaurants SET ";
        $updateParams = [];
        $allowedFields = [
            'name', 'email', 'phone', 'address', 'latitude', 'longitude', 
            'logo_url', 'banner_url', 'description', 'opening_time', 
            'closing_time', 'is_open', 'min_order_amount', 'delivery_fee'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $query .= "$field = :$field, ";
                $updateParams[":$field"] = $data[$field];
            }
        }
        
        // Remove trailing comma and space
        $query = rtrim($query, ', ');
        
        // Add WHERE clause
        $query .= " WHERE restaurant_id = :id";
        $updateParams[':id'] = $params['id'];
        
        // Execute update
        $stmt = $conn->prepare($query);
        foreach ($updateParams as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Restaurant updated successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update restaurant']);
        }
        break;
        
    case 'DELETE':
        // Delete restaurant (admin only)
        // In a real app, you would check admin authentication here
        
        // Get restaurant ID from URL
        $url_components = parse_url($_SERVER['REQUEST_URI']);
        parse_str($url_components['query'] ?? '', $params);
        
        if (!isset($params['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Restaurant ID is required']);
            exit;
        }
        
        // Delete restaurant
        $stmt = $conn->prepare("DELETE FROM restaurants WHERE restaurant_id = :id");
        $stmt->bindParam(':id', $params['id']);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Restaurant deleted successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete restaurant']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
