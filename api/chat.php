<?php
/**
 * Chat API for KikaZen Ship
 * Handles chat operations: creating rooms, sending messages, retrieving messages, etc.
 */

// Set headers for JSON response
header('Content-Type: application/json');

// Start session
session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Function to log errors
function logError($message, $details = []) {
    $logFile = '../logs/chat_api_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";

    if (!empty($details)) {
        $logMessage .= "Details: " . json_encode($details, JSON_PRETTY_PRINT) . "\n";
    }

    $logMessage .= "--------------------\n";

    // Create logs directory if it doesn't exist
    if (!is_dir('../logs')) {
        mkdir('../logs', 0755, true);
    }

    // Append to log file
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Connect to database
$conn = connectDB();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Initialize response
$response = [
    'success' => false,
    'message' => 'Invalid request',
    'data' => null
];

// Check if user is logged in
if (!isUserLoggedIn() && !isDriverLoggedIn() && !isRestaurantOwnerLoggedIn() && !isAdminLoggedIn()) {
    $response['message'] = 'Unauthorized';
    echo json_encode($response);
    exit;
}

// Determine user type and ID
$user_type = '';
$user_id = 0;

if (isUserLoggedIn()) {
    $user_type = 'customer';
    $user_id = $_SESSION['user_id'];
} elseif (isDriverLoggedIn()) {
    $user_type = 'driver';
    $user_id = $_SESSION['driver_id'];
} elseif (isRestaurantOwnerLoggedIn()) {
    $user_type = 'restaurant_owner';
    $user_id = $_SESSION['owner_id'];
} elseif (isAdminLoggedIn()) {
    $user_type = 'admin';
    $user_id = $_SESSION['admin_id'];
}

// Process request based on method and action
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    // GET requests
    if ($method === 'GET') {
        switch ($action) {
            case 'get_rooms':
                // Get chat rooms for the current user
                $stmt = $conn->prepare("
                    SELECT cr.*,
                           o.order_id,
                           o.total_amount,
                           r.name AS restaurant_name,
                           u.name AS customer_name,
                           d.name AS driver_name,
                           (SELECT COUNT(*) FROM chat_messages cm
                            JOIN chat_participants cp ON cm.room_id = cp.room_id
                            WHERE cm.room_id = cr.room_id
                            AND cp.user_type = :user_type
                            AND cp.user_id = :user_id
                            AND cm.created_at > cp.last_read_at
                            AND (cm.sender_type != :user_type OR cm.sender_id != :user_id)) AS unread_count,
                           (SELECT cm.message_text FROM chat_messages cm
                            WHERE cm.room_id = cr.room_id
                            ORDER BY cm.created_at DESC LIMIT 1) AS last_message,
                           (SELECT cm.created_at FROM chat_messages cm
                            WHERE cm.room_id = cr.room_id
                            ORDER BY cm.created_at DESC LIMIT 1) AS last_message_time
                    FROM chat_rooms cr
                    JOIN chat_participants cp ON cr.room_id = cp.room_id
                    LEFT JOIN orders o ON cr.order_id = o.order_id
                    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                    LEFT JOIN users u ON o.user_id = u.user_id
                    LEFT JOIN drivers d ON o.driver_id = d.driver_id
                    WHERE cp.user_type = :user_type AND cp.user_id = :user_id
                    ORDER BY cr.updated_at DESC
                ");
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                $rooms = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Format the data
                foreach ($rooms as &$room) {
                    $room['total_amount'] = $room['total_amount'] ? $room['total_amount'] * 15000 : 0;
                    $room['last_message_time'] = $room['last_message_time'] ? date('Y-m-d H:i:s', strtotime($room['last_message_time'])) : null;
                }

                $response['success'] = true;
                $response['message'] = 'Chat rooms retrieved successfully';
                $response['data'] = $rooms;
                break;

            case 'get_messages':
                // Get messages for a specific room
                $room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;

                if (!$room_id) {
                    $response['message'] = 'Room ID is required';
                    break;
                }

                // Check if user is a participant in this room
                $stmt = $conn->prepare("
                    SELECT * FROM chat_participants
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $response['message'] = 'You are not a participant in this chat room';
                    break;
                }

                // Get messages
                $stmt = $conn->prepare("
                    SELECT cm.*,
                           u.name AS customer_name,
                           ro.name AS restaurant_owner_name,
                           d.name AS driver_name,
                           a.name AS admin_name
                    FROM chat_messages cm
                    LEFT JOIN users u ON cm.sender_type = 'customer' AND cm.sender_id = u.user_id
                    LEFT JOIN restaurant_owners ro ON cm.sender_type = 'restaurant_owner' AND cm.sender_id = ro.owner_id
                    LEFT JOIN drivers d ON cm.sender_type = 'driver' AND cm.sender_id = d.driver_id
                    LEFT JOIN admins a ON cm.sender_type = 'admin' AND cm.sender_id = a.admin_id
                    WHERE cm.room_id = :room_id
                    ORDER BY cm.created_at ASC
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->execute();

                $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Update last read timestamp
                $stmt = $conn->prepare("
                    UPDATE chat_participants
                    SET last_read_at = NOW()
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                // Mark messages as read
                $stmt = $conn->prepare("
                    UPDATE chat_messages
                    SET is_read = 1
                    WHERE room_id = :room_id AND sender_type != :user_type AND sender_id != :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                // Get room details
                $stmt = $conn->prepare("
                    SELECT cr.*,
                           o.order_id,
                           o.total_amount,
                           r.name AS restaurant_name,
                           u.name AS customer_name,
                           d.name AS driver_name
                    FROM chat_rooms cr
                    LEFT JOIN orders o ON cr.order_id = o.order_id
                    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                    LEFT JOIN users u ON o.user_id = u.user_id
                    LEFT JOIN drivers d ON o.driver_id = d.driver_id
                    WHERE cr.room_id = :room_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->execute();

                $room = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($room) {
                    $room['total_amount'] = $room['total_amount'] ? $room['total_amount'] * 15000 : 0;
                }

                $response['success'] = true;
                $response['message'] = 'Messages retrieved successfully';
                $response['data'] = [
                    'messages' => $messages,
                    'room' => $room
                ];
                break;

            case 'get_new_messages':
                // Get new messages for a specific room since a timestamp
                $room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;
                $since = isset($_GET['since']) ? $_GET['since'] : null;

                if (!$room_id) {
                    $response['message'] = 'Room ID is required';
                    break;
                }

                // Check if user is a participant in this room
                $stmt = $conn->prepare("
                    SELECT * FROM chat_participants
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $response['message'] = 'You are not a participant in this chat room';
                    break;
                }

                // Get new messages
                $query = "
                    SELECT cm.*,
                           u.name AS customer_name,
                           ro.name AS restaurant_owner_name,
                           d.name AS driver_name,
                           a.name AS admin_name
                    FROM chat_messages cm
                    LEFT JOIN users u ON cm.sender_type = 'customer' AND cm.sender_id = u.user_id
                    LEFT JOIN restaurant_owners ro ON cm.sender_type = 'restaurant_owner' AND cm.sender_id = ro.owner_id
                    LEFT JOIN drivers d ON cm.sender_type = 'driver' AND cm.sender_id = d.driver_id
                    LEFT JOIN admins a ON cm.sender_type = 'admin' AND cm.sender_id = a.admin_id
                    WHERE cm.room_id = :room_id
                ";

                if ($since) {
                    $query .= " AND cm.created_at > :since";
                }

                $query .= " ORDER BY cm.created_at ASC";

                $stmt = $conn->prepare($query);
                $stmt->bindParam(':room_id', $room_id);

                if ($since) {
                    $stmt->bindParam(':since', $since);
                }

                $stmt->execute();

                $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Mark messages as read
                $stmt = $conn->prepare("
                    UPDATE chat_messages
                    SET is_read = 1
                    WHERE room_id = :room_id AND sender_type != :user_type AND sender_id != :user_id
                    AND is_read = 0
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                // Update last read timestamp
                $stmt = $conn->prepare("
                    UPDATE chat_participants
                    SET last_read_at = NOW()
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                $response['success'] = true;
                $response['message'] = 'New messages retrieved successfully';
                $response['data'] = [
                    'messages' => $messages
                ];
                break;

            case 'mark_as_read':
                // Mark all messages in a room as read
                $room_id = isset($_GET['room_id']) ? intval($_GET['room_id']) : 0;

                if (!$room_id) {
                    $response['message'] = 'Room ID is required';
                    break;
                }

                // Check if user is a participant in this room
                $stmt = $conn->prepare("
                    SELECT * FROM chat_participants
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $response['message'] = 'You are not a participant in this chat room';
                    break;
                }

                // Mark messages as read
                $stmt = $conn->prepare("
                    UPDATE chat_messages
                    SET is_read = 1
                    WHERE room_id = :room_id AND sender_type != :user_type AND sender_id != :user_id
                    AND is_read = 0
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                // Update last read timestamp
                $stmt = $conn->prepare("
                    UPDATE chat_participants
                    SET last_read_at = NOW()
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                $response['success'] = true;
                $response['message'] = 'Messages marked as read successfully';
                break;

            case 'get_unread_counts':
                // Get unread message counts for all rooms
                $stmt = $conn->prepare("
                    SELECT cr.room_id,
                           (SELECT COUNT(*) FROM chat_messages cm
                            WHERE cm.room_id = cr.room_id
                            AND cm.is_read = 0
                            AND cm.sender_type != :user_type
                            AND cm.sender_id != :user_id) AS unread_count
                    FROM chat_rooms cr
                    JOIN chat_participants cp ON cr.room_id = cp.room_id
                    WHERE cp.user_type = :user_type AND cp.user_id = :user_id
                ");
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                $unreadCounts = [];
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($results as $result) {
                    $unreadCounts[$result['room_id']] = intval($result['unread_count']);
                }

                $response['success'] = true;
                $response['message'] = 'Unread counts retrieved successfully';
                $response['data'] = $unreadCounts;
                break;

            default:
                $response['message'] = 'Invalid action';
                break;
        }
    }
    // POST requests
    elseif ($method === 'POST') {
        switch ($action) {
            case 'create_room':
                // Create a new chat room
                $room_type = isset($data['room_type']) ? $data['room_type'] : 'order';
                $message = isset($data['message']) ? trim($data['message']) : '';
                $participants = isset($data['participants']) ? $data['participants'] : [];

                // Set order_id to NULL for support and complaint chats
                if ($room_type === 'support' || $room_type === 'complaint') {
                    $order_id = null;

                    // Log for debugging
                    logError("Creating {$room_type} chat room with NULL order_id", [
                        'user_type' => $user_type,
                        'user_id' => $user_id,
                        'room_type' => $room_type,
                        'message' => $message
                    ]);
                } else {
                    $order_id = isset($data['order_id']) ? intval($data['order_id']) : 0;

                    // Log for debugging
                    logError("Creating {$room_type} chat room with order_id: {$order_id}", [
                        'user_type' => $user_type,
                        'user_id' => $user_id,
                        'room_type' => $room_type,
                        'order_id' => $order_id,
                        'message' => $message
                    ]);

                    if (!$order_id && $room_type === 'order') {
                        $response['message'] = 'Order ID is required for order chat rooms';
                        break;
                    }
                }

                // Begin transaction
                $conn->beginTransaction();

                try {
                    // Create room
                    $stmt = $conn->prepare("
                        INSERT INTO chat_rooms (order_id, room_type, status)
                        VALUES (:order_id, :room_type, 'active')
                    ");
                    $stmt->bindParam(':order_id', $order_id);
                    $stmt->bindParam(':room_type', $room_type);
                    $stmt->execute();

                    $room_id = $conn->lastInsertId();

                    // Add current user as participant
                    $stmt = $conn->prepare("
                        INSERT INTO chat_participants (room_id, user_type, user_id)
                        VALUES (:room_id, :user_type, :user_id)
                    ");
                    $stmt->bindParam(':room_id', $room_id);
                    $stmt->bindParam(':user_type', $user_type);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();

                    // Add appropriate participants based on room type
                    if ($room_type === 'order' && $order_id > 0) {
                        // Get order details to add restaurant owner and driver
                        $stmt = $conn->prepare("
                            SELECT o.restaurant_id, r.owner_id, o.driver_id
                            FROM orders o
                            JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                            WHERE o.order_id = :order_id
                        ");
                        $stmt->bindParam(':order_id', $order_id);
                        $stmt->execute();
                        $orderDetails = $stmt->fetch(PDO::FETCH_ASSOC);

                        if ($orderDetails) {
                            // Add restaurant owner
                            if ($orderDetails['owner_id']) {
                                $stmt = $conn->prepare("
                                    INSERT INTO chat_participants (room_id, user_type, user_id)
                                    VALUES (:room_id, 'restaurant_owner', :owner_id)
                                ");
                                $stmt->bindParam(':room_id', $room_id);
                                $stmt->bindParam(':owner_id', $orderDetails['owner_id']);
                                $stmt->execute();
                            }

                            // Add driver if assigned
                            if ($orderDetails['driver_id']) {
                                $stmt = $conn->prepare("
                                    INSERT INTO chat_participants (room_id, user_type, user_id)
                                    VALUES (:room_id, 'driver', :driver_id)
                                ");
                                $stmt->bindParam(':room_id', $room_id);
                                $stmt->bindParam(':driver_id', $orderDetails['driver_id']);
                                $stmt->execute();
                            }
                        }
                    } else if ($room_type === 'support' || $room_type === 'complaint') {
                        // Add admin to support and complaint chats
                        $stmt = $conn->prepare("
                            SELECT admin_id FROM admins ORDER BY admin_id LIMIT 1
                        ");
                        $stmt->execute();
                        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

                        if ($admin) {
                            $stmt = $conn->prepare("
                                INSERT INTO chat_participants (room_id, user_type, user_id)
                                VALUES (:room_id, 'admin', :admin_id)
                            ");
                            $stmt->bindParam(':room_id', $room_id);
                            $stmt->bindParam(':admin_id', $admin['admin_id']);
                            $stmt->execute();
                        }
                    }

                    // Add other participants if specified
                    foreach ($participants as $participant) {
                        $stmt = $conn->prepare("
                            INSERT INTO chat_participants (room_id, user_type, user_id)
                            VALUES (:room_id, :user_type, :user_id)
                        ");
                        $stmt->bindParam(':room_id', $room_id);
                        $stmt->bindParam(':user_type', $participant['user_type']);
                        $stmt->bindParam(':user_id', $participant['user_id']);
                        $stmt->execute();
                    }

                    // Add system message
                    $system_message = "Percakapan dibuat oleh " . ucfirst($user_type);

                    // Log for debugging
                    logError("Adding system message", [
                        'room_id' => $room_id,
                        'message' => $system_message,
                        'sender_type' => 'system'
                    ]);

                    $stmt = $conn->prepare("
                        INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
                        VALUES (:room_id, 'admin', 0, :message_text, 'system')
                    ");
                    $stmt->bindParam(':room_id', $room_id);
                    $stmt->bindParam(':message_text', $system_message);
                    $stmt->execute();

                    // Add first message from user if provided
                    if (!empty($message)) {
                        // Map user_type to valid sender_type if needed
                        $sender_type = $user_type;

                        // Make sure sender_type is one of the allowed values in the database
                        if (!in_array($sender_type, ['customer', 'restaurant_owner', 'driver', 'admin'])) {
                            // Default to customer if not a valid type
                            $sender_type = 'customer';
                        }

                        // Log for debugging
                        logError("Adding first message from user", [
                            'room_id' => $room_id,
                            'message' => $message,
                            'user_type' => $user_type,
                            'sender_type' => $sender_type,
                            'user_id' => $user_id
                        ]);

                        $stmt = $conn->prepare("
                            INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
                            VALUES (:room_id, :sender_type, :sender_id, :message_text, 'text')
                        ");
                        $stmt->bindParam(':room_id', $room_id);
                        $stmt->bindParam(':sender_type', $sender_type);
                        $stmt->bindParam(':sender_id', $user_id);
                        $stmt->bindParam(':message_text', $message);
                        $stmt->execute();
                    }

                    // Commit transaction
                    $conn->commit();

                    $response['success'] = true;
                    $response['message'] = 'Chat room created successfully';
                    $response['data'] = ['room_id' => $room_id];
                } catch (PDOException $e) {
                    // Rollback transaction on error
                    $conn->rollBack();
                    throw $e;
                }
                break;

            case 'send_message':
                // Send a message to a chat room
                // Log received data for debugging
                logError("Received data for send_message", $data);

                $room_id = isset($data['room_id']) ? intval($data['room_id']) : 0;
                $message_text = isset($data['message']) ? trim($data['message']) : '';
                $message_type = isset($data['message_type']) ? $data['message_type'] : 'text';

                // Log parsed data for debugging
                logError("Parsed data for send_message", [
                    'room_id' => $room_id,
                    'message_text' => $message_text,
                    'message_type' => $message_type
                ]);

                if (!$room_id) {
                    $response['message'] = 'Room ID is required';
                    break;
                }

                if (empty($message_text)) {
                    $response['message'] = 'Message cannot be empty';
                    break;
                }

                // Check if user is a participant in this room
                $stmt = $conn->prepare("
                    SELECT * FROM chat_participants
                    WHERE room_id = :room_id AND user_type = :user_type AND user_id = :user_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_type', $user_type);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();

                if ($stmt->rowCount() === 0) {
                    $response['message'] = 'You are not a participant in this chat room';
                    break;
                }

                // Begin transaction
                $conn->beginTransaction();

                // Map user_type to valid sender_type if needed
                $sender_type = $user_type;

                // Make sure sender_type is one of the allowed values in the database
                if (!in_array($sender_type, ['customer', 'restaurant_owner', 'driver', 'admin'])) {
                    // Default to customer if not a valid type
                    $sender_type = 'customer';
                }

                // Log for debugging
                logError("Sending message", [
                    'room_id' => $room_id,
                    'message' => $message_text,
                    'user_type' => $user_type,
                    'sender_type' => $sender_type,
                    'user_id' => $user_id,
                    'message_type' => $message_type
                ]);

                // Insert message
                $stmt = $conn->prepare("
                    INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
                    VALUES (:room_id, :sender_type, :sender_id, :message_text, :message_type)
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':sender_type', $sender_type);
                $stmt->bindParam(':sender_id', $user_id);
                $stmt->bindParam(':message_text', $message_text);
                $stmt->bindParam(':message_type', $message_type);
                $stmt->execute();

                $message_id = $conn->lastInsertId();

                // Update room's updated_at timestamp
                $stmt = $conn->prepare("
                    UPDATE chat_rooms
                    SET updated_at = NOW()
                    WHERE room_id = :room_id
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->execute();

                // Commit transaction
                $conn->commit();

                // Get sender name
                $sender_name = '';
                switch ($user_type) {
                    case 'customer':
                        $stmt = $conn->prepare("SELECT name FROM users WHERE user_id = :user_id");
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $user = $stmt->fetch(PDO::FETCH_ASSOC);
                        $sender_name = $user ? $user['name'] : 'Customer';
                        break;
                    case 'restaurant_owner':
                        $stmt = $conn->prepare("SELECT name FROM restaurant_owners WHERE owner_id = :user_id");
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $owner = $stmt->fetch(PDO::FETCH_ASSOC);
                        $sender_name = $owner ? $owner['name'] : 'Restaurant Owner';
                        break;
                    case 'driver':
                        $stmt = $conn->prepare("SELECT name FROM drivers WHERE driver_id = :user_id");
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $driver = $stmt->fetch(PDO::FETCH_ASSOC);
                        $sender_name = $driver ? $driver['name'] : 'Driver';
                        break;
                    case 'admin':
                        $stmt = $conn->prepare("SELECT name FROM admins WHERE admin_id = :user_id");
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                        $sender_name = $admin ? $admin['name'] : 'Admin';
                        break;
                }

                $response['success'] = true;
                $response['message'] = 'Message sent successfully';
                $response['data'] = [
                    'message_id' => $message_id,
                    'room_id' => $room_id,
                    'sender_type' => $user_type,
                    'sender_id' => $user_id,
                    'sender_name' => $sender_name,
                    'message_text' => $message_text,
                    'message_type' => $message_type,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                break;

            default:
                $response['message'] = 'Invalid action';
                break;
        }
    }
} catch (PDOException $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }

    $errorMessage = 'Database error: ' . $e->getMessage();
    $errorDetails = [
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];

    // Log the error
    logError($errorMessage, [
        'error_details' => $errorDetails,
        'request_data' => $data ?? [],
        'action' => $action ?? 'unknown',
        'method' => $method ?? 'unknown',
        'user_type' => $user_type ?? 'unknown',
        'user_id' => $user_id ?? 'unknown'
    ]);

    $response['message'] = $errorMessage;
    $response['error_details'] = $errorDetails;
}

// Return response
echo json_encode($response);
