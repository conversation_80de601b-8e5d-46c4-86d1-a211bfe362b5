<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['restaurant_id']) || !isset($input['user_lat']) || !isset($input['user_lng'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters: restaurant_id, user_lat, user_lng']);
    exit;
}

$restaurant_id = $input['restaurant_id'];
$user_lat = floatval($input['user_lat']);
$user_lng = floatval($input['user_lng']);

try {
    $conn = connectDB();
    
    // Get restaurant coordinates
    $stmt = $conn->prepare("SELECT latitude, longitude, name, address FROM restaurants WHERE restaurant_id = :restaurant_id");
    $stmt->bindParam(':restaurant_id', $restaurant_id);
    $stmt->execute();
    $restaurant = $stmt->fetch();
    
    if (!$restaurant) {
        http_response_code(404);
        echo json_encode(['error' => 'Restaurant not found']);
        exit;
    }
    
    $restaurant_lat = floatval($restaurant['latitude']);
    $restaurant_lng = floatval($restaurant['longitude']);
    
    // Validate coordinates
    if ($restaurant_lat == 0 || $restaurant_lng == 0) {
        // Use default coordinates if restaurant coordinates are not set
        $restaurant_lat = -6.2088; // Jakarta default
        $restaurant_lng = 106.8456;
    }
    
    if ($user_lat == 0 || $user_lng == 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid user coordinates']);
        exit;
    }
    
    // Calculate distance using Haversine formula
    $distance = getDistance($restaurant_lat, $restaurant_lng, $user_lat, $user_lng);
    
    // Calculate delivery fee
    $delivery_fee_rupiah = calculateDeliveryFee($distance);
    $delivery_fee_units = calculateDeliveryFeeInUnits($distance);
    
    // Calculate estimated delivery time
    $estimated_time = calculateEstimatedDeliveryTime($distance);
    
    // Response
    $response = [
        'success' => true,
        'distance' => round($distance, 2),
        'delivery_fee' => [
            'rupiah' => $delivery_fee_rupiah,
            'units' => $delivery_fee_units,
            'formatted' => 'Rp' . number_format($delivery_fee_rupiah, 0, ',', '.')
        ],
        'estimated_time' => round($estimated_time),
        'restaurant' => [
            'name' => $restaurant['name'],
            'address' => $restaurant['address'],
            'coordinates' => [
                'lat' => $restaurant_lat,
                'lng' => $restaurant_lng
            ]
        ],
        'user_coordinates' => [
            'lat' => $user_lat,
            'lng' => $user_lng
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
?>
