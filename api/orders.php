<?php
/**
 * API endpoint for orders
 */

header('Content-Type: application/json');

// Allow from any origin
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database connection
require_once __DIR__ . '/../config/database.php';
$conn = connectDB();

// Include auth functions
require_once __DIR__ . '/../includes/auth.php';

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get orders
        if (isset($_GET['id'])) {
            // Get specific order with order items
            $stmt = $conn->prepare("
                SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
                       r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
                       d.name as driver_name, d.phone as driver_phone,
                       d.vehicle_type, d.license_plate
                FROM orders o
                LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                LEFT JOIN drivers d ON o.driver_id = d.driver_id
                WHERE o.order_id = :id
            ");
            $stmt->bindParam(':id', $_GET['id']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $order = $stmt->fetch();
                
                // Get order items
                $stmt = $conn->prepare("
                    SELECT oi.*, m.name, m.image_url
                    FROM order_items oi
                    JOIN menu_items m ON oi.item_id = m.item_id
                    WHERE oi.order_id = :order_id
                ");
                $stmt->bindParam(':order_id', $_GET['id']);
                $stmt->execute();
                $orderItems = $stmt->fetchAll();
                
                // Get order tracking
                $stmt = $conn->prepare("
                    SELECT *
                    FROM order_tracking
                    WHERE order_id = :order_id
                    ORDER BY timestamp DESC
                ");
                $stmt->bindParam(':order_id', $_GET['id']);
                $stmt->execute();
                $tracking = $stmt->fetchAll();
                
                $order['items'] = $orderItems;
                $order['tracking'] = $tracking;
                
                echo json_encode(['success' => true, 'data' => $order]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Order not found']);
            }
        } elseif (isset($_GET['user_id'])) {
            // Get orders for a specific user
            $stmt = $conn->prepare("
                SELECT o.*, r.name as restaurant_name
                FROM orders o
                JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                WHERE o.user_id = :user_id
                ORDER BY o.created_at DESC
            ");
            $stmt->bindParam(':user_id', $_GET['user_id']);
            $stmt->execute();
            
            $orders = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $orders]);
        } elseif (isset($_GET['driver_id'])) {
            // Get orders for a specific driver
            $stmt = $conn->prepare("
                SELECT o.*, r.name as restaurant_name, r.address as restaurant_address,
                       r.latitude as restaurant_latitude, r.longitude as restaurant_longitude,
                       u.name as user_name, u.phone as user_phone
                FROM orders o
                JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                JOIN users u ON o.user_id = u.user_id
                WHERE o.driver_id = :driver_id
                ORDER BY o.created_at DESC
            ");
            $stmt->bindParam(':driver_id', $_GET['driver_id']);
            $stmt->execute();
            
            $orders = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $orders]);
        } elseif (isset($_GET['restaurant_id'])) {
            // Get orders for a specific restaurant
            $stmt = $conn->prepare("
                SELECT o.*, u.name as user_name, u.phone as user_phone,
                       d.name as driver_name, d.phone as driver_phone
                FROM orders o
                JOIN users u ON o.user_id = u.user_id
                LEFT JOIN drivers d ON o.driver_id = d.driver_id
                WHERE o.restaurant_id = :restaurant_id
                ORDER BY o.created_at DESC
            ");
            $stmt->bindParam(':restaurant_id', $_GET['restaurant_id']);
            $stmt->execute();
            
            $orders = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $orders]);
        } else {
            // Get all orders (admin only)
            // In a real app, you would check admin authentication here
            
            $stmt = $conn->prepare("
                SELECT o.*, r.name as restaurant_name, u.name as user_name,
                       d.name as driver_name
                FROM orders o
                JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                JOIN users u ON o.user_id = u.user_id
                LEFT JOIN drivers d ON o.driver_id = d.driver_id
                ORDER BY o.created_at DESC
            ");
            $stmt->execute();
            
            $orders = $stmt->fetchAll();
            echo json_encode(['success' => true, 'data' => $orders]);
        }
        break;
        
    case 'POST':
        // Create new order
        // In a real app, you would check user authentication here
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Check required fields
        if (!isset($data['user_id']) || !isset($data['restaurant_id']) || 
            !isset($data['items']) || !isset($data['delivery_address']) || 
            !isset($data['delivery_latitude']) || !isset($data['delivery_longitude'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }
        
        // Start transaction
        $conn->beginTransaction();
        
        try {
            // Calculate subtotal and total
            $subtotal = 0;
            
            // Get restaurant delivery fee
            $stmt = $conn->prepare("SELECT delivery_fee FROM restaurants WHERE restaurant_id = :restaurant_id");
            $stmt->bindParam(':restaurant_id', $data['restaurant_id']);
            $stmt->execute();
            $restaurant = $stmt->fetch();
            $delivery_fee = $restaurant['delivery_fee'];
            
            // Insert order
            $stmt = $conn->prepare("
                INSERT INTO orders (user_id, restaurant_id, order_status, payment_method, 
                                   payment_status, subtotal, delivery_fee, total_amount, 
                                   delivery_address, delivery_latitude, delivery_longitude, notes)
                VALUES (:user_id, :restaurant_id, 'pending', :payment_method, 
                       'pending', :subtotal, :delivery_fee, :total_amount, 
                       :delivery_address, :delivery_latitude, :delivery_longitude, :notes)
            ");
            
            $stmt->bindParam(':user_id', $data['user_id']);
            $stmt->bindParam(':restaurant_id', $data['restaurant_id']);
            $stmt->bindParam(':payment_method', $data['payment_method'] ?? 'cash');
            $stmt->bindParam(':delivery_address', $data['delivery_address']);
            $stmt->bindParam(':delivery_latitude', $data['delivery_latitude']);
            $stmt->bindParam(':delivery_longitude', $data['delivery_longitude']);
            $stmt->bindParam(':notes', $data['notes'] ?? null);
            
            // Placeholder values for subtotal and total, will update after calculating
            $stmt->bindValue(':subtotal', 0);
            $stmt->bindValue(':delivery_fee', $delivery_fee);
            $stmt->bindValue(':total_amount', 0);
            
            $stmt->execute();
            $order_id = $conn->lastInsertId();
            
            // Insert order items and calculate subtotal
            foreach ($data['items'] as $item) {
                // Get item price from database to prevent client-side price manipulation
                $stmt = $conn->prepare("SELECT price FROM menu_items WHERE item_id = :item_id");
                $stmt->bindParam(':item_id', $item['item_id']);
                $stmt->execute();
                $menuItem = $stmt->fetch();
                $price = $menuItem['price'];
                
                // Insert order item
                $stmt = $conn->prepare("
                    INSERT INTO order_items (order_id, item_id, quantity, price, special_instructions)
                    VALUES (:order_id, :item_id, :quantity, :price, :special_instructions)
                ");
                
                $stmt->bindParam(':order_id', $order_id);
                $stmt->bindParam(':item_id', $item['item_id']);
                $stmt->bindParam(':quantity', $item['quantity']);
                $stmt->bindParam(':price', $price);
                $stmt->bindParam(':special_instructions', $item['special_instructions'] ?? null);
                
                $stmt->execute();
                
                // Add to subtotal
                $subtotal += $price * $item['quantity'];
            }
            
            // Calculate total
            $total_amount = $subtotal + $delivery_fee;
            
            // Update order with correct subtotal and total
            $stmt = $conn->prepare("
                UPDATE orders 
                SET subtotal = :subtotal, total_amount = :total_amount 
                WHERE order_id = :order_id
            ");
            
            $stmt->bindParam(':subtotal', $subtotal);
            $stmt->bindParam(':total_amount', $total_amount);
            $stmt->bindParam(':order_id', $order_id);
            
            $stmt->execute();
            
            // Add initial tracking entry
            $stmt = $conn->prepare("
                INSERT INTO order_tracking (order_id, status, notes)
                VALUES (:order_id, 'pending', 'Order placed')
            ");
            
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();
            
            // Commit transaction
            $conn->commit();
            
            http_response_code(201);
            echo json_encode([
                'success' => true, 
                'message' => 'Order created successfully', 
                'order_id' => $order_id,
                'subtotal' => $subtotal,
                'delivery_fee' => $delivery_fee,
                'total_amount' => $total_amount
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create order: ' . $e->getMessage()]);
        }
        break;
        
    case 'PUT':
        // Update order status
        // In a real app, you would check authentication here
        
        // Get order ID from URL
        $url_components = parse_url($_SERVER['REQUEST_URI']);
        parse_str($url_components['query'] ?? '', $params);
        
        if (!isset($params['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Order ID is required']);
            exit;
        }
        
        // Get JSON input
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Check if status is provided
        if (!isset($data['order_status'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Order status is required']);
            exit;
        }
        
        // Start transaction
        $conn->beginTransaction();
        
        try {
            // Update order status
            $stmt = $conn->prepare("
                UPDATE orders 
                SET order_status = :order_status
                WHERE order_id = :order_id
            ");
            
            $stmt->bindParam(':order_status', $data['order_status']);
            $stmt->bindParam(':order_id', $params['id']);
            
            $stmt->execute();
            
            // Update driver if provided
            if (isset($data['driver_id'])) {
                $stmt = $conn->prepare("
                    UPDATE orders 
                    SET driver_id = :driver_id
                    WHERE order_id = :order_id
                ");
                
                $stmt->bindParam(':driver_id', $data['driver_id']);
                $stmt->bindParam(':order_id', $params['id']);
                
                $stmt->execute();
            }
            
            // Update payment status if provided
            if (isset($data['payment_status'])) {
                $stmt = $conn->prepare("
                    UPDATE orders 
                    SET payment_status = :payment_status
                    WHERE order_id = :order_id
                ");
                
                $stmt->bindParam(':payment_status', $data['payment_status']);
                $stmt->bindParam(':order_id', $params['id']);
                
                $stmt->execute();
            }
            
            // Add tracking entry
            $stmt = $conn->prepare("
                INSERT INTO order_tracking (order_id, status, notes, latitude, longitude)
                VALUES (:order_id, :status, :notes, :latitude, :longitude)
            ");
            
            $stmt->bindParam(':order_id', $params['id']);
            $stmt->bindParam(':status', $data['order_status']);
            $stmt->bindParam(':notes', $data['notes'] ?? null);
            $stmt->bindParam(':latitude', $data['latitude'] ?? null);
            $stmt->bindParam(':longitude', $data['longitude'] ?? null);
            
            $stmt->execute();
            
            // Commit transaction
            $conn->commit();
            
            echo json_encode(['success' => true, 'message' => 'Order updated successfully']);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update order: ' . $e->getMessage()]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
