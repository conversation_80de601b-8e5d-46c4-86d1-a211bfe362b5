<?php
// Start session
session_start();

// Set headers
header('Content-Type: application/json');

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'cart' => null
];

// Handle GET request (retrieve cart)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Check if cart exists in session
    if (isset($_SESSION['cart'])) {
        $response['success'] = true;
        $response['cart'] = $_SESSION['cart'];
    } else {
        $response['message'] = 'Cart not found';
    }
}

// Handle POST request (update cart)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data from request body
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if ($data) {
        // Ensure all items have item_id property
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as &$item) {
                // If item doesn't have item_id but has id, copy id to item_id
                if (!isset($item['item_id']) && isset($item['id'])) {
                    $item['item_id'] = $item['id'];
                }
            }
            unset($item); // Break the reference
        }

        // Save cart to session
        $_SESSION['cart'] = $data;

        $response['success'] = true;
        $response['message'] = 'Cart updated successfully';
    } else {
        $response['message'] = 'Invalid cart data';
    }
}

// Return response
echo json_encode($response);
