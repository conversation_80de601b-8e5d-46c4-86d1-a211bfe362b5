<?php
/**
 * Complaints API for KikaZen Ship
 * Handles complaint operations: creating complaints, updating status, etc.
 */

// Set headers for JSON response
header('Content-Type: application/json');

// Start session
session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Connect to database
$conn = connectDB();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Initialize response
$response = [
    'success' => false,
    'message' => 'Invalid request',
    'data' => null
];

// Check if user is logged in
if (!isUserLoggedIn() && !isAdminLoggedIn()) {
    $response['message'] = 'Unauthorized';
    echo json_encode($response);
    exit;
}

// Determine user type and ID
$user_type = '';
$user_id = 0;

if (isUserLoggedIn()) {
    $user_type = 'customer';
    $user_id = $_SESSION['user_id'];
} elseif (isAdminLoggedIn()) {
    $user_type = 'admin';
    $user_id = $_SESSION['admin_id'];
}

// Process request based on method and action
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    // GET requests
    if ($method === 'GET') {
        switch ($action) {
            case 'get_complaints':
                // Get complaints for the current user or all complaints for admin
                if ($user_type === 'customer') {
                    $stmt = $conn->prepare("
                        SELECT c.*, 
                               o.order_id, 
                               o.total_amount,
                               r.name AS restaurant_name,
                               a.name AS admin_name,
                               (SELECT COUNT(*) FROM complaint_updates cu WHERE cu.complaint_id = c.complaint_id) AS update_count
                        FROM complaints c
                        LEFT JOIN orders o ON c.order_id = o.order_id
                        LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                        LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                        WHERE c.user_id = :user_id
                        ORDER BY c.created_at DESC
                    ");
                    $stmt->bindParam(':user_id', $user_id);
                } else {
                    // For admin, get all complaints or filter by status
                    $status = isset($_GET['status']) ? $_GET['status'] : '';
                    
                    if ($status) {
                        $stmt = $conn->prepare("
                            SELECT c.*, 
                                   u.name AS user_name,
                                   o.order_id, 
                                   o.total_amount,
                                   r.name AS restaurant_name,
                                   a.name AS admin_name,
                                   (SELECT COUNT(*) FROM complaint_updates cu WHERE cu.complaint_id = c.complaint_id) AS update_count
                            FROM complaints c
                            LEFT JOIN users u ON c.user_id = u.user_id
                            LEFT JOIN orders o ON c.order_id = o.order_id
                            LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                            LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                            WHERE c.status = :status
                            ORDER BY 
                                CASE c.priority
                                    WHEN 'urgent' THEN 1
                                    WHEN 'high' THEN 2
                                    WHEN 'medium' THEN 3
                                    WHEN 'low' THEN 4
                                END,
                                c.created_at DESC
                        ");
                        $stmt->bindParam(':status', $status);
                    } else {
                        $stmt = $conn->prepare("
                            SELECT c.*, 
                                   u.name AS user_name,
                                   o.order_id, 
                                   o.total_amount,
                                   r.name AS restaurant_name,
                                   a.name AS admin_name,
                                   (SELECT COUNT(*) FROM complaint_updates cu WHERE cu.complaint_id = c.complaint_id) AS update_count
                            FROM complaints c
                            LEFT JOIN users u ON c.user_id = u.user_id
                            LEFT JOIN orders o ON c.order_id = o.order_id
                            LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                            LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                            ORDER BY 
                                CASE c.status
                                    WHEN 'pending' THEN 1
                                    WHEN 'in_progress' THEN 2
                                    WHEN 'resolved' THEN 3
                                    WHEN 'closed' THEN 4
                                END,
                                CASE c.priority
                                    WHEN 'urgent' THEN 1
                                    WHEN 'high' THEN 2
                                    WHEN 'medium' THEN 3
                                    WHEN 'low' THEN 4
                                END,
                                c.created_at DESC
                        ");
                    }
                }
                
                $stmt->execute();
                $complaints = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Format the data
                foreach ($complaints as &$complaint) {
                    $complaint['total_amount'] = $complaint['total_amount'] ? $complaint['total_amount'] * 15000 : 0;
                    $complaint['created_at'] = date('Y-m-d H:i:s', strtotime($complaint['created_at']));
                    $complaint['updated_at'] = date('Y-m-d H:i:s', strtotime($complaint['updated_at']));
                    $complaint['resolved_at'] = $complaint['resolved_at'] ? date('Y-m-d H:i:s', strtotime($complaint['resolved_at'])) : null;
                    
                    // Get attachments
                    $stmt = $conn->prepare("
                        SELECT * FROM complaint_attachments
                        WHERE complaint_id = :complaint_id
                    ");
                    $stmt->bindParam(':complaint_id', $complaint['complaint_id']);
                    $stmt->execute();
                    $complaint['attachments'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                }
                
                $response['success'] = true;
                $response['message'] = 'Complaints retrieved successfully';
                $response['data'] = $complaints;
                break;
                
            case 'get_complaint':
                // Get a specific complaint
                $complaint_id = isset($_GET['complaint_id']) ? intval($_GET['complaint_id']) : 0;
                
                if (!$complaint_id) {
                    $response['message'] = 'Complaint ID is required';
                    break;
                }
                
                // Check if user has access to this complaint
                if ($user_type === 'customer') {
                    $stmt = $conn->prepare("
                        SELECT * FROM complaints 
                        WHERE complaint_id = :complaint_id AND user_id = :user_id
                    ");
                    $stmt->bindParam(':complaint_id', $complaint_id);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() === 0) {
                        $response['message'] = 'You do not have access to this complaint';
                        break;
                    }
                }
                
                // Get complaint details
                $stmt = $conn->prepare("
                    SELECT c.*, 
                           u.name AS user_name,
                           u.email AS user_email,
                           o.order_id, 
                           o.total_amount,
                           r.name AS restaurant_name,
                           a.name AS admin_name
                    FROM complaints c
                    LEFT JOIN users u ON c.user_id = u.user_id
                    LEFT JOIN orders o ON c.order_id = o.order_id
                    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
                    LEFT JOIN admins a ON c.assigned_admin_id = a.admin_id
                    WHERE c.complaint_id = :complaint_id
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                
                $complaint = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$complaint) {
                    $response['message'] = 'Complaint not found';
                    break;
                }
                
                // Format the data
                $complaint['total_amount'] = $complaint['total_amount'] ? $complaint['total_amount'] * 15000 : 0;
                $complaint['created_at'] = date('Y-m-d H:i:s', strtotime($complaint['created_at']));
                $complaint['updated_at'] = date('Y-m-d H:i:s', strtotime($complaint['updated_at']));
                $complaint['resolved_at'] = $complaint['resolved_at'] ? date('Y-m-d H:i:s', strtotime($complaint['resolved_at'])) : null;
                
                // Get attachments
                $stmt = $conn->prepare("
                    SELECT * FROM complaint_attachments
                    WHERE complaint_id = :complaint_id
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                $complaint['attachments'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Get updates
                $stmt = $conn->prepare("
                    SELECT cu.*, a.name AS admin_name
                    FROM complaint_updates cu
                    JOIN admins a ON cu.admin_id = a.admin_id
                    WHERE cu.complaint_id = :complaint_id
                    ORDER BY cu.created_at ASC
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                $complaint['updates'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Format update timestamps
                foreach ($complaint['updates'] as &$update) {
                    $update['created_at'] = date('Y-m-d H:i:s', strtotime($update['created_at']));
                }
                
                // Get chat room if exists
                $stmt = $conn->prepare("
                    SELECT cr.room_id
                    FROM chat_rooms cr
                    WHERE cr.room_type = 'complaint' AND cr.order_id = :complaint_id
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                $chatRoom = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $complaint['chat_room_id'] = $chatRoom ? $chatRoom['room_id'] : null;
                
                $response['success'] = true;
                $response['message'] = 'Complaint retrieved successfully';
                $response['data'] = $complaint;
                break;
                
            default:
                $response['message'] = 'Invalid action';
                break;
        }
    }
    // POST requests
    elseif ($method === 'POST') {
        switch ($action) {
            case 'create_complaint':
                // Create a new complaint
                if ($user_type !== 'customer') {
                    $response['message'] = 'Only customers can create complaints';
                    break;
                }
                
                $order_id = isset($data['order_id']) ? intval($data['order_id']) : null;
                $complaint_type = isset($data['complaint_type']) ? $data['complaint_type'] : '';
                $subject = isset($data['subject']) ? trim($data['subject']) : '';
                $description = isset($data['description']) ? trim($data['description']) : '';
                
                if (empty($complaint_type) || empty($subject) || empty($description)) {
                    $response['message'] = 'Complaint type, subject, and description are required';
                    break;
                }
                
                // Check if order belongs to user if provided
                if ($order_id) {
                    $stmt = $conn->prepare("
                        SELECT * FROM orders 
                        WHERE order_id = :order_id AND user_id = :user_id
                    ");
                    $stmt->bindParam(':order_id', $order_id);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() === 0) {
                        $response['message'] = 'Invalid order ID';
                        break;
                    }
                }
                
                // Begin transaction
                $conn->beginTransaction();
                
                // Insert complaint
                $stmt = $conn->prepare("
                    INSERT INTO complaints (user_id, order_id, complaint_type, subject, description, status, priority)
                    VALUES (:user_id, :order_id, :complaint_type, :subject, :description, 'pending', 'medium')
                ");
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':order_id', $order_id);
                $stmt->bindParam(':complaint_type', $complaint_type);
                $stmt->bindParam(':subject', $subject);
                $stmt->bindParam(':description', $description);
                $stmt->execute();
                
                $complaint_id = $conn->lastInsertId();
                
                // Create chat room for the complaint
                $stmt = $conn->prepare("
                    INSERT INTO chat_rooms (order_id, room_type, status)
                    VALUES (:complaint_id, 'complaint', 'active')
                ");
                $stmt->bindParam(':complaint_id', $complaint_id);
                $stmt->execute();
                
                $room_id = $conn->lastInsertId();
                
                // Add customer as participant
                $stmt = $conn->prepare("
                    INSERT INTO chat_participants (room_id, user_type, user_id)
                    VALUES (:room_id, 'customer', :user_id)
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                // Add system message
                $system_message = "Complaint support chat created";
                $stmt = $conn->prepare("
                    INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
                    VALUES (:room_id, 'system', 0, :message_text, 'system')
                ");
                $stmt->bindParam(':room_id', $room_id);
                $stmt->bindParam(':message_text', $system_message);
                $stmt->execute();
                
                // Commit transaction
                $conn->commit();
                
                $response['success'] = true;
                $response['message'] = 'Complaint created successfully';
                $response['data'] = [
                    'complaint_id' => $complaint_id,
                    'chat_room_id' => $room_id
                ];
                break;
                
            case 'update_complaint':
                // Update a complaint (admin only)
                if ($user_type !== 'admin') {
                    $response['message'] = 'Only admins can update complaints';
                    break;
                }
                
                $complaint_id = isset($data['complaint_id']) ? intval($data['complaint_id']) : 0;
                $status = isset($data['status']) ? $data['status'] : '';
                $priority = isset($data['priority']) ? $data['priority'] : '';
                $assigned_admin_id = isset($data['assigned_admin_id']) ? intval($data['assigned_admin_id']) : null;
                $resolution_notes = isset($data['resolution_notes']) ? trim($data['resolution_notes']) : '';
                $update_text = isset($data['update_text']) ? trim($data['update_text']) : '';
                
                if (!$complaint_id) {
                    $response['message'] = 'Complaint ID is required';
                    break;
                }
                
                // Begin transaction
                $conn->beginTransaction();
                
                // Update complaint
                $updateFields = [];
                $params = [':complaint_id' => $complaint_id];
                
                if ($status) {
                    $updateFields[] = "status = :status";
                    $params[':status'] = $status;
                    
                    // Set resolved_at if status is resolved
                    if ($status === 'resolved') {
                        $updateFields[] = "resolved_at = NOW()";
                    }
                }
                
                if ($priority) {
                    $updateFields[] = "priority = :priority";
                    $params[':priority'] = $priority;
                }
                
                if ($assigned_admin_id) {
                    $updateFields[] = "assigned_admin_id = :assigned_admin_id";
                    $params[':assigned_admin_id'] = $assigned_admin_id;
                }
                
                if ($resolution_notes) {
                    $updateFields[] = "resolution_notes = :resolution_notes";
                    $params[':resolution_notes'] = $resolution_notes;
                }
                
                if (!empty($updateFields)) {
                    $sql = "UPDATE complaints SET " . implode(", ", $updateFields) . " WHERE complaint_id = :complaint_id";
                    $stmt = $conn->prepare($sql);
                    foreach ($params as $key => $value) {
                        $stmt->bindValue($key, $value);
                    }
                    $stmt->execute();
                }
                
                // Add update if provided
                if ($update_text) {
                    $stmt = $conn->prepare("
                        INSERT INTO complaint_updates (complaint_id, admin_id, update_text)
                        VALUES (:complaint_id, :admin_id, :update_text)
                    ");
                    $stmt->bindParam(':complaint_id', $complaint_id);
                    $stmt->bindParam(':admin_id', $user_id);
                    $stmt->bindParam(':update_text', $update_text);
                    $stmt->execute();
                }
                
                // Commit transaction
                $conn->commit();
                
                $response['success'] = true;
                $response['message'] = 'Complaint updated successfully';
                break;
                
            default:
                $response['message'] = 'Invalid action';
                break;
        }
    }
} catch (PDOException $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['message'] = 'Database error: ' . $e->getMessage();
}

// Return response
echo json_encode($response);
