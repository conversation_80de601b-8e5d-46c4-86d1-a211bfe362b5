<?php
// Script untuk memperbaiki chat rooms untuk pesanan yang sudah ada
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Memperbaiki Chat Rooms untuk Pesanan yang Sudah Ada</h2>";

$conn = connectDB();

// Get orders that don't have chat rooms
$stmt = $conn->prepare("
    SELECT o.*, r.owner_id, u.name as customer_name, ro.name as owner_name, d.name as driver_name
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    WHERE o.order_id NOT IN (SELECT DISTINCT order_id FROM chat_rooms WHERE order_id IS NOT NULL)
    ORDER BY o.created_at DESC
");
$stmt->execute();
$ordersWithoutChat = $stmt->fetchAll();

echo "<p>Ditemukan " . count($ordersWithoutChat) . " pesanan tanpa chat room.</p>";

if (!empty($ordersWithoutChat)) {
    echo "<h3>Membuat Chat Rooms...</h3>";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($ordersWithoutChat as $order) {
        echo "<p>Memproses Order #{$order['order_id']} - {$order['customer_name']}...</p>";
        
        $room_id = createOrderChatRoom($order['order_id']);
        
        if ($room_id) {
            echo "<p style='color: green; margin-left: 20px;'>✓ Chat room berhasil dibuat (ID: $room_id)</p>";
            $success_count++;
            
            // Show participants
            $stmt = $conn->prepare("
                SELECT cp.*, 
                       CASE 
                           WHEN cp.user_type = 'customer' THEN u.name
                           WHEN cp.user_type = 'restaurant_owner' THEN ro.name
                           WHEN cp.user_type = 'driver' THEN d.name
                           ELSE 'Unknown'
                       END as user_name
                FROM chat_participants cp
                LEFT JOIN users u ON cp.user_type = 'customer' AND cp.user_id = u.user_id
                LEFT JOIN restaurant_owners ro ON cp.user_type = 'restaurant_owner' AND cp.user_id = ro.owner_id
                LEFT JOIN drivers d ON cp.user_type = 'driver' AND cp.user_id = d.driver_id
                WHERE cp.room_id = :room_id
            ");
            $stmt->bindParam(':room_id', $room_id);
            $stmt->execute();
            $participants = $stmt->fetchAll();
            
            echo "<p style='margin-left: 40px;'>Participants:</p>";
            echo "<ul style='margin-left: 40px;'>";
            foreach ($participants as $participant) {
                echo "<li>{$participant['user_type']}: {$participant['user_name']}</li>";
            }
            echo "</ul>";
            
        } else {
            echo "<p style='color: red; margin-left: 20px;'>✗ Gagal membuat chat room</p>";
            $error_count++;
        }
    }
    
    echo "<hr>";
    echo "<h3>Ringkasan</h3>";
    echo "<p>✓ Berhasil: $success_count chat rooms</p>";
    echo "<p>✗ Gagal: $error_count chat rooms</p>";
    
} else {
    echo "<p style='color: green;'>✓ Semua pesanan sudah memiliki chat room!</p>";
}

// Check for orders with drivers but driver not in chat room
echo "<h3>Memeriksa Driver yang Belum Ditambahkan ke Chat Room</h3>";

$stmt = $conn->prepare("
    SELECT o.order_id, o.driver_id, d.name as driver_name, cr.room_id
    FROM orders o
    JOIN drivers d ON o.driver_id = d.driver_id
    JOIN chat_rooms cr ON o.order_id = cr.order_id
    WHERE o.driver_id IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM chat_participants cp 
        WHERE cp.room_id = cr.room_id 
        AND cp.user_type = 'driver' 
        AND cp.user_id = o.driver_id
    )
");
$stmt->execute();
$driversNotInChat = $stmt->fetchAll();

echo "<p>Ditemukan " . count($driversNotInChat) . " driver yang belum ditambahkan ke chat room.</p>";

if (!empty($driversNotInChat)) {
    echo "<h4>Menambahkan Driver ke Chat Room...</h4>";
    
    $driver_success = 0;
    $driver_error = 0;
    
    foreach ($driversNotInChat as $driverOrder) {
        echo "<p>Menambahkan Driver {$driverOrder['driver_name']} ke Order #{$driverOrder['order_id']}...</p>";
        
        $added = addDriverToChatRoom($driverOrder['order_id'], $driverOrder['driver_id']);
        
        if ($added) {
            echo "<p style='color: green; margin-left: 20px;'>✓ Driver berhasil ditambahkan</p>";
            $driver_success++;
        } else {
            echo "<p style='color: red; margin-left: 20px;'>✗ Gagal menambahkan driver</p>";
            $driver_error++;
        }
    }
    
    echo "<hr>";
    echo "<h4>Ringkasan Driver</h4>";
    echo "<p>✓ Berhasil: $driver_success drivers</p>";
    echo "<p>✗ Gagal: $driver_error drivers</p>";
    
} else {
    echo "<p style='color: green;'>✓ Semua driver sudah ditambahkan ke chat room!</p>";
}

// Final statistics
echo "<h3>Statistik Akhir</h3>";

$stats = [];

// Total chat rooms
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM chat_rooms WHERE room_type = 'order'");
$stmt->execute();
$stats['total_order_rooms'] = $stmt->fetch()['count'];

// Total orders
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders");
$stmt->execute();
$stats['total_orders'] = $stmt->fetch()['count'];

// Orders with chat rooms
$stmt = $conn->prepare("SELECT COUNT(DISTINCT order_id) as count FROM chat_rooms WHERE order_id IS NOT NULL");
$stmt->execute();
$stats['orders_with_chat'] = $stmt->fetch()['count'];

// Orders without chat rooms
$stmt = $conn->prepare("
    SELECT COUNT(*) as count FROM orders o
    WHERE o.order_id NOT IN (SELECT DISTINCT order_id FROM chat_rooms WHERE order_id IS NOT NULL)
");
$stmt->execute();
$stats['orders_without_chat'] = $stmt->fetch()['count'];

// Participants by type
$stmt = $conn->prepare("
    SELECT user_type, COUNT(*) as count 
    FROM chat_participants cp
    JOIN chat_rooms cr ON cp.room_id = cr.room_id
    WHERE cr.room_type = 'order'
    GROUP BY user_type
");
$stmt->execute();
$participantsByType = $stmt->fetchAll();

echo "<ul>";
echo "<li>Total Orders: {$stats['total_orders']}</li>";
echo "<li>Total Order Chat Rooms: {$stats['total_order_rooms']}</li>";
echo "<li>Orders dengan Chat Room: {$stats['orders_with_chat']}</li>";
echo "<li>Orders tanpa Chat Room: {$stats['orders_without_chat']}</li>";
echo "</ul>";

echo "<h4>Participants by Type:</h4>";
echo "<ul>";
foreach ($participantsByType as $type) {
    echo "<li>{$type['user_type']}: {$type['count']}</li>";
}
echo "</ul>";

$coverage = $stats['total_orders'] > 0 ? round(($stats['orders_with_chat'] / $stats['total_orders']) * 100, 2) : 0;
echo "<p><strong>Coverage: {$coverage}% pesanan memiliki chat room</strong></p>";

if ($stats['orders_without_chat'] == 0) {
    echo "<p style='color: green; font-weight: bold;'>🎉 Semua pesanan sekarang memiliki chat room!</p>";
}

echo "<hr>";
echo "<p><a href='test_chat_system.php'>Test Chat System</a> | <a href='index.php'>Kembali ke Beranda</a></p>";
?>
