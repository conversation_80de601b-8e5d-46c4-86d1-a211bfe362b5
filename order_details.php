<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isUserLoggedIn() && !isDriverLoggedIn() && !isOwnerLoggedIn() && !isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Check if order ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$order_id = $_GET['id'];

// Connect to database
$conn = connectDB();

// Get order information
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.logo_url as restaurant_logo, r.phone as restaurant_phone,
           u.name as user_name, u.phone as user_phone,
           d.name as driver_name, d.phone as driver_phone
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    JOIN users u ON o.user_id = u.user_id
    LEFT JOIN drivers d ON o.driver_id = d.driver_id
    WHERE o.order_id = ?
");
$stmt->execute([$order_id]);
$order = $stmt->fetch();

// Check if order exists and user has permission to view it
if (!$order) {
    header('Location: index.php');
    exit;
}

// Check if user has permission to view this order
$hasPermission = false;

if (isUserLoggedIn() && $order['user_id'] == $_SESSION['user_id']) {
    $hasPermission = true;
} elseif (isDriverLoggedIn() && $order['driver_id'] == $_SESSION['driver_id']) {
    $hasPermission = true;
} elseif (isOwnerLoggedIn() && $order['restaurant_id'] == $_SESSION['restaurant_id']) {
    $hasPermission = true;
} elseif (isAdminLoggedIn()) {
    $hasPermission = true;
}

if (!$hasPermission) {
    header('Location: index.php');
    exit;
}

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, mi.name as item_name, mi.image_url as item_image
    FROM order_items oi
    JOIN menu_items mi ON oi.item_id = mi.item_id
    WHERE oi.order_id = ?
");
$stmt->execute([$order_id]);
$orderItems = $stmt->fetchAll();

// Get order tracking
$stmt = $conn->prepare("
    SELECT *
    FROM order_tracking
    WHERE order_id = ?
    ORDER BY timestamp DESC
");
$stmt->execute([$order_id]);
$orderTracking = $stmt->fetchAll();

// Use existing functions from includes/functions.php
// These functions are already defined in includes/functions.php:
// - getOrderStatusText() instead of getStatusTranslation()
// - getOrderStatusBadgeClass() instead of getStatusBadgeClass()
// - getPaymentStatusText() instead of getPaymentStatusTranslation()
// - getPaymentMethodText() instead of getPaymentMethodTranslation()
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Pesanan #<?= $order_id ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isUserLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['user_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php elseif (isDriverLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['driver_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php elseif (isOwnerLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['owner_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php elseif (isAdminLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $_SESSION['admin_name'] ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Detail Pesanan #<?= $order_id ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                        <?php if (isUserLoggedIn()): ?>
                            <li class="breadcrumb-item"><a href="user/orders.php">Pesanan</a></li>
                        <?php elseif (isDriverLoggedIn()): ?>
                            <li class="breadcrumb-item"><a href="driver/dashboard.php">Dasbor</a></li>
                        <?php elseif (isOwnerLoggedIn()): ?>
                            <li class="breadcrumb-item"><a href="owner/dashboard.php">Dasbor</a></li>
                        <?php elseif (isAdminLoggedIn()): ?>
                            <li class="breadcrumb-item"><a href="admin/dashboard.php">Dasbor</a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page">Detail Pesanan #<?= $order_id ?></li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?> fs-6 p-2">
                    <?= getOrderStatusText($order['order_status']) ?>
                </span>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- Order Items -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Item Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($orderItems as $item): ?>
                            <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                <img src="<?= $item['item_image'] ?? 'assets/restaurant-placeholder.png' ?>" alt="<?= $item['item_name'] ?>" class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h5 class="mb-0"><?= $item['item_name'] ?></h5>
                                    <p class="mb-0 text-muted"><?= formatCurrency($item['price']) ?> x <?= $item['quantity'] ?></p>
                                </div>
                                <div class="text-end">
                                    <h5><?= formatCurrency($item['subtotal']) ?></h5>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="d-flex justify-content-between mt-4">
                            <span>Subtotal:</span>
                            <span><?= formatCurrency($order['subtotal']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Biaya Pengiriman:</span>
                            <span><?= formatCurrency($order['delivery_fee']) ?></span>
                        </div>
                        <?php if ($order['discount_amount'] > 0): ?>
                            <div class="d-flex justify-content-between">
                                <span>Diskon:</span>
                                <span>-<?= formatCurrency($order['discount_amount']) ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if ($order['carbon_offset'] && isset($order['carbon_offset_amount']) && $order['carbon_offset_amount'] > 0): ?>
                            <div class="d-flex justify-content-between">
                                <span>Offset Karbon:</span>
                                <span><?= formatCurrency($order['carbon_offset_amount']) ?></span>
                            </div>
                        <?php endif; ?>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <span class="fw-bold">Total:</span>
                            <span class="fw-bold fs-5"><?= formatCurrency($order['total_amount']) ?></span>
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Nomor Pesanan:</div>
                            <div class="col-md-8">#<?= $order_id ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Tanggal Pesanan:</div>
                            <div class="col-md-8"><?= date('d M Y, H:i', strtotime($order['created_at'])) ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Status Pesanan:</div>
                            <div class="col-md-8">
                                <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?>">
                                    <?= getOrderStatusText($order['order_status']) ?>
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Metode Pembayaran:</div>
                            <div class="col-md-8"><?= getPaymentMethodText($order['payment_method']) ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Status Pembayaran:</div>
                            <div class="col-md-8">
                                <span class="badge <?= getPaymentStatusBadgeClass($order['payment_status']) ?>">
                                    <?= getPaymentStatusText($order['payment_status']) ?>
                                </span>
                            </div>
                        </div>
                        <?php if ($order['notes']): ?>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Catatan:</div>
                                <div class="col-md-8"><?= $order['notes'] ?></div>
                            </div>
                        <?php endif; ?>

                        <!-- Sustainability Options -->
                        <?php if ($order['no_utensils'] || $order['eco_packaging'] || $order['carbon_offset']): ?>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Opsi Keberlanjutan:</div>
                                <div class="col-md-8">
                                    <?php if ($order['no_utensils']): ?>
                                        <div><i class="fas fa-utensils-slash me-2 text-success"></i> Tanpa Alat Makan</div>
                                    <?php endif; ?>
                                    <?php if ($order['eco_packaging']): ?>
                                        <div><i class="fas fa-box me-2 text-success"></i> Kemasan Ramah Lingkungan</div>
                                    <?php endif; ?>
                                    <?php if ($order['carbon_offset']): ?>
                                        <div><i class="fas fa-leaf me-2 text-success"></i> Offset Karbon</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Restaurant Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Restoran</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <img src="<?= $order['restaurant_logo'] ?? 'assets/restaurant-placeholder.png' ?>" alt="<?= $order['restaurant_name'] ?>" class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                            <div>
                                <h5 class="mb-0"><?= $order['restaurant_name'] ?></h5>
                                <p class="mb-0"><i class="fas fa-phone me-2"></i> <?= $order['restaurant_phone'] ?></p>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="restaurant.php?id=<?= $order['restaurant_id'] ?>" class="btn btn-outline-primary">
                                <i class="fas fa-store me-2"></i> Lihat Restoran
                            </a>
                            <?php if (isUserLoggedIn()): ?>
                                <a href="user/chat.php?start_chat=order&order_id=<?= $order_id ?>" class="btn btn-outline-success">
                                    <i class="fas fa-comments me-2"></i> Chat Tentang Pesanan
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Delivery Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pengiriman</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>Alamat Pengiriman:</h6>
                            <p><?= $order['delivery_address'] ?></p>
                        </div>

                        <?php if ($order['driver_id']): ?>
                            <div class="mb-3">
                                <h6>Pengemudi:</h6>
                                <p>
                                    <i class="fas fa-user me-2"></i> <?= $order['driver_name'] ?><br>
                                    <i class="fas fa-phone me-2"></i> <?= $order['driver_phone'] ?>
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Order Tracking -->
                <?php if (!empty($orderTracking)): ?>
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Pelacakan Pesanan</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <?php foreach ($orderTracking as $tracking): ?>
                                    <li class="list-group-item">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <span class="badge <?= getOrderStatusBadgeClass($tracking['status']) ?>">
                                                    <?= getOrderStatusText($tracking['status']) ?>
                                                </span>
                                                <?php if ($tracking['notes']): ?>
                                                    <p class="mb-0 mt-1 small"><?= $tracking['notes'] ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted"><?= date('d M Y, H:i', strtotime($tracking['timestamp'])) ?></small>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship</h5>
                    <p>Layanan pengiriman makanan terbaik di kota Anda.</p>
                    <p>
                        <a href="about.php" class="text-white">Tentang Kami</a> |
                        <a href="contact.php" class="text-white">Hubungi Kami</a> |
                        <a href="terms.php" class="text-white">Syarat dan Ketentuan</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Contoh No. 123, Jakarta</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
