# Sistem GPS dan Perhitungan Biaya Pengiriman

## Masalah yang Diperbaiki

### Masalah Sebelumnya:
❌ **Biaya pengiriman tetap** - tidak berdasarkan jarak sebenarnya  
❌ **GPS tidak digunakan** untuk perhitungan real-time  
❌ **Lokasi tidak akurat** - menggunakan nilai default  
❌ **Tidak ada feedback** kepada user tentang jarak dan estimasi waktu  

### Solusi yang Diimplementasikan:
✅ **Perhitungan biaya berdasarkan GPS** real-time  
✅ **API endpoint** untuk menghitung jarak dan biaya  
✅ **Fallback system** jika GPS tidak tersedia  
✅ **UI yang informatif** dengan jarak dan estimasi waktu  

## Komponen Sistem

### 1. **API Endpoint** (`api/calculate_delivery.php`)
```php
POST /api/calculate_delivery.php
Content-Type: application/json

{
    "restaurant_id": 1,
    "user_lat": -6.2088,
    "user_lng": 106.8456
}
```

**Response:**
```json
{
    "success": true,
    "distance": 5.2,
    "delivery_fee": {
        "rupiah": 9400,
        "units": 0.6267,
        "formatted": "Rp9.400"
    },
    "estimated_time": 31,
    "restaurant": {
        "name": "Warung Makan Sederhana",
        "coordinates": {"lat": -6.1944, "lng": 106.8229}
    }
}
```

### 2. **Fungsi Perhitungan Biaya** (`includes/functions.php`)

#### **calculateDeliveryFee($distance)**
```php
function calculateDeliveryFee($distance) {
    $fee = 5000;                    // Base fee Rp5.000
    if ($distance > 3) {
        $fee += ($distance - 3) * 2000;  // +Rp2.000 per km setelah 3km
    }
    $fee = max($fee, 3000);         // Minimum Rp3.000
    $fee = min($fee, 25000);        // Maximum Rp25.000
    return $fee;
}
```

#### **Contoh Perhitungan:**
- **0-3 km**: Rp5.000 (tarif dasar)
- **5 km**: Rp5.000 + (5-3) × Rp2.000 = **Rp9.000**
- **10 km**: Rp5.000 + (10-3) × Rp2.000 = **Rp19.000**
- **15 km**: Rp5.000 + (15-3) × Rp2.000 = **Rp25.000** (maksimum)

### 3. **Frontend GPS Integration** (`checkout.php`)

#### **Fitur GPS:**
- **Auto-detection** lokasi user saat checkout
- **Manual refresh** dengan tombol GPS
- **Loading indicator** saat menghitung
- **Error handling** jika GPS gagal
- **Fallback** ke tarif standar

#### **UI Components:**
```html
<div class="d-flex justify-content-between mb-2">
    <span>Biaya Pengiriman:</span>
    <div class="text-end">
        <span id="delivery-fee">Rp15.000</span>
        <button onclick="calculateDeliveryFeeByGPS()">
            <i class="fas fa-location-arrow"></i>
        </button>
    </div>
</div>
```

## Cara Kerja Sistem

### 1. **Saat User Membuka Checkout:**
1. Sistem meminta izin GPS dari browser
2. Jika diizinkan, ambil koordinat user
3. Panggil API untuk menghitung jarak ke restoran
4. Update biaya pengiriman secara real-time
5. Tampilkan jarak dan estimasi waktu

### 2. **Jika GPS Tidak Tersedia:**
1. Gunakan tarif standar (Rp15.000)
2. Tampilkan pesan "GPS tidak tersedia"
3. User masih bisa melanjutkan checkout

### 3. **Perhitungan Jarak:**
- Menggunakan **Haversine formula** untuk jarak great-circle
- Akurat untuk jarak pendek-menengah
- Tidak mempertimbangkan rute jalan (straight-line distance)

## File yang Dimodifikasi

### **Backend:**
1. **`includes/functions.php`**
   - Fungsi `calculateDeliveryFee()` diperbaiki
   - Tambah `calculateDeliveryFeeInUnits()`
   - Fungsi `getDistance()` sudah ada

2. **`api/calculate_delivery.php`** (baru)
   - Endpoint untuk perhitungan real-time
   - Validasi input dan error handling
   - Response format JSON

3. **`checkout.php`**
   - Integrasi perhitungan berdasarkan koordinat
   - Fallback ke tarif default jika koordinat tidak ada

### **Frontend:**
1. **`checkout.php` (JavaScript)**
   - Fungsi `calculateDeliveryFeeByGPS()`
   - Fungsi `updateTotal()`
   - GPS geolocation handling
   - UI updates dan error handling

## Testing

### **Manual Testing:**
1. **Buka**: `http://serverku.local/kikazen_ship/test_gps_delivery.php`
2. **Test GPS**: Klik "Dapatkan Lokasi GPS"
3. **Test Calculation**: Pilih restoran dan hitung biaya
4. **Test Manual**: Input jarak manual untuk verifikasi formula

### **Browser Testing:**
1. **Chrome/Firefox**: GPS biasanya bekerja dengan baik
2. **Safari**: Mungkin perlu HTTPS untuk GPS
3. **Mobile**: GPS lebih akurat di perangkat mobile

### **Error Scenarios:**
- GPS diblokir user → Fallback ke tarif standar
- Koordinat restoran tidak ada → Gunakan default Jakarta
- Network error → Tampilkan pesan error
- Invalid input → Validasi dan error response

## Konfigurasi

### **GPS Options:**
```javascript
const options = {
    enableHighAccuracy: true,    // Gunakan GPS, bukan WiFi/cell tower
    timeout: 10000,             // 10 detik timeout
    maximumAge: 300000          // Cache 5 menit
};
```

### **Delivery Fee Formula:**
```php
// Dapat disesuaikan di includes/functions.php
$base_fee = 5000;           // Tarif dasar
$free_distance = 3;         // Jarak gratis (km)
$per_km_fee = 2000;         // Tarif per km tambahan
$min_fee = 3000;            // Minimum
$max_fee = 25000;           // Maximum
```

## Troubleshooting

### **GPS Tidak Bekerja:**
1. **Periksa HTTPS**: Beberapa browser memerlukan HTTPS untuk GPS
2. **Izin Browser**: Pastikan user mengizinkan akses lokasi
3. **Timeout**: Tingkatkan timeout jika koneksi lambat

### **Biaya Tidak Update:**
1. **Console Error**: Periksa browser console untuk error JavaScript
2. **API Response**: Test API endpoint secara langsung
3. **Koordinat Restoran**: Pastikan restoran memiliki koordinat yang valid

### **Jarak Tidak Akurat:**
1. **Haversine vs Road Distance**: Sistem menggunakan jarak lurus, bukan rute jalan
2. **Koordinat Precision**: Pastikan koordinat akurat hingga 6-8 desimal
3. **Alternative**: Bisa diintegrasikan dengan Google Maps Distance Matrix API

## Pengembangan Selanjutnya

### **Peningkatan yang Bisa Dilakukan:**
1. **Google Maps Integration**: Untuk rute jalan yang akurat
2. **Traffic Consideration**: Perhitungan berdasarkan kondisi lalu lintas
3. **Zone-based Pricing**: Tarif berbeda untuk zona berbeda
4. **Dynamic Pricing**: Tarif berubah berdasarkan waktu/demand
5. **Driver Location**: Perhitungan berdasarkan lokasi driver terdekat

### **Optimisasi:**
1. **Caching**: Cache hasil perhitungan untuk koordinat yang sama
2. **Batch Calculation**: Hitung multiple restoran sekaligus
3. **Background Updates**: Update lokasi secara berkala
4. **Offline Support**: Simpan tarif untuk area yang sering diakses

## Status Implementasi

✅ **GPS Location Detection** - Implemented  
✅ **Real-time Distance Calculation** - Implemented  
✅ **Dynamic Delivery Fee** - Implemented  
✅ **Fallback System** - Implemented  
✅ **User Interface** - Implemented  
✅ **Error Handling** - Implemented  
✅ **Testing Tools** - Implemented  

**Sistem GPS dan perhitungan biaya pengiriman sekarang sudah berfungsi dengan baik dan memberikan tarif yang akurat berdasarkan jarak sebenarnya!**
