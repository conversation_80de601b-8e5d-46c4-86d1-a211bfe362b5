<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GPS Permission Handling</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-box {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .status-success { border-color: #28a745; background-color: #d4edda; }
        .status-warning { border-color: #ffc107; background-color: #fff3cd; }
        .status-error { border-color: #dc3545; background-color: #f8d7da; }
        .status-info { border-color: #17a2b8; background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test GPS Permission Handling</h2>
        <p class="text-muted">Test berbagai skenario GPS permission untuk memastikan user experience yang baik.</p>
        
        <div class="row">
            <div class="col-md-8">
                <!-- GPS Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-satellite-dish"></i> GPS Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="gps-status" class="status-box status-info">
                            <i class="fas fa-info-circle"></i> Klik tombol di bawah untuk test GPS
                        </div>
                        
                        <div class="btn-group w-100 mb-3" role="group">
                            <button class="btn btn-primary" onclick="testGPS()">
                                <i class="fas fa-location-arrow"></i> Test GPS
                            </button>
                            <button class="btn btn-secondary" onclick="simulatePermissionDenied()">
                                <i class="fas fa-ban"></i> Simulasi Permission Denied
                            </button>
                            <button class="btn btn-warning" onclick="simulateTimeout()">
                                <i class="fas fa-clock"></i> Simulasi Timeout
                            </button>
                        </div>
                        
                        <div id="gps-details" style="display: none;">
                            <h6>Detail GPS:</h6>
                            <table class="table table-sm">
                                <tr><td>Latitude:</td><td id="gps-lat">-</td></tr>
                                <tr><td>Longitude:</td><td id="gps-lng">-</td></tr>
                                <tr><td>Akurasi:</td><td id="gps-accuracy">-</td></tr>
                                <tr><td>Timestamp:</td><td id="gps-timestamp">-</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Location Test -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-map-marker-alt"></i> Manual Location Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Test pemilihan lokasi manual sebagai alternatif GPS:</p>
                        
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-outline-secondary w-100" onclick="testManualLocation(-6.2088, 106.8456, 'Jakarta Pusat')">
                                    <i class="fas fa-building"></i> Jakarta Pusat
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-outline-secondary w-100" onclick="testManualLocation(-6.1751, 106.8650, 'Jakarta Timur')">
                                    <i class="fas fa-home"></i> Jakarta Timur
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-outline-secondary w-100" onclick="testManualLocation(-6.2297, 106.7663, 'Jakarta Barat')">
                                    <i class="fas fa-store"></i> Jakarta Barat
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-outline-secondary w-100" onclick="testManualLocation(-6.2615, 106.8106, 'Jakarta Selatan')">
                                    <i class="fas fa-university"></i> Jakarta Selatan
                                </button>
                            </div>
                        </div>
                        
                        <div id="manual-location-result" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Delivery Calculation Test -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator"></i> Delivery Calculation Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Restaurant ID:</label>
                            <select class="form-select" id="test-restaurant">
                                <option value="">Pilih restoran...</option>
                                <?php
                                require_once 'config/database.php';
                                $conn = connectDB();
                                $stmt = $conn->prepare("SELECT restaurant_id, name FROM restaurants ORDER BY name LIMIT 10");
                                $stmt->execute();
                                $restaurants = $stmt->fetchAll();
                                
                                foreach ($restaurants as $restaurant) {
                                    echo "<option value='{$restaurant['restaurant_id']}'>";
                                    echo htmlspecialchars($restaurant['name']);
                                    echo "</option>";
                                }
                                ?>
                            </select>
                        </div>
                        
                        <button class="btn btn-success" onclick="testDeliveryCalculation()" disabled id="test-delivery-btn">
                            <i class="fas fa-shipping-fast"></i> Test Delivery Calculation
                        </button>
                        
                        <div id="delivery-calculation-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- Browser Support -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-browser"></i> Browser Support</h5>
                    </div>
                    <div class="card-body">
                        <div id="browser-support"></div>
                    </div>
                </div>
                
                <!-- Permission Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Permission Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="permission-status"></div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="checkPermissions()">
                            <i class="fas fa-sync"></i> Check Permissions
                        </button>
                    </div>
                </div>
                
                <!-- Test Log -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Test Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.8em; background: #f8f9fa; padding: 10px; border-radius: 4px;"></div>
                        <button class="btn btn-sm btn-secondary mt-2" onclick="clearLog()">Clear Log</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentLat, currentLng;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkBrowserSupport();
            checkPermissions();
            
            // Enable delivery test when restaurant is selected
            document.getElementById('test-restaurant').addEventListener('change', function() {
                document.getElementById('test-delivery-btn').disabled = !this.value;
            });
            
            logMessage('GPS Permission Test initialized');
        });
        
        // Check browser support
        function checkBrowserSupport() {
            const supportDiv = document.getElementById('browser-support');
            let html = '';
            
            if (navigator.geolocation) {
                html += '<div class="alert alert-success"><i class="fas fa-check"></i> Geolocation API supported</div>';
            } else {
                html += '<div class="alert alert-danger"><i class="fas fa-times"></i> Geolocation API not supported</div>';
            }
            
            if (navigator.permissions) {
                html += '<div class="alert alert-success"><i class="fas fa-check"></i> Permissions API supported</div>';
            } else {
                html += '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Permissions API not supported</div>';
            }
            
            if (window.isSecureContext) {
                html += '<div class="alert alert-success"><i class="fas fa-lock"></i> Secure context (HTTPS)</div>';
            } else {
                html += '<div class="alert alert-warning"><i class="fas fa-unlock"></i> Not secure context (HTTP)</div>';
            }
            
            supportDiv.innerHTML = html;
        }
        
        // Check permissions
        function checkPermissions() {
            const statusDiv = document.getElementById('permission-status');
            
            if (!navigator.permissions) {
                statusDiv.innerHTML = '<div class="alert alert-warning">Permissions API not available</div>';
                return;
            }
            
            navigator.permissions.query({name: 'geolocation'}).then(function(result) {
                let alertClass = '';
                let icon = '';
                let message = '';
                
                switch(result.state) {
                    case 'granted':
                        alertClass = 'alert-success';
                        icon = 'fas fa-check';
                        message = 'Location permission granted';
                        break;
                    case 'denied':
                        alertClass = 'alert-danger';
                        icon = 'fas fa-ban';
                        message = 'Location permission denied';
                        break;
                    case 'prompt':
                        alertClass = 'alert-info';
                        icon = 'fas fa-question';
                        message = 'Location permission will be prompted';
                        break;
                }
                
                statusDiv.innerHTML = `<div class="alert ${alertClass}"><i class="${icon}"></i> ${message}</div>`;
                logMessage(`Permission status: ${result.state}`);
            }).catch(function(error) {
                statusDiv.innerHTML = '<div class="alert alert-warning">Could not check permissions</div>';
                logMessage(`Permission check error: ${error.message}`);
            });
        }
        
        // Test GPS
        function testGPS() {
            const statusDiv = document.getElementById('gps-status');
            const detailsDiv = document.getElementById('gps-details');
            
            statusDiv.className = 'status-box status-info';
            statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting GPS location...';
            detailsDiv.style.display = 'none';
            
            logMessage('Starting GPS test...');
            
            const options = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
            };
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    currentLat = position.coords.latitude;
                    currentLng = position.coords.longitude;
                    
                    statusDiv.className = 'status-box status-success';
                    statusDiv.innerHTML = '<i class="fas fa-check"></i> GPS location obtained successfully!';
                    
                    // Update details
                    document.getElementById('gps-lat').textContent = currentLat.toFixed(8);
                    document.getElementById('gps-lng').textContent = currentLng.toFixed(8);
                    document.getElementById('gps-accuracy').textContent = Math.round(position.coords.accuracy) + ' meters';
                    document.getElementById('gps-timestamp').textContent = new Date(position.timestamp).toLocaleString();
                    detailsDiv.style.display = 'block';
                    
                    logMessage(`GPS Success: ${currentLat.toFixed(6)}, ${currentLng.toFixed(6)} (±${Math.round(position.coords.accuracy)}m)`);
                },
                function(error) {
                    statusDiv.className = 'status-box status-error';
                    
                    let errorMessage = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Permission denied by user';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Position unavailable';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'Request timeout';
                            break;
                        default:
                            errorMessage = 'Unknown error';
                            break;
                    }
                    
                    statusDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> GPS Error: ${errorMessage}`;
                    logMessage(`GPS Error: ${errorMessage} (code: ${error.code})`);
                },
                options
            );
        }
        
        // Simulate permission denied
        function simulatePermissionDenied() {
            const statusDiv = document.getElementById('gps-status');
            statusDiv.className = 'status-box status-error';
            statusDiv.innerHTML = '<i class="fas fa-ban"></i> Simulated: Permission denied by user';
            logMessage('Simulated: Permission denied');
        }
        
        // Simulate timeout
        function simulateTimeout() {
            const statusDiv = document.getElementById('gps-status');
            statusDiv.className = 'status-box status-warning';
            statusDiv.innerHTML = '<i class="fas fa-clock"></i> Simulated: GPS request timeout';
            logMessage('Simulated: GPS timeout');
        }
        
        // Test manual location
        function testManualLocation(lat, lng, name) {
            const resultDiv = document.getElementById('manual-location-result');
            
            currentLat = lat;
            currentLng = lng;
            
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-map-marker-alt"></i> Manual Location Set</h6>
                    <p><strong>Location:</strong> ${name}</p>
                    <p><strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                </div>
            `;
            
            logMessage(`Manual location set: ${name} (${lat.toFixed(6)}, ${lng.toFixed(6)})`);
        }
        
        // Test delivery calculation
        function testDeliveryCalculation() {
            const restaurantSelect = document.getElementById('test-restaurant');
            const resultDiv = document.getElementById('delivery-calculation-result');
            
            if (!restaurantSelect.value) {
                alert('Select a restaurant first');
                return;
            }
            
            if (!currentLat || !currentLng) {
                alert('Get GPS location or set manual location first');
                return;
            }
            
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Calculating delivery fee...</div>';
            
            logMessage(`Testing delivery calculation for restaurant ${restaurantSelect.value}`);
            
            fetch('api/calculate_delivery.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    restaurant_id: restaurantSelect.value,
                    user_lat: currentLat,
                    user_lng: currentLng
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check"></i> Delivery Calculation Success</h6>
                            <p><strong>Distance:</strong> ${data.distance} km</p>
                            <p><strong>Delivery Fee:</strong> ${data.delivery_fee.formatted}</p>
                            <p><strong>Estimated Time:</strong> ${data.estimated_time} minutes</p>
                            <small class="text-muted">Restaurant: ${data.restaurant.name}</small>
                        </div>
                    `;
                    logMessage(`Delivery calculation success: ${data.distance}km = ${data.delivery_fee.formatted}`);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${data.error}</div>`;
                    logMessage(`Delivery calculation error: ${data.error}`);
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Network Error: ${error.message}</div>`;
                logMessage(`Network error: ${error.message}`);
            });
        }
        
        // Log messages
        function logMessage(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
    </script>
</body>
</html>
