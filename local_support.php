<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);

// Get user name if logged in
$userName = '';
if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}

// Connect to database
$conn = connectDB();

// Get local restaurants count
$stmt = $conn->prepare("
    SELECT COUNT(*) as local_count
    FROM restaurants
    WHERE is_local = 1
");
$stmt->execute();
$localRestaurants = $stmt->fetch(PDO::FETCH_ASSOC)['local_count'];

// Get local restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address, r.sustainability_description
    FROM restaurants r
    WHERE r.is_local = 1
    ORDER BY r.name
    LIMIT 6
");
$stmt->execute();
$restaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get donation program stats
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT d.donation_id) as total_donations,
        SUM(d.amount) as total_amount,
        COUNT(DISTINCT d.user_id) as unique_donors
    FROM donations d
    WHERE d.donation_type = 'food_bank'
");
$stmt->execute();
$donations = $stmt->fetch(PDO::FETCH_ASSOC);

// Get donation organizations
$stmt = $conn->prepare("
    SELECT recipient_organization, COUNT(*) as donation_count, SUM(amount) as total_amount
    FROM donations
    WHERE donation_type = 'food_bank'
    GROUP BY recipient_organization
    ORDER BY total_amount DESC
    LIMIT 3
");
$stmt->execute();
$organizations = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get upcoming community events
$stmt = $conn->prepare("
    SELECT *
    FROM community_events
    WHERE event_date >= CURDATE() AND event_type IN ('food_drive', 'community_support')
    ORDER BY event_date
    LIMIT 3
");
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dukungan Komunitas Lokal - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
        .local-icon {
            font-size: 3rem;
            color: #fd7e14;
            margin-bottom: 1rem;
        }
        
        .local-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s;
            height: 100%;
        }
        
        .local-card:hover {
            transform: translateY(-5px);
        }
        
        .hero-local {
            background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/local-community-hero.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 5rem 0;
            margin-bottom: 3rem;
        }
        
        .local-badge {
            background-color: #fd7e14;
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .donation-counter {
            background-color: #f8f9fa;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .counter-value {
            font-size: 3rem;
            font-weight: bold;
            color: #fd7e14;
        }
        
        .organization-card {
            border-left: 4px solid #fd7e14;
        }
        
        .event-date {
            background-color: #fd7e14;
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-weight: bold;
            text-align: center;
            width: 60px;
        }
        
        .event-month {
            font-size: 0.8rem;
            text-transform: uppercase;
        }
        
        .event-day {
            font-size: 1.5rem;
            line-height: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="driver/orders.php">Pengiriman</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="owner/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Masuk</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Daftar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-local">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Dukungan Komunitas Lokal</h1>
            <p class="lead mb-4">Mendukung bisnis lokal dan membantu mereka yang membutuhkan di komunitas kita.</p>
            <a href="#local-restaurants" class="btn btn-warning btn-lg">
                <i class="fas fa-store me-2"></i>Jelajahi Restoran Lokal
            </a>
        </div>
    </section>

    <!-- Content -->
    <div class="container py-5">
        <!-- Local Restaurants -->
        <section class="mb-5" id="local-restaurants">
            <h2 class="text-center mb-4">Restoran Lokal</h2>
            <p class="text-center text-muted mb-5">Dukung <?= $localRestaurants ?> bisnis lokal di platform kami dan bantu ekonomi lokal berkembang.</p>
            
            <div class="row row-cols-1 row-cols-md-3 g-4">
                <?php foreach ($restaurants as $restaurant): ?>
                    <div class="col">
                        <div class="card h-100 local-card">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="local-badge"><i class="fas fa-store me-1"></i>Lokal</span>
                            </div>
                            <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" class="card-img-top" alt="<?= $restaurant['name'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                <p class="card-text">
                                    <i class="fas fa-utensils me-2"></i><?= $restaurant['cuisine_type'] ?>
                                </p>
                                <p class="card-text">
                                    <i class="fas fa-map-marker-alt me-2"></i><?= $restaurant['address'] ?>
                                </p>
                                <?php if (!empty($restaurant['sustainability_description'])): ?>
                                    <p class="card-text small text-muted mt-2"><?= $restaurant['sustainability_description'] ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-warning w-100">
                                    <i class="fas fa-utensils me-2"></i>Lihat Menu
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="local_restaurants.php" class="btn btn-outline-warning">
                    <i class="fas fa-list me-2"></i>Lihat Semua Restoran Lokal
                </a>
            </div>
        </section>

        <!-- Food Donation Program -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Program Donasi Makanan</h2>
            <p class="text-center text-muted mb-5">Melalui program "Berbagi Makanan", Anda dapat membantu menyediakan makanan untuk mereka yang membutuhkan.</p>
            
            <div class="donation-counter mb-5">
                <div class="row">
                    <div class="col-md-4">
                        <div class="counter-value">Rp <?= number_format($donations['total_amount']) ?></div>
                        <p>Total donasi terkumpul</p>
                    </div>
                    <div class="col-md-4">
                        <div class="counter-value"><?= number_format($donations['total_donations']) ?></div>
                        <p>Donasi telah dilakukan</p>
                    </div>
                    <div class="col-md-4">
                        <div class="counter-value"><?= number_format($donations['unique_donors']) ?></div>
                        <p>Pengguna telah berpartisipasi</p>
                    </div>
                </div>
            </div>
            
            <div class="row mb-5">
                <div class="col-md-6">
                    <img src="images/food-donation.jpg" alt="Program Donasi Makanan" class="img-fluid rounded">
                </div>
                <div class="col-md-6">
                    <h3><i class="fas fa-hand-holding-heart text-warning me-2"></i>Bagaimana Cara Kerjanya?</h3>
                    <p>Saat memesan, Anda akan melihat opsi untuk menambahkan donasi ke pesanan Anda. Setiap donasi Rp 5.000 dapat menyediakan satu porsi makanan untuk mereka yang membutuhkan melalui bank makanan lokal dan panti asuhan.</p>
                    
                    <div class="alert alert-warning mt-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle fa-2x me-3"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Dampak Anda</h5>
                                <p class="mb-0">100% dari donasi Anda langsung digunakan untuk menyediakan makanan. KikaZen Ship tidak mengambil biaya administrasi apa pun dari program donasi ini.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mt-4">Mitra Bank Makanan Kami</h5>
                    <div class="row mt-3">
                        <?php foreach ($organizations as $org): ?>
                            <div class="col-md-12 mb-3">
                                <div class="card organization-card">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= $org['recipient_organization'] ?></h6>
                                        <p class="card-text small">
                                            Telah menerima Rp <?= number_format($org['total_amount']) ?> dari <?= number_format($org['donation_count']) ?> donasi
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <a href="donate.php" class="btn btn-warning btn-lg">
                    <i class="fas fa-donate me-2"></i>Donasi Sekarang
                </a>
            </div>
        </section>

        <!-- Community Events -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Acara Komunitas Mendatang</h2>
            <p class="text-center text-muted mb-5">Bergabunglah dengan kami dalam acara-acara yang mendukung komunitas lokal.</p>
            
            <div class="row">
                <?php if (empty($events)): ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">Tidak ada acara yang akan datang saat ini. Silakan periksa kembali nanti.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($events as $event): ?>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex mb-3">
                                        <?php 
                                            $eventDate = new DateTime($event['event_date']);
                                            $month = $eventDate->format('M');
                                            $day = $eventDate->format('d');
                                        ?>
                                        <div class="event-date me-3">
                                            <div class="event-month"><?= $month ?></div>
                                            <div class="event-day"><?= $day ?></div>
                                        </div>
                                        <div>
                                            <h5 class="card-title"><?= $event['title'] ?></h5>
                                            <p class="card-text text-muted">
                                                <i class="fas fa-map-marker-alt me-2"></i><?= $event['location'] ?>
                                            </p>
                                        </div>
                                    </div>
                                    <p class="card-text"><?= $event['description'] ?></p>
                                    <a href="event_detail.php?id=<?= $event['event_id'] ?>" class="btn btn-outline-warning">
                                        <i class="fas fa-info-circle me-2"></i>Detail Acara
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="community_events.php" class="btn btn-outline-warning">
                    <i class="fas fa-calendar-alt me-2"></i>Lihat Semua Acara
                </a>
            </div>
        </section>

        <!-- Become a Local Partner -->
        <section class="mb-5">
            <div class="card bg-light">
                <div class="card-body p-5">
                    <div class="row">
                        <div class="col-md-8">
                            <h2 class="mb-4">Apakah Anda Memiliki Restoran Lokal?</h2>
                            <p class="lead mb-4">Bergabunglah dengan KikaZen Ship sebagai mitra restoran lokal dan dapatkan keuntungan dari program dukungan khusus kami.</p>
                            
                            <h5 class="mt-4"><i class="fas fa-check-circle text-warning me-2"></i>Keuntungan untuk Restoran Lokal</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-angle-right me-2"></i>Biaya komisi yang lebih rendah dibandingkan restoran non-lokal</li>
                                <li class="mb-2"><i class="fas fa-angle-right me-2"></i>Visibilitas khusus di platform kami dengan badge "Lokal"</li>
                                <li class="mb-2"><i class="fas fa-angle-right me-2"></i>Dukungan pemasaran gratis untuk mempromosikan bisnis Anda</li>
                                <li class="mb-2"><i class="fas fa-angle-right me-2"></i>Akses ke program pelatihan dan sumber daya untuk mengembangkan bisnis Anda</li>
                            </ul>
                            
                            <div class="mt-4">
                                <a href="become_partner.php" class="btn btn-warning">
                                    <i class="fas fa-handshake me-2"></i>Menjadi Mitra
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-center justify-content-center">
                            <i class="fas fa-store local-icon" style="font-size: 8rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat dan bertanggung jawab.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="sustainability.php" class="text-white">Keberlanjutan</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
                <p class="text-muted small">Dicetak di atas kertas digital untuk menyelamatkan pohon. 🌳</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
