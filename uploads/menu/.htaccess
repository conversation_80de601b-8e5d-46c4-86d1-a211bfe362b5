Options +Indexes
<IfModule mod_php7.c>
    php_flag engine off
</IfModule>
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

<FilesMatch "(?i)\.(jpg|jpeg|png|gif)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

<FilesMatch "(?i)\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|aspx|cgi|exe|sh)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent directory listing
Options -Indexes

# Allow direct access to image files
<Files ~ "\.(?i:jpe?g|png|gif)$">
    Order Allow,Deny
    Allow from all
</Files>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
</IfModule>

# Enable CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
</IfModule>
