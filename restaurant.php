<?php
// Start session
session_start();

// Include database connection
require_once 'config/database.php';
$conn = connectDB();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['admin_id']);
$userType = $_SESSION['user_type'] ?? '';

// Get restaurant ID from URL
$restaurant_id = $_GET['id'] ?? 0;

// Get restaurant details
$stmt = $conn->prepare("SELECT * FROM restaurants WHERE restaurant_id = :id");
$stmt->bindParam(':id', $restaurant_id);
$stmt->execute();

if ($stmt->rowCount() === 0) {
    // Restaurant not found, redirect to restaurants page
    header('Location: restaurants.php');
    exit;
}

$restaurant = $stmt->fetch();

// Get restaurant categories
$stmt = $conn->prepare("
    SELECT DISTINCT c.*
    FROM categories c
    JOIN menu_items m ON c.category_id = m.category_id
    WHERE m.restaurant_id = :restaurant_id
");
$stmt->bindParam(':restaurant_id', $restaurant_id);
$stmt->execute();
$categories = $stmt->fetchAll();

// Get menu items
$stmt = $conn->prepare("
    SELECT m.*, c.name as category_name
    FROM menu_items m
    LEFT JOIN categories c ON m.category_id = c.category_id
    WHERE m.restaurant_id = :restaurant_id AND m.is_available = 1
    ORDER BY m.category_id, m.name
");
$stmt->bindParam(':restaurant_id', $restaurant_id);
$stmt->execute();
$menuItems = $stmt->fetchAll();

// Group menu items by category
$menuByCategory = [];
foreach ($menuItems as $item) {
    $categoryId = $item['category_id'] ?? 0;
    $categoryName = $item['category_name'] ?? 'Uncategorized';

    if (!isset($menuByCategory[$categoryId])) {
        $menuByCategory[$categoryId] = [
            'name' => $categoryName,
            'items' => []
        ];
    }

    $menuByCategory[$categoryId]['items'][] = $item;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $restaurant['name'] ?> - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="restaurants.php">Restoran</a>
                    </li>
                    <?php if ($isLoggedIn && $userType === 'customer'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user/dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="user/orders.php">Pesanan Saya</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'driver'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="driver/dashboard.php">Dasbor Pengemudi</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/dashboard.php">Dasbor Admin</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'owner'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="owner/dashboard.php">Dasbor Restoran</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="offcanvas" data-bs-target="#cartOffcanvas">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="badge bg-danger rounded-pill" id="cart-count" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['user_name'] ?? $_SESSION['driver_name'] ?? $_SESSION['admin_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php if ($userType === 'customer'): ?>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan Saya</a></li>
                                <li><a class="dropdown-item" href="user/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'driver'): ?>
                                <li><a class="dropdown-item" href="driver/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="driver/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'admin'): ?>
                                <li><a class="dropdown-item" href="admin/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="admin/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'owner'): ?>
                                <li><a class="dropdown-item" href="owner/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="owner/menu.php">Menu</a></li>
                                <li><a class="dropdown-item" href="owner/chat.php">Chat</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light me-2" href="login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Masuk
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-light text-primary" href="register.php">
                            <i class="fas fa-user-plus me-1"></i>Daftar
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Restaurant Header -->
    <div class="restaurant-header position-relative">
        <img src="<?= $restaurant['banner_url'] ?? 'assets/restaurant-placeholder.png' ?>" alt="<?= $restaurant['name'] ?>" class="w-100" style="height: 250px; object-fit: cover;">
        <div class="container position-relative" style="margin-top: -60px;">
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex align-items-end">
                        <?php if (!empty($restaurant['logo_url'])): ?>
                            <img src="<?= $restaurant['logo_url'] ?>" alt="Logo" class="rounded-circle border border-3 border-white" width="100" height="100" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded-circle border border-3 border-white d-flex align-items-center justify-content-center bg-primary text-white" style="width: 100px; height: 100px; font-size: 2rem; font-weight: bold;"><?= substr($restaurant['name'], 0, 1) ?></div>
                        <?php endif; ?>
                        <div class="ms-3 bg-white p-3 rounded shadow-sm">
                            <h1 class="h3 mb-1"><?= $restaurant['name'] ?></h1>
                            <p class="text-muted mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i> <?= $restaurant['address'] ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end justify-content-end">
                    <div class="bg-white p-3 rounded shadow-sm text-center">
                        <div class="d-flex justify-content-between mb-2">
                            <span><i class="fas fa-star text-warning me-1"></i> <?= number_format($restaurant['rating'], 1) ?></span>
                            <span class="ms-3"><i class="fas fa-truck text-primary me-1"></i> Rp<?= number_format($restaurant['delivery_fee'] * 15000, 0, ',', '.') ?></span>
                        </div>
                        <div class="small text-muted">
                            <?php if ($restaurant['is_open']): ?>
                                <span class="text-success"><i class="fas fa-check-circle me-1"></i> Buka Sekarang</span>
                            <?php else: ?>
                                <span class="text-danger"><i class="fas fa-times-circle me-1"></i> Tutup</span>
                            <?php endif; ?>
                            <span class="ms-2"><?= $restaurant['opening_time'] ? date('g:i A', strtotime($restaurant['opening_time'])) : '9:00 AM' ?> - <?= $restaurant['closing_time'] ? date('g:i A', strtotime($restaurant['closing_time'])) : '10:00 PM' ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Restaurant Menu -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Categories Sidebar -->
                <div class="col-md-3 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Kategori</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <?php foreach ($categories as $category): ?>
                            <a href="#category-<?= $category['category_id'] ?>" class="list-group-item list-group-item-action">
                                <?= $category['name'] ?>
                            </a>
                            <?php endforeach; ?>
                            <?php if (isset($menuByCategory[0])): ?>
                            <a href="#category-0" class="list-group-item list-group-item-action">
                                Uncategorized
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card shadow-sm mt-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Informasi Restoran</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><i class="fas fa-phone text-primary me-2"></i> <?= $restaurant['phone'] ?></p>
                            <p class="mb-2"><i class="fas fa-envelope text-primary me-2"></i> <?= $restaurant['email'] ?></p>
                            <p class="mb-0"><i class="fas fa-info-circle text-primary me-2"></i> <?= $restaurant['description'] ?? 'Tidak ada deskripsi tersedia.' ?></p>
                        </div>
                    </div>
                </div>

                <!-- Menu Items -->
                <div class="col-md-9">
                    <?php if (empty($menuItems)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                        <h3>Belum ada menu tersedia</h3>
                        <p class="text-muted">Restoran ini belum menambahkan menu apapun.</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($menuByCategory as $categoryId => $category): ?>
                        <div id="category-<?= $categoryId ?>" class="mb-4">
                            <h2 class="h4 mb-3 pb-2 border-bottom"><?= $category['name'] ?></h2>
                            <div class="row row-cols-1 row-cols-md-2 g-4">
                                <?php foreach ($category['items'] as $item): ?>
                                <div class="col">
                                    <div class="card menu-item-card h-100 shadow-sm">
                                        <div class="row g-0">
                                            <div class="col-4">
                                                <img src="<?= $item['image_url'] ?? 'assets/restaurant-placeholder.png' ?>" class="img-fluid rounded-start h-100" alt="<?= $item['name'] ?>" style="object-fit: cover;">
                                            </div>
                                            <div class="col-8">
                                                <div class="card-body">
                                                    <h5 class="card-title"><?= $item['name'] ?></h5>
                                                    <p class="card-text small">
                                                        <?= substr($item['description'] ?? 'Tidak ada deskripsi tersedia.', 0, 80) ?>...
                                                    </p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span class="fw-bold">Rp<?= number_format($item['price'] * 15000, 0, ',', '.') ?></span>
                                                        <button class="btn btn-sm btn-primary add-to-cart"
                                                                data-item-id="<?= $item['item_id'] ?>"
                                                                data-item-name="<?= $item['name'] ?>"
                                                                data-item-price="<?= $item['price'] ?>"
                                                                data-restaurant-id="<?= $restaurant_id ?>">
                                                            <i class="fas fa-plus me-1"></i> Tambah
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Offcanvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="cartOffcanvas" aria-labelledby="cartOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="cartOffcanvasLabel">Keranjang Belanja</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div id="cart-items">
                <!-- Cart items will be dynamically added here -->
                <p class="text-center">Keranjang belanja Anda kosong</p>
            </div>
            <hr>
            <div class="d-flex justify-content-between mb-3">
                <span class="fw-bold">Total:</span>
                <span class="fw-bold" id="cart-total">Rp0</span>
            </div>
            <div class="d-grid gap-2">
                <button class="btn btn-primary" id="checkout-btn" onclick="window.location.href='checkout.php'">Lanjut ke Pembayaran</button>
                <button class="btn btn-outline-secondary" id="clear-cart">Kosongkan Keranjang</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="position-fixed bottom-0 end-0 p-3"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="js/script.js"></script>
    <script>
        // Initialize AOS animation
        AOS.init({
            once: true,
            disable: 'mobile'
        });
    </script>
</body>
</html>
