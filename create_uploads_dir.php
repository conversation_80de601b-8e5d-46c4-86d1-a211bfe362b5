<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Direktori yang akan dibuat
$directories = [
    'uploads',
    'uploads/menu',
    'uploads/restaurants',
    'uploads/users',
    'uploads/drivers'
];

echo "<h1>Membuat Direktori Upload</h1>";

foreach ($directories as $dir) {
    echo "<h2>Direktori: $dir</h2>";
    
    $fullPath = __DIR__ . '/' . $dir;
    
    if (!file_exists($fullPath)) {
        echo "Direktori tidak ada. Mencoba membuat...<br>";
        
        if (mkdir($fullPath, 0777, true)) {
            echo "Berhasil membuat direktori.<br>";
        } else {
            echo "Gagal membuat direktori!<br>";
            echo "Error: " . error_get_last()['message'] . "<br>";
        }
    } else {
        echo "Direktori sudah ada.<br>";
    }
    
    // Coba atur izin akses
    echo "Mencoba mengatur izin akses...<br>";
    if (chmod($fullPath, 0777)) {
        echo "Berhasil mengatur izin akses ke 777.<br>";
    } else {
        echo "Gagal mengatur izin akses!<br>";
        echo "Error: " . error_get_last()['message'] . "<br>";
    }
    
    // Cek izin akses
    echo "Izin akses saat ini: " . substr(sprintf('%o', fileperms($fullPath)), -4) . "<br>";
    
    // Cek apakah direktori dapat ditulis
    if (is_writable($fullPath)) {
        echo "Direktori dapat ditulis.<br>";
    } else {
        echo "Direktori tidak dapat ditulis!<br>";
    }
    
    // Coba buat file test
    $testFile = $fullPath . '/test.txt';
    echo "Mencoba membuat file test...<br>";
    
    if (file_put_contents($testFile, 'Test file')) {
        echo "Berhasil membuat file test.<br>";
        
        // Hapus file test
        if (unlink($testFile)) {
            echo "Berhasil menghapus file test.<br>";
        } else {
            echo "Gagal menghapus file test!<br>";
        }
    } else {
        echo "Gagal membuat file test!<br>";
        echo "Error: " . error_get_last()['message'] . "<br>";
    }
    
    echo "<hr>";
}

// Tampilkan informasi server
echo "<h2>Informasi Server</h2>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "PHP User: " . get_current_user() . "<br>";
echo "PHP Version: " . phpversion() . "<br>";

// Cek fungsi yang dinonaktifkan
echo "<h2>Fungsi yang Dinonaktifkan</h2>";
$disabled_functions = ini_get('disable_functions');
if (empty($disabled_functions)) {
    echo "Tidak ada fungsi yang dinonaktifkan.<br>";
} else {
    echo "Fungsi yang dinonaktifkan: " . $disabled_functions . "<br>";
}

// Cek apakah fungsi mkdir dan chmod diizinkan
echo "<h2>Cek Fungsi</h2>";
echo "Fungsi mkdir " . (function_exists('mkdir') ? "tersedia" : "tidak tersedia") . ".<br>";
echo "Fungsi chmod " . (function_exists('chmod') ? "tersedia" : "tidak tersedia") . ".<br>";
echo "Fungsi move_uploaded_file " . (function_exists('move_uploaded_file') ? "tersedia" : "tidak tersedia") . ".<br>";

// Cek konfigurasi upload
echo "<h2>Konfigurasi Upload</h2>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";

// Cek direktori sementara untuk upload
echo "<h2>Direktori Sementara</h2>";
echo "upload_tmp_dir: " . ini_get('upload_tmp_dir') . "<br>";
echo "sys_temp_dir: " . sys_get_temp_dir() . "<br>";

if (!empty(ini_get('upload_tmp_dir'))) {
    $tmp_dir = ini_get('upload_tmp_dir');
    echo "Izin akses direktori sementara: " . substr(sprintf('%o', fileperms($tmp_dir)), -4) . "<br>";
    echo "Direktori sementara " . (is_writable($tmp_dir) ? "dapat ditulis" : "tidak dapat ditulis") . ".<br>";
} else {
    $tmp_dir = sys_get_temp_dir();
    echo "Izin akses direktori sementara sistem: " . substr(sprintf('%o', fileperms($tmp_dir)), -4) . "<br>";
    echo "Direktori sementara sistem " . (is_writable($tmp_dir) ? "dapat ditulis" : "tidak dapat ditulis") . ".<br>";
}
?>
