<?php
// Start session
session_start();

// Check if user is already logged in
if (isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['admin_id']) || isset($_SESSION['owner_id'])) {
    header('Location: index.php');
    exit;
}

// Include auth functions
require_once 'includes/auth.php';

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $address = $_POST['address'] ?? '';
    $userType = $_POST['user_type'] ?? 'customer';

    // Additional fields for driver
    $vehicleType = $_POST['vehicle_type'] ?? '';
    $licensePlate = $_POST['license_plate'] ?? '';

    // Validate inputs
    if (empty($name) || empty($email) || empty($password) || empty($phone)) {
        $error = 'Silakan isi semua bidang yang diperlukan';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Silakan masukkan alamat email yang valid';
    } elseif (strlen($password) < 8) {
        $error = 'Kata sandi harus minimal 8 karakter';
    } elseif ($password !== $confirmPassword) {
        $error = 'Kata sandi tidak cocok';
    } elseif ($userType === 'driver' && empty($vehicleType)) {
        $error = 'Silakan pilih jenis kendaraan';
    } else {
        // Register based on user type
        $result = [];

        switch ($userType) {
            case 'customer':
                $result = registerUser($name, $email, $password, $phone, $address);
                break;
            case 'driver':
                $result = registerDriver($name, $email, $password, $phone, $vehicleType, $licensePlate);
                break;
            case 'owner':
                $result = registerRestaurantOwner($name, $email, $password, $phone, $address);
                break;
            default:
                $error = 'Tipe pengguna tidak valid';
                break;
        }

        if ($result['success']) {
            $success = 'Pendaftaran berhasil! Anda sekarang dapat masuk.';
            // Redirect to login page after 2 seconds
            header('refresh:2;url=login.php');
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">Masuk</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="register.php">Daftar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Register Form -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="text-center mb-4">Buat Akun</h2>

                            <?php if ($error): ?>
                                <div class="alert alert-danger"><?= $error ?></div>
                            <?php endif; ?>

                            <?php if ($success): ?>
                                <div class="alert alert-success"><?= $success ?></div>
                            <?php endif; ?>

                            <form action="register.php" method="post">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">Saya ingin mendaftar sebagai:</label>
                                    <select class="form-select" id="user_type" name="user_type" required onchange="toggleUserTypeFields()">
                                        <option value="customer">Pelanggan</option>
                                        <option value="driver">Pengemudi</option>
                                        <option value="owner">Pemilik Restoran</option>
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Nama Lengkap</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Alamat Email</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">Kata Sandi</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <small class="text-muted">Kata sandi minimal 8 karakter</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">Konfirmasi Kata Sandi</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Nomor Telepon</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="address" class="form-label">Alamat</label>
                                        <input type="text" class="form-control" id="address" name="address">
                                    </div>
                                </div>

                                <!-- Driver-specific fields -->
                                <div id="driver_fields" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="vehicle_type" class="form-label">Jenis Kendaraan</label>
                                            <select class="form-select" id="vehicle_type" name="vehicle_type">
                                                <option value="">Pilih jenis kendaraan</option>
                                                <option value="motorcycle">Motor</option>
                                                <option value="car">Mobil</option>
                                                <option value="bicycle">Sepeda</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="license_plate" class="form-label">Plat Nomor</label>
                                            <input type="text" class="form-control" id="license_plate" name="license_plate">
                                        </div>
                                    </div>
                                </div>

                                <!-- Restaurant Owner-specific fields -->
                                <div id="owner_fields" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> Setelah mendaftar, akun Anda akan ditinjau oleh admin. Anda akan dapat menambahkan restoran Anda setelah akun disetujui.
                                    </div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">Saya menyetujui <a href="#">Syarat dan Ketentuan</a> dan <a href="#">Kebijakan Privasi</a></label>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Daftar</button>
                                </div>
                            </form>

                            <div class="text-center mt-3">
                                <p>Sudah punya akun? <a href="login.php">Masuk di sini</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function toggleUserTypeFields() {
            const userType = document.getElementById('user_type').value;
            const driverFields = document.getElementById('driver_fields');
            const ownerFields = document.getElementById('owner_fields');

            // Hide all fields first
            driverFields.style.display = 'none';
            ownerFields.style.display = 'none';
            document.getElementById('vehicle_type').removeAttribute('required');

            // Show fields based on user type
            if (userType === 'driver') {
                driverFields.style.display = 'block';
                document.getElementById('vehicle_type').setAttribute('required', 'required');
            } else if (userType === 'owner') {
                ownerFields.style.display = 'block';
            }
        }
    </script>
</body>
</html>
