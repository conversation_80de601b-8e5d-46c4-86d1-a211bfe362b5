<?php
// Start session
session_start();

// Include database connection
require_once 'config/database.php';
$conn = connectDB();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['admin_id']) || isset($_SESSION['owner_id']);
$userType = $_SESSION['user_type'] ?? '';

// Get restaurants
$query = "SELECT * FROM restaurants WHERE is_open = 1 ORDER BY name ASC";
$stmt = $conn->prepare($query);
$stmt->execute();
$restaurants = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restoran - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="restaurants.php">Restoran</a>
                    </li>
                    <?php if ($isLoggedIn && $userType === 'customer'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user/dashboard.php">Dasbor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="user/orders.php">Pesanan Saya</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'driver'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="driver/dashboard.php">Dasbor Pengemudi</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/dashboard.php">Dasbor Admin</a>
                    </li>
                    <?php elseif ($isLoggedIn && $userType === 'owner'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="owner/dashboard.php">Dasbor Restoran</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $_SESSION['user_name'] ?? $_SESSION['driver_name'] ?? $_SESSION['admin_name'] ?? $_SESSION['owner_name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php if ($userType === 'customer'): ?>
                                <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="user/orders.php">Pesanan Saya</a></li>
                                <li><a class="dropdown-item" href="user/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'driver'): ?>
                                <li><a class="dropdown-item" href="driver/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="driver/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'admin'): ?>
                                <li><a class="dropdown-item" href="admin/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="admin/chat.php">Chat</a></li>
                            <?php elseif ($userType === 'owner'): ?>
                                <li><a class="dropdown-item" href="owner/profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                <li><a class="dropdown-item" href="owner/menu.php">Menu</a></li>
                                <li><a class="dropdown-item" href="owner/chat.php">Chat</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light me-2" href="login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Masuk
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-light text-primary" href="register.php">
                            <i class="fas fa-user-plus me-1"></i>Daftar
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Restaurants Section -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-md-6" data-aos="fade-right" data-aos-duration="800">
                    <h1 class="mb-2 fw-bold">Restoran</h1>
                    <p class="text-muted">Temukan restoran terbaik di kota Anda</p>
                </div>
                <div class="col-md-6 d-flex align-items-center justify-content-md-end" data-aos="fade-left" data-aos-duration="800">
                    <?php if ($isLoggedIn && $userType === 'owner'): ?>
                    <a href="owner/dashboard.php" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Kelola Restoran Anda
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="row mb-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="Cari restoran berdasarkan nama atau jenis makanan..." id="search-input">
                        <button class="btn btn-primary" type="button" id="search-button">
                            Cari
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                        <select class="form-select w-auto" id="sort-select">
                            <option value="name">Urutkan berdasarkan Nama</option>
                            <option value="rating">Urutkan berdasarkan Rating</option>
                            <option value="delivery_fee">Urutkan berdasarkan Biaya Pengiriman</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Restaurant Cards -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4" id="restaurant-container">
                <?php foreach ($restaurants as $restaurant): ?>
                <div class="col" data-aos="fade-up" data-aos-duration="800" data-aos-delay="<?= 100 + (50 * $loop_index ?? 0) ?>">
                    <div class="card restaurant-card h-100">
                        <div class="position-relative">
                            <img src="<?= $restaurant['banner_url'] ?? 'assets/restaurant-placeholder.png' ?>" class="card-img-top" alt="<?= $restaurant['name'] ?>" style="height: 180px; object-fit: cover;">
                            <?php if ($restaurant['rating'] > 0): ?>
                            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2">
                                <i class="fas fa-star"></i> <?= number_format($restaurant['rating'], 1) ?>
                            </span>
                            <?php endif; ?>
                            <?php if ($restaurant['is_open']): ?>
                            <span class="badge bg-success position-absolute top-0 start-0 m-2">Buka</span>
                            <?php else: ?>
                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">Tutup</span>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <?php if (!empty($restaurant['logo_url'])): ?>
                                    <img src="<?= $restaurant['logo_url'] ?>" alt="Logo" class="rounded-circle me-2" width="50" height="50" style="object-fit: cover; border: 2px solid #f8f9fa;">
                                <?php else: ?>
                                    <div class="rounded-circle me-2 d-flex align-items-center justify-content-center bg-primary text-white" style="width: 50px; height: 50px; font-weight: bold; border: 2px solid #f8f9fa;"><?= substr($restaurant['name'], 0, 1) ?></div>
                                <?php endif; ?>
                                <div>
                                    <h5 class="card-title mb-0 fw-bold"><?= $restaurant['name'] ?></h5>
                                    <p class="card-text small text-muted mb-0">
                                        <i class="fas fa-map-marker-alt me-1"></i> <?= $restaurant['address'] ?>
                                    </p>
                                </div>
                            </div>
                            <p class="card-text">
                                <?= substr($restaurant['description'] ?? 'Makanan lezat diantar langsung ke rumah Anda.', 0, 100) ?>...
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="badge bg-light text-dark">
                                    <i class="fas fa-truck me-1"></i> Ongkir: Rp<?= number_format($restaurant['delivery_fee'] * 15000, 0, ',', '.') ?>
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="fas fa-shopping-basket me-1"></i> Min: Rp<?= number_format($restaurant['min_order_amount'] * 15000, 0, ',', '.') ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-footer bg-white border-top-0 pb-3">
                            <a href="restaurant.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-primary w-100">
                                <i class="fas fa-utensils me-2"></i>Lihat Menu
                            </a>
                        </div>
                    </div>
                </div>
                <?php $loop_index = ($loop_index ?? 0) + 1; ?>
                <?php endforeach; ?>

                <?php if (count($restaurants) === 0): ?>
                <div class="col-12 text-center py-5" data-aos="fade-up" data-aos-duration="800">
                    <div class="card p-5">
                        <i class="fas fa-utensils fa-4x text-muted mb-4"></i>
                        <h3 class="fw-bold">Tidak ada restoran ditemukan</h3>
                        <p class="text-muted mb-4">Silakan coba pencarian lain atau periksa kembali nanti.</p>
                        <div class="d-flex justify-content-center">
                            <button class="btn btn-primary" onclick="document.getElementById('search-input').value = ''; document.getElementById('search-button').click();">
                                <i class="fas fa-sync-alt me-2"></i>Tampilkan Semua Restoran
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="position-fixed bottom-0 end-0 p-3"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="js/script.js"></script>
    <script>
        // Initialize AOS animation
        AOS.init({
            once: true,
            disable: 'mobile'
        });
    </script>

    <script>
        // Search functionality
        document.getElementById('search-button').addEventListener('click', function() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const restaurantCards = document.querySelectorAll('.restaurant-card');

            restaurantCards.forEach(card => {
                const restaurantName = card.querySelector('.card-title').textContent.toLowerCase();
                const restaurantDesc = card.querySelector('.card-text').textContent.toLowerCase();

                if (restaurantName.includes(searchTerm) || restaurantDesc.includes(searchTerm)) {
                    card.closest('.col').style.display = 'block';
                } else {
                    card.closest('.col').style.display = 'none';
                }
            });
        });

        // Sort functionality
        document.getElementById('sort-select').addEventListener('change', function() {
            const sortBy = this.value;
            const restaurantContainer = document.getElementById('restaurant-container');
            const restaurantCards = Array.from(restaurantContainer.querySelectorAll('.col'));

            restaurantCards.sort((a, b) => {
                if (sortBy === 'name') {
                    const nameA = a.querySelector('.card-title').textContent.toLowerCase();
                    const nameB = b.querySelector('.card-title').textContent.toLowerCase();
                    return nameA.localeCompare(nameB);
                } else if (sortBy === 'rating') {
                    const ratingA = a.querySelector('.badge') ? parseFloat(a.querySelector('.badge').textContent.replace(/[^\d.]/g, '')) : 0;
                    const ratingB = b.querySelector('.badge') ? parseFloat(b.querySelector('.badge').textContent.replace(/[^\d.]/g, '')) : 0;
                    return ratingB - ratingA;
                } else if (sortBy === 'delivery_fee') {
                    const feeA = parseFloat(a.querySelector('.text-muted small').textContent.replace(/[^\d.]/g, ''));
                    const feeB = parseFloat(b.querySelector('.text-muted small').textContent.replace(/[^\d.]/g, ''));
                    return feeA - feeB;
                }
                return 0;
            });

            // Clear container
            restaurantContainer.innerHTML = '';

            // Append sorted cards
            restaurantCards.forEach(card => {
                restaurantContainer.appendChild(card);
            });
        });
    </script>
</body>
</html>
