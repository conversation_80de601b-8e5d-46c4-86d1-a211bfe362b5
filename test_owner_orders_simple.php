<?php
// Simple test untuk owner/orders.php
session_start();

echo "<h2>Test Owner Orders Page - Simple</h2>";

// Simulasi login sebagai owner
if (!isset($_SESSION['owner_id'])) {
    echo "<h3>Simulasi Login Owner</h3>";
    echo "<p>Untuk test ini, kita akan simulasi login sebagai owner.</p>";
    
    // Set session untuk simulasi (ganti dengan owner_id yang ada di database)
    $_SESSION['owner_id'] = 1;
    $_SESSION['owner_name'] = 'Test Owner';
    $_SESSION['user_type'] = 'owner';
    
    echo "<p style='color: green;'>✓ Session owner telah diset (ID: 1)</p>";
}

// Test include files
echo "<h3>Test Include Files</h3>";

$files_to_check = [
    'includes/auth.php',
    'config/database.php', 
    'includes/functions.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file not found</p>";
    }
}

// Test functions
echo "<h3>Test Functions</h3>";

require_once 'includes/functions.php';

if (function_exists('formatCurrency')) {
    echo "<p style='color: green;'>✓ formatCurrency function exists</p>";
    echo "<p>Test: formatCurrency(5.07) = " . formatCurrency(5.07) . "</p>";
} else {
    echo "<p style='color: red;'>✗ formatCurrency function not found</p>";
}

if (function_exists('getOrderStatusText')) {
    echo "<p style='color: green;'>✓ getOrderStatusText function exists</p>";
    echo "<p>Test: getOrderStatusText('on_the_way') = " . getOrderStatusText('on_the_way') . "</p>";
} else {
    echo "<p style='color: red;'>✗ getOrderStatusText function not found</p>";
}

if (function_exists('getOrderStatusBadgeClass')) {
    echo "<p style='color: green;'>✓ getOrderStatusBadgeClass function exists</p>";
    echo "<p>Test: getOrderStatusBadgeClass('on_the_way') = " . getOrderStatusBadgeClass('on_the_way') . "</p>";
} else {
    echo "<p style='color: red;'>✗ getOrderStatusBadgeClass function not found</p>";
}

// Test database connection
echo "<h3>Test Database Connection</h3>";

try {
    require_once 'config/database.php';
    $conn = connectDB();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test query
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders");
    $stmt->execute();
    $orderCount = $stmt->fetch()['count'];
    echo "<p>Total orders in database: $orderCount</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test auth function
echo "<h3>Test Auth Function</h3>";

try {
    require_once 'includes/auth.php';
    
    if (function_exists('isRestaurantOwnerLoggedIn')) {
        echo "<p style='color: green;'>✓ isRestaurantOwnerLoggedIn function exists</p>";
        
        $isLoggedIn = isRestaurantOwnerLoggedIn();
        echo "<p>isRestaurantOwnerLoggedIn() = " . ($isLoggedIn ? 'true' : 'false') . "</p>";
    } else {
        echo "<p style='color: red;'>✗ isRestaurantOwnerLoggedIn function not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Auth include failed: " . $e->getMessage() . "</p>";
}

// Test owner data
echo "<h3>Test Owner Data</h3>";

if (isset($conn)) {
    $owner_id = $_SESSION['owner_id'];
    $stmt = $conn->prepare("SELECT * FROM restaurant_owners WHERE owner_id = :owner_id");
    $stmt->bindParam(':owner_id', $owner_id);
    $stmt->execute();
    $owner = $stmt->fetch();
    
    if ($owner) {
        echo "<p style='color: green;'>✓ Owner data found</p>";
        echo "<ul>";
        echo "<li>Name: " . $owner['name'] . "</li>";
        echo "<li>Email: " . $owner['email'] . "</li>";
        echo "<li>Status: " . $owner['status'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Owner data not found for ID: $owner_id</p>";
        echo "<p>Available owners:</p>";
        
        $stmt = $conn->prepare("SELECT owner_id, name, email FROM restaurant_owners LIMIT 5");
        $stmt->execute();
        $owners = $stmt->fetchAll();
        
        if (!empty($owners)) {
            echo "<ul>";
            foreach ($owners as $o) {
                echo "<li>ID: " . $o['owner_id'] . " - " . $o['name'] . " (" . $o['email'] . ")</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No owners found in database</p>";
        }
    }
}

echo "<h3>Test Results</h3>";
echo "<p>Jika semua test di atas menunjukkan ✓ (hijau), maka halaman owner/orders.php seharusnya berfungsi dengan baik.</p>";

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li><a href='owner/orders.php' target='_blank'>Buka owner/orders.php</a></li>";
echo "<li>Jika masih error, copy paste error message yang muncul</li>";
echo "<li>Pastikan sudah login sebagai restaurant owner</li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>Debug Info:</strong></p>";
echo "<ul>";
echo "<li>Current working directory: " . getcwd() . "</li>";
echo "<li>PHP version: " . phpversion() . "</li>";
echo "<li>Session ID: " . session_id() . "</li>";
echo "</ul>";
?>
