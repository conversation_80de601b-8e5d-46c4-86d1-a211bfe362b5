<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$user = $stmt->fetch();

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate form data
    if (empty($name)) {
        $error_message = 'Nama tidak boleh kosong';
    } elseif (empty($email)) {
        $error_message = 'Email tidak boleh kosong';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Format email tidak valid';
    } else {
        // Check if email is already used by another user
        $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email AND user_id != :user_id");
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        if ($stmt->fetchColumn() > 0) {
            $error_message = 'Email sudah digunakan oleh pengguna lain';
        }
    }
    
    // If password is being changed, validate it
    if (!empty($current_password) || !empty($new_password) || !empty($confirm_password)) {
        // Verify current password
        if (!password_verify($current_password, $user['password'])) {
            $error_message = 'Kata sandi saat ini tidak valid';
        } elseif (empty($new_password)) {
            $error_message = 'Kata sandi baru tidak boleh kosong';
        } elseif (strlen($new_password) < 6) {
            $error_message = 'Kata sandi baru harus minimal 6 karakter';
        } elseif ($new_password !== $confirm_password) {
            $error_message = 'Konfirmasi kata sandi tidak cocok';
        }
    }
    
    // If no errors, update user information
    if (empty($error_message)) {
        try {
            $conn->beginTransaction();
            
            // Update user information
            $stmt = $conn->prepare("
                UPDATE users
                SET name = :name,
                    email = :email,
                    phone = :phone,
                    address = :address
                WHERE user_id = :user_id
            ");
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            // Update password if changed
            if (!empty($new_password)) {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("
                    UPDATE users
                    SET password = :password
                    WHERE user_id = :user_id
                ");
                $stmt->bindParam(':password', $hashed_password);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
            }
            
            $conn->commit();
            
            // Update session data
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;
            
            // Refresh user data
            $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            $user = $stmt->fetch();
            
            $success_message = 'Profil berhasil diperbarui';
        } catch (PDOException $e) {
            $conn->rollBack();
            $error_message = 'Terjadi kesalahan: ' . $e->getMessage();
        }
    }
}

// Get unread messages count
$stmt = $conn->prepare("
    SELECT COUNT(*) as unread_count
    FROM chat_messages cm
    JOIN chat_rooms cr ON cm.room_id = cr.room_id
    JOIN chat_participants cp ON cr.room_id = cp.room_id
    WHERE cp.user_type = 'customer' 
    AND cp.user_id = :user_id
    AND cm.sender_type != 'customer'
    AND cm.sender_id != :user_id
    AND cm.is_read = 0
");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$unreadMessages = $stmt->fetch()['unread_count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Pengguna - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .avatar-circle {
            width: 100px;
            height: 100px;
            background-color: #007bff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $user['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item active" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="chat.php">Chat <?= $unreadMessages > 0 ? '<span class="badge bg-danger">' . $unreadMessages . '</span>' : '' ?></a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Profile Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Profil Pengguna</h1>
                <p class="text-muted">Kelola informasi profil Anda</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group">
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Dasbor
                    </a>
                    <a href="orders.php" class="btn btn-outline-success">
                        <i class="fas fa-list me-2"></i>Pesanan
                    </a>
                    <a href="chat.php" class="btn btn-outline-secondary">
                        <i class="fas fa-comments me-2"></i>Chat
                        <?= $unreadMessages > 0 ? '<span class="badge bg-danger">' . $unreadMessages . '</span>' : '' ?>
                    </a>
                </div>
            </div>
        </div>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <div class="avatar-circle mx-auto mb-3">
                            <span><?= substr($user['name'], 0, 1) ?></span>
                        </div>
                        <h5 class="card-title"><?= $user['name'] ?></h5>
                        <p class="card-text text-muted"><?= $user['email'] ?></p>
                        <p class="card-text">
                            <small class="text-muted">Bergabung sejak <?= date('d F Y', strtotime($user['created_at'])) ?></small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Profil</h5>
                    </div>
                    <div class="card-body">
                        <form action="profile.php" method="post">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nama Lengkap</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($user['name']) ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars($user['email']) ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">Nomor Telepon</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                            </div>
                            <div class="mb-3">
                                <label for="address" class="form-label">Alamat</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($user['address'] ?? '') ?></textarea>
                            </div>

                            <hr class="my-4">
                            <h5>Ubah Kata Sandi</h5>
                            <p class="text-muted small">Biarkan kosong jika tidak ingin mengubah kata sandi</p>

                            <div class="mb-3">
                                <label for="current_password" class="form-label">Kata Sandi Saat Ini</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>
                            <div class="mb-3">
                                <label for="new_password" class="form-label">Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <div class="form-text">Minimal 6 karakter</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Konfirmasi Kata Sandi Baru</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship</h5>
                    <p>Layanan pengiriman makanan terbaik di kota Anda.</p>
                    <p>
                        <a href="../about.php" class="text-white">Tentang Kami</a> | 
                        <a href="../contact.php" class="text-white">Hubungi Kami</a> | 
                        <a href="../terms.php" class="text-white">Syarat dan Ketentuan</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Contoh No. 123, Jakarta</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/user-experience.js"></script>
</body>
</html>
