<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get user orders with restaurant info
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.logo_url as restaurant_logo
    FROM orders o
    LEFT JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.user_id = ?
    ORDER BY o.created_at DESC
");
$stmt->execute([$user_id]);
$orders = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riwayat Pesanan - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $user['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item active" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="addresses.php">Alamat</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Riwayat Pesanan</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../index.php">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pesanan</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if (empty($orders)): ?>
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                    <h3>Belum ada pesanan</h3>
                    <p class="text-muted">Anda belum melakukan pesanan apapun</p>
                    <a href="../restaurants.php" class="btn btn-primary mt-2">Jelajahi Restoran</a>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($orders as $order): ?>
                    <div class="col-md-6 mb-4">
                        <div class="card shadow-sm h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge <?= getOrderStatusBadgeClass($order['order_status']) ?> me-2">
                                        <?= getOrderStatusText($order['order_status']) ?>
                                    </span>
                                    <span class="badge <?= getPaymentStatusBadgeClass($order['payment_status']) ?>">
                                        <?= getPaymentStatusText($order['payment_status']) ?>
                                    </span>
                                </div>
                                <small class="text-muted"><?= date('d M Y, H:i', strtotime($order['created_at'])) ?></small>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="<?= $order['restaurant_logo'] ?? '../assets/restaurant-placeholder.png' ?>" alt="<?= $order['restaurant_name'] ?>" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                    <div>
                                        <h5 class="mb-0"><?= $order['restaurant_name'] ?></h5>
                                        <small class="text-muted">Order #<?= $order['order_id'] ?></small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <p class="mb-1"><i class="fas fa-map-marker-alt me-2 text-primary"></i> <?= substr($order['delivery_address'], 0, 100) ?><?= strlen($order['delivery_address']) > 100 ? '...' : '' ?></p>
                                    <p class="mb-1"><i class="fas fa-money-bill-wave me-2 text-success"></i> <?= getPaymentMethodText($order['payment_method']) ?></p>
                                    <?php if ($order['notes']): ?>
                                        <p class="mb-1"><i class="fas fa-sticky-note me-2 text-warning"></i> <?= substr($order['notes'], 0, 100) ?><?= strlen($order['notes']) > 100 ? '...' : '' ?></p>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div>
                                        <p class="mb-0"><strong>Total:</strong></p>
                                        <h5 class="text-primary"><?= formatCurrency($order['total_amount']) ?></h5>
                                    </div>
                                    <div>
                                        <div class="btn-group">
                                            <a href="../order_details.php?id=<?= $order['order_id'] ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> Detail
                                            </a>
                                            <a href="chat.php?start_chat=order&order_id=<?= $order['order_id'] ?>" class="btn btn-outline-success">
                                                <i class="fas fa-comments me-1"></i> Chat
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship</h5>
                    <p>Layanan pengiriman makanan terbaik di kota Anda.</p>
                    <p>
                        <a href="../about.php" class="text-white">Tentang Kami</a> |
                        <a href="../contact.php" class="text-white">Hubungi Kami</a> |
                        <a href="../terms.php" class="text-white">Syarat dan Ketentuan</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Contoh No. 123, Jakarta</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
