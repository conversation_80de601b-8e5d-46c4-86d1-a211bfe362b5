<?php
// Start session
session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$userName = $_SESSION['user_name'];

// Connect to database
$conn = connectDB();

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all cuisine types
$stmt = $conn->prepare("
    SELECT DISTINCT cuisine_type 
    FROM restaurants 
    WHERE cuisine_type IS NOT NULL AND cuisine_type != ''
    ORDER BY cuisine_type
");
$stmt->execute();
$cuisineTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get user's preferred cuisine types
$stmt = $conn->prepare("
    SELECT cuisine_type 
    FROM user_cuisine_preferences 
    WHERE user_id = :user_id
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$userCuisinePreferences = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get all dietary preferences
$stmt = $conn->prepare("
    SELECT preference_id, name, description 
    FROM preferences 
    WHERE type = 'dietary'
    ORDER BY name
");
$stmt->execute();
$dietaryPreferences = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user's dietary preferences
$stmt = $conn->prepare("
    SELECT preference_id 
    FROM user_preferences 
    WHERE user_id = :user_id AND preference_id IN (
        SELECT preference_id FROM preferences WHERE type = 'dietary'
    )
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$userDietaryPreferences = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get all spice level preferences
$stmt = $conn->prepare("
    SELECT preference_id, name, description 
    FROM preferences 
    WHERE type = 'spice_level'
    ORDER BY name
");
$stmt->execute();
$spiceLevelPreferences = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user's spice level preference
$stmt = $conn->prepare("
    SELECT preference_id 
    FROM user_preferences 
    WHERE user_id = :user_id AND preference_id IN (
        SELECT preference_id FROM preferences WHERE type = 'spice_level'
    )
");
$stmt->bindParam(':user_id', $userId);
$stmt->execute();
$userSpiceLevelPreference = $stmt->fetch(PDO::FETCH_COLUMN);

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // Update cuisine preferences
        $stmt = $conn->prepare("DELETE FROM user_cuisine_preferences WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        if (isset($_POST['cuisine_types']) && is_array($_POST['cuisine_types'])) {
            $insertCuisine = $conn->prepare("
                INSERT INTO user_cuisine_preferences (user_id, cuisine_type) 
                VALUES (:user_id, :cuisine_type)
            ");
            
            foreach ($_POST['cuisine_types'] as $cuisineType) {
                $insertCuisine->bindParam(':user_id', $userId);
                $insertCuisine->bindParam(':cuisine_type', $cuisineType);
                $insertCuisine->execute();
            }
        }
        
        // Update dietary preferences
        $stmt = $conn->prepare("
            DELETE FROM user_preferences 
            WHERE user_id = :user_id AND preference_id IN (
                SELECT preference_id FROM preferences WHERE type = 'dietary'
            )
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        if (isset($_POST['dietary_preferences']) && is_array($_POST['dietary_preferences'])) {
            $insertDietary = $conn->prepare("
                INSERT INTO user_preferences (user_id, preference_id) 
                VALUES (:user_id, :preference_id)
            ");
            
            foreach ($_POST['dietary_preferences'] as $preferenceId) {
                $insertDietary->bindParam(':user_id', $userId);
                $insertDietary->bindParam(':preference_id', $preferenceId);
                $insertDietary->execute();
            }
        }
        
        // Update spice level preference
        $stmt = $conn->prepare("
            DELETE FROM user_preferences 
            WHERE user_id = :user_id AND preference_id IN (
                SELECT preference_id FROM preferences WHERE type = 'spice_level'
            )
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        if (isset($_POST['spice_level']) && !empty($_POST['spice_level'])) {
            $insertSpiceLevel = $conn->prepare("
                INSERT INTO user_preferences (user_id, preference_id) 
                VALUES (:user_id, :preference_id)
            ");
            
            $insertSpiceLevel->bindParam(':user_id', $userId);
            $insertSpiceLevel->bindParam(':preference_id', $_POST['spice_level']);
            $insertSpiceLevel->execute();
        }
        
        // Update notification preferences
        $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
        $pushNotifications = isset($_POST['push_notifications']) ? 1 : 0;
        $smsNotifications = isset($_POST['sms_notifications']) ? 1 : 0;
        $marketingEmails = isset($_POST['marketing_emails']) ? 1 : 0;
        
        $stmt = $conn->prepare("
            UPDATE users 
            SET email_notifications = :email_notifications,
                push_notifications = :push_notifications,
                sms_notifications = :sms_notifications,
                marketing_emails = :marketing_emails
            WHERE user_id = :user_id
        ");
        
        $stmt->bindParam(':email_notifications', $emailNotifications);
        $stmt->bindParam(':push_notifications', $pushNotifications);
        $stmt->bindParam(':sms_notifications', $smsNotifications);
        $stmt->bindParam(':marketing_emails', $marketingEmails);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        $success = 'Preferensi berhasil disimpan!';
        
        // Refresh user data
        $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Refresh user's preferred cuisine types
        $stmt = $conn->prepare("
            SELECT cuisine_type 
            FROM user_cuisine_preferences 
            WHERE user_id = :user_id
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $userCuisinePreferences = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Refresh user's dietary preferences
        $stmt = $conn->prepare("
            SELECT preference_id 
            FROM user_preferences 
            WHERE user_id = :user_id AND preference_id IN (
                SELECT preference_id FROM preferences WHERE type = 'dietary'
            )
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $userDietaryPreferences = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Refresh user's spice level preference
        $stmt = $conn->prepare("
            SELECT preference_id 
            FROM user_preferences 
            WHERE user_id = :user_id AND preference_id IN (
                SELECT preference_id FROM preferences WHERE type = 'spice_level'
            )
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $userSpiceLevelPreference = $stmt->fetch(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        $error = 'Terjadi kesalahan: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preferensi - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/personalization.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                            <span class="badge bg-danger rounded-pill">
                                <?php
                                    $cartCount = 0;
                                    if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                                        foreach ($_SESSION['cart'] as $items) {
                                            $cartCount += count($items);
                                        }
                                    }
                                    echo $cartCount;
                                ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $userName ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item active" href="preferences.php">Preferensi</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Preferensi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../index.php">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Preferensi</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="list-group">
                    <a href="dashboard.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dasbor
                    </a>
                    <a href="orders.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-list me-2"></i>Pesanan
                    </a>
                    <a href="profile.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i>Profil
                    </a>
                    <a href="preferences.php" class="list-group-item list-group-item-action active">
                        <i class="fas fa-sliders-h me-2"></i>Preferensi
                    </a>
                    <a href="favorites.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Favorit
                    </a>
                    <a href="addresses.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i>Alamat
                    </a>
                    <a href="../logout.php" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Keluar
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pengaturan Preferensi</h5>
                    </div>
                    <div class="card-body">
                        <form action="preferences.php" method="post" id="personalization-form">
                            <!-- Cuisine Preferences -->
                            <div class="mb-4 preference-item">
                                <h5><i class="fas fa-utensils me-2"></i>Jenis Masakan Favorit</h5>
                                <p class="text-muted small">Pilih jenis masakan yang Anda sukai untuk mendapatkan rekomendasi yang lebih baik.</p>
                                
                                <div class="preference-tags">
                                    <?php foreach ($cuisineTypes as $cuisine): ?>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" name="cuisine_types[]" 
                                                   id="cuisine-<?= str_replace(' ', '-', strtolower($cuisine)) ?>" 
                                                   value="<?= $cuisine ?>" 
                                                   <?= in_array($cuisine, $userCuisinePreferences) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="cuisine-<?= str_replace(' ', '-', strtolower($cuisine)) ?>">
                                                <?= $cuisine ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <!-- Dietary Preferences -->
                            <div class="mb-4 preference-item">
                                <h5><i class="fas fa-leaf me-2"></i>Preferensi Diet</h5>
                                <p class="text-muted small">Pilih preferensi diet Anda untuk mendapatkan rekomendasi makanan yang sesuai.</p>
                                
                                <div class="row">
                                    <?php foreach ($dietaryPreferences as $preference): ?>
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="dietary_preferences[]" 
                                                       id="dietary-<?= $preference['preference_id'] ?>" 
                                                       value="<?= $preference['preference_id'] ?>" 
                                                       <?= in_array($preference['preference_id'], $userDietaryPreferences) ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="dietary-<?= $preference['preference_id'] ?>">
                                                    <?= $preference['name'] ?>
                                                    <?php if (!empty($preference['description'])): ?>
                                                        <small class="d-block text-muted"><?= $preference['description'] ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <!-- Spice Level Preference -->
                            <div class="mb-4 preference-item">
                                <h5><i class="fas fa-pepper-hot me-2"></i>Tingkat Kepedasan</h5>
                                <p class="text-muted small">Pilih tingkat kepedasan yang Anda sukai.</p>
                                
                                <div class="row">
                                    <?php foreach ($spiceLevelPreferences as $preference): ?>
                                        <div class="col-md-4">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="spice_level" 
                                                       id="spice-<?= $preference['preference_id'] ?>" 
                                                       value="<?= $preference['preference_id'] ?>" 
                                                       <?= $userSpiceLevelPreference == $preference['preference_id'] ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="spice-<?= $preference['preference_id'] ?>">
                                                    <?= $preference['name'] ?>
                                                    <?php if (!empty($preference['description'])): ?>
                                                        <small class="d-block text-muted"><?= $preference['description'] ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <!-- Notification Preferences -->
                            <div class="mb-4 preference-item">
                                <h5><i class="fas fa-bell me-2"></i>Preferensi Notifikasi</h5>
                                <p class="text-muted small">Pilih jenis notifikasi yang ingin Anda terima.</p>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="email_notifications" 
                                           id="email-notifications" <?= $user['email_notifications'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email-notifications">
                                        Notifikasi Email
                                        <small class="d-block text-muted">Terima notifikasi tentang status pesanan dan promosi melalui email.</small>
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="push_notifications" 
                                           id="push-notifications" <?= $user['push_notifications'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="push-notifications">
                                        Notifikasi Push
                                        <small class="d-block text-muted">Terima notifikasi langsung di perangkat Anda.</small>
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="sms_notifications" 
                                           id="sms-notifications" <?= $user['sms_notifications'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="sms-notifications">
                                        Notifikasi SMS
                                        <small class="d-block text-muted">Terima notifikasi tentang status pesanan melalui SMS.</small>
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="marketing_emails" 
                                           id="marketing-emails" <?= $user['marketing_emails'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="marketing-emails">
                                        Email Pemasaran
                                        <small class="d-block text-muted">Terima penawaran khusus, promosi, dan berita terbaru.</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Preferensi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">Beranda</a></li>
                        <li><a href="../restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="../about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="../contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="spinner-overlay">
        <div class="spinner"></div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/user-experience.js"></script>
    <script src="../js/personalization.js"></script>
</body>
</html>
