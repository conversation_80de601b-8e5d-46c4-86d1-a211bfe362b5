<?php
// Start session
session_start();

// Include auth functions
require_once '../includes/auth.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Include database connection
require_once '../config/database.php';
$conn = connectDB();

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$user = $stmt->fetch();

// Get recent orders
$stmt = $conn->prepare("
    SELECT o.*, r.name as restaurant_name, r.banner_url as restaurant_image
    FROM orders o
    JOIN restaurants r ON o.restaurant_id = r.restaurant_id
    WHERE o.user_id = :user_id
    ORDER BY o.created_at DESC
    LIMIT 5
");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$recentOrders = $stmt->fetchAll();

// Get favorite restaurants
$stmt = $conn->prepare("
    SELECT r.*,
           (SELECT COUNT(*) FROM orders WHERE user_id = :user_id AND restaurant_id = r.restaurant_id) as order_count
    FROM restaurants r
    JOIN orders o ON r.restaurant_id = o.restaurant_id
    WHERE o.user_id = :user_id
    GROUP BY r.restaurant_id
    ORDER BY order_count DESC
    LIMIT 3
");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$favoriteRestaurants = $stmt->fetchAll();

// Get unread messages count
$stmt = $conn->prepare("
    SELECT COUNT(*) as unread_count
    FROM chat_messages cm
    JOIN chat_rooms cr ON cm.room_id = cr.room_id
    JOIN chat_participants cp ON cr.room_id = cp.room_id
    WHERE cp.user_type = 'customer'
    AND cp.user_id = :user_id
    AND cm.sender_type != 'customer'
    AND cm.sender_id != :user_id
    AND cm.is_read = 0
");
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$unreadMessages = $stmt->fetch()['unread_count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dasbor Pengguna - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $user['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item" href="chat.php">Chat <?= $unreadMessages > 0 ? '<span class="badge bg-danger">' . $unreadMessages . '</span>' : '' ?></a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-0">Dasbor Pengguna</h1>
                <p class="text-muted">Selamat datang, <?= $user['name'] ?></p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group">
                    <a href="orders.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>Pesanan Saya
                    </a>
                    <a href="profile.php" class="btn btn-outline-success">
                        <i class="fas fa-user me-2"></i>Profil
                    </a>
                    <a href="chat.php" class="btn btn-outline-secondary">
                        <i class="fas fa-comments me-2"></i>Chat
                        <?= $unreadMessages > 0 ? '<span class="badge bg-danger">' . $unreadMessages . '</span>' : '' ?>
                    </a>
                </div>
            </div>
        </div>

        <!-- User Info Card -->
        <div class="row mb-4">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Informasi Pengguna</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="avatar-circle mx-auto mb-3">
                                <span class="avatar-initials"><?= substr($user['name'], 0, 1) ?></span>
                            </div>
                            <h5><?= $user['name'] ?></h5>
                            <p class="text-muted mb-0"><?= $user['email'] ?></p>
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-phone me-2"></i>Telepon</span>
                                <span class="text-muted"><?= $user['phone'] ?: 'Belum diatur' ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-map-marker-alt me-2"></i>Alamat</span>
                                <span class="text-muted"><?= $user['address'] ?: 'Belum diatur' ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-calendar me-2"></i>Bergabung</span>
                                <span class="text-muted"><?= date('d/m/Y', strtotime($user['created_at'])) ?></span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white">
                        <a href="profile.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-edit me-2"></i>Edit Profil
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Pesanan Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentOrders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h4>Belum ada pesanan</h4>
                                <p class="text-muted">Anda belum melakukan pemesanan. Jelajahi restoran untuk mulai memesan.</p>
                                <a href="../restaurants.php" class="btn btn-primary mt-2">Jelajahi Restoran</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Restoran</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentOrders as $order): ?>
                                            <tr>
                                                <td>#<?= $order['order_id'] ?></td>
                                                <td><?= $order['restaurant_name'] ?></td>
                                                <td>Rp<?= number_format($order['total_amount'] * 15000, 0, ',', '.') ?></td>
                                                <td>
                                                    <span class="badge <?php
                                                        switch ($order['order_status']) {
                                                            case 'pending': echo 'bg-warning text-dark'; break;
                                                            case 'confirmed': echo 'bg-info text-dark'; break;
                                                            case 'preparing': echo 'bg-primary'; break;
                                                            case 'ready_for_pickup': echo 'bg-primary'; break;
                                                            case 'picked_up': echo 'bg-info'; break;
                                                            case 'on_the_way': echo 'bg-info'; break;
                                                            case 'delivered': echo 'bg-success'; break;
                                                            case 'cancelled': echo 'bg-danger'; break;
                                                            default: echo 'bg-secondary';
                                                        }
                                                    ?>">
                                                        <?php
                                                            switch ($order['order_status']) {
                                                                case 'pending': echo 'Menunggu'; break;
                                                                case 'confirmed': echo 'Dikonfirmasi'; break;
                                                                case 'preparing': echo 'Sedang Dipersiapkan'; break;
                                                                case 'ready_for_pickup': echo 'Siap Diambil'; break;
                                                                case 'picked_up': echo 'Diambil'; break;
                                                                case 'on_the_way': echo 'Dalam Perjalanan'; break;
                                                                case 'delivered': echo 'Terkirim'; break;
                                                                case 'cancelled': echo 'Dibatalkan'; break;
                                                                default: echo $order['order_status'];
                                                            }
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="../order_details.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="chat.php?start_chat=order&order_id=<?= $order['order_id'] ?>" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-comments"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-3">
                                <a href="orders.php" class="btn btn-outline-primary">Lihat Semua Pesanan</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Favorite Restaurants -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Restoran Favorit</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($favoriteRestaurants)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                <h4>Belum ada restoran favorit</h4>
                                <p class="text-muted">Pesan dari restoran untuk menambahkannya ke daftar favorit Anda.</p>
                                <a href="../restaurants.php" class="btn btn-primary mt-2">Jelajahi Restoran</a>
                            </div>
                        <?php else: ?>
                            <div class="row row-cols-1 row-cols-md-3 g-4">
                                <?php foreach ($favoriteRestaurants as $restaurant): ?>
                                    <div class="col">
                                        <div class="card h-100 restaurant-card">
                                            <img src="<?= $restaurant['banner_url'] ?? '../assets/restaurant-placeholder.png' ?>" class="card-img-top" alt="<?= $restaurant['name'] ?>" style="height: 120px; object-fit: cover;">
                                            <div class="card-body">
                                                <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                                <p class="card-text small text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i> <?= $restaurant['address'] ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="badge <?= $restaurant['is_open'] ? 'bg-success' : 'bg-danger' ?>">
                                                        <?= $restaurant['is_open'] ? 'Buka' : 'Tutup' ?>
                                                    </span>
                                                    <span class="text-muted small">
                                                        <i class="fas fa-star me-1"></i> <?= number_format($restaurant['rating'], 1) ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-white">
                                                <a href="../restaurant.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-utensils me-2"></i>Lihat Menu
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship</h5>
                    <p>Layanan pengiriman makanan terbaik di kota Anda.</p>
                    <p>
                        <a href="../about.php" class="text-white">Tentang Kami</a> |
                        <a href="../contact.php" class="text-white">Hubungi Kami</a> |
                        <a href="../terms.php" class="text-white">Syarat dan Ketentuan</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Contoh No. 123, Jakarta</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/user-experience.js"></script>
</body>
</html>
