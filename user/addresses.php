<?php
// Start session
session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isUserLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Connect to database
$conn = connectDB();

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get user addresses
$stmt = $conn->prepare("
    SELECT * FROM user_addresses
    WHERE user_id = ?
    ORDER BY is_default DESC, created_at DESC
");
$stmt->execute([$user_id]);
$addresses = $stmt->fetchAll();

// Process form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Add or edit address
        if ($_POST['action'] === 'save') {
            $address_id = $_POST['address_id'] ?? null;
            $address_label = $_POST['address_label'] ?? '';
            $address = $_POST['address'] ?? '';
            $latitude = $_POST['latitude'] ?? -6.2088; // Default to Jakarta
            $longitude = $_POST['longitude'] ?? 106.8456; // Default to Jakarta
            $is_default = isset($_POST['is_default']) ? 1 : 0;
            
            if (empty($address_label) || empty($address)) {
                $error = 'Label alamat dan alamat harus diisi';
            } else {
                try {
                    // Begin transaction
                    $conn->beginTransaction();
                    
                    // If setting as default, unset all other defaults
                    if ($is_default) {
                        $stmt = $conn->prepare("
                            UPDATE user_addresses
                            SET is_default = 0
                            WHERE user_id = ?
                        ");
                        $stmt->execute([$user_id]);
                    }
                    
                    // Add or update address
                    if ($address_id) {
                        // Update existing address
                        $stmt = $conn->prepare("
                            UPDATE user_addresses
                            SET address_label = ?, address = ?, latitude = ?, longitude = ?, is_default = ?
                            WHERE address_id = ? AND user_id = ?
                        ");
                        $stmt->execute([$address_label, $address, $latitude, $longitude, $is_default, $address_id, $user_id]);
                        $success = 'Alamat berhasil diperbarui';
                    } else {
                        // Add new address
                        $stmt = $conn->prepare("
                            INSERT INTO user_addresses (user_id, address_label, address, latitude, longitude, is_default)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$user_id, $address_label, $address, $latitude, $longitude, $is_default]);
                        $success = 'Alamat baru berhasil ditambahkan';
                    }
                    
                    // Commit transaction
                    $conn->commit();
                    
                    // Redirect to refresh page
                    header('Location: addresses.php?success=' . urlencode($success));
                    exit;
                } catch (PDOException $e) {
                    // Rollback transaction on error
                    $conn->rollBack();
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
        }
        
        // Delete address
        if ($_POST['action'] === 'delete' && isset($_POST['address_id'])) {
            $address_id = $_POST['address_id'];
            
            try {
                $stmt = $conn->prepare("
                    DELETE FROM user_addresses
                    WHERE address_id = ? AND user_id = ?
                ");
                $stmt->execute([$address_id, $user_id]);
                
                $success = 'Alamat berhasil dihapus';
                header('Location: addresses.php?success=' . urlencode($success));
                exit;
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
        
        // Set as default
        if ($_POST['action'] === 'set_default' && isset($_POST['address_id'])) {
            $address_id = $_POST['address_id'];
            
            try {
                // Begin transaction
                $conn->beginTransaction();
                
                // Unset all defaults
                $stmt = $conn->prepare("
                    UPDATE user_addresses
                    SET is_default = 0
                    WHERE user_id = ?
                ");
                $stmt->execute([$user_id]);
                
                // Set new default
                $stmt = $conn->prepare("
                    UPDATE user_addresses
                    SET is_default = 1
                    WHERE address_id = ? AND user_id = ?
                ");
                $stmt->execute([$address_id, $user_id]);
                
                // Commit transaction
                $conn->commit();
                
                $success = 'Alamat utama berhasil diubah';
                header('Location: addresses.php?success=' . urlencode($success));
                exit;
            } catch (PDOException $e) {
                // Rollback transaction on error
                $conn->rollBack();
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get success message from URL
if (isset($_GET['success'])) {
    $success = $_GET['success'];
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alamat Saya - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $user['name'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="dashboard.php">Dasbor</a></li>
                            <li><a class="dropdown-item" href="orders.php">Pesanan</a></li>
                            <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                            <li><a class="dropdown-item active" href="addresses.php">Alamat</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Keluar</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Alamat Saya</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../index.php">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="dashboard.php">Dasbor</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Alamat</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-md-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                    <i class="fas fa-plus me-2"></i>Tambah Alamat Baru
                </button>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <div class="row">
            <?php if (empty($addresses)): ?>
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h3>Belum ada alamat tersimpan</h3>
                            <p class="text-muted">Tambahkan alamat untuk mempermudah proses checkout</p>
                            <button class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                <i class="fas fa-plus me-2"></i>Tambah Alamat Baru
                            </button>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($addresses as $address): ?>
                    <div class="col-md-6 mb-4">
                        <div class="card shadow-sm h-100 <?= $address['is_default'] ? 'border-primary' : '' ?>">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><?= $address['address_label'] ?></h5>
                                <?php if ($address['is_default']): ?>
                                    <span class="badge bg-primary">Alamat Utama</span>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?= $address['address'] ?></p>
                            </div>
                            <div class="card-footer bg-white">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editAddressModal" 
                                        data-address-id="<?= $address['address_id'] ?>"
                                        data-address-label="<?= $address['address_label'] ?>"
                                        data-address="<?= $address['address'] ?>"
                                        data-latitude="<?= $address['latitude'] ?>"
                                        data-longitude="<?= $address['longitude'] ?>"
                                        data-is-default="<?= $address['is_default'] ?>">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </button>
                                    <?php if (!$address['is_default']): ?>
                                        <form method="post" class="d-inline">
                                            <input type="hidden" name="action" value="set_default">
                                            <input type="hidden" name="address_id" value="<?= $address['address_id'] ?>">
                                            <button type="submit" class="btn btn-outline-success">
                                                <i class="fas fa-check-circle me-1"></i> Jadikan Utama
                                            </button>
                                        </form>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus alamat ini?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="address_id" value="<?= $address['address_id'] ?>">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i> Hapus
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Address Modal -->
    <div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAddressModalLabel">Tambah Alamat Baru</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="save">
                        
                        <div class="mb-3">
                            <label for="address_label" class="form-label">Label Alamat</label>
                            <input type="text" class="form-control" id="address_label" name="address_label" required placeholder="Contoh: Rumah, Kantor, Apartemen">
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Alamat Lengkap</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Masukkan alamat lengkap"></textarea>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1">
                            <label class="form-check-label" for="is_default">
                                Jadikan sebagai alamat utama
                            </label>
                        </div>
                        
                        <input type="hidden" name="latitude" value="-6.2088">
                        <input type="hidden" name="longitude" value="106.8456">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Address Modal -->
    <div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAddressModalLabel">Edit Alamat</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="save">
                        <input type="hidden" name="address_id" id="edit_address_id">
                        
                        <div class="mb-3">
                            <label for="edit_address_label" class="form-label">Label Alamat</label>
                            <input type="text" class="form-control" id="edit_address_label" name="address_label" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_address" class="form-label">Alamat Lengkap</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="3" required></textarea>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="edit_is_default" name="is_default" value="1">
                            <label class="form-check-label" for="edit_is_default">
                                Jadikan sebagai alamat utama
                            </label>
                        </div>
                        
                        <input type="hidden" name="latitude" id="edit_latitude">
                        <input type="hidden" name="longitude" id="edit_longitude">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>KikaZen Ship</h5>
                    <p>Layanan pengiriman makanan terbaik di kota Anda.</p>
                    <p>
                        <a href="../about.php" class="text-white">Tentang Kami</a> | 
                        <a href="../contact.php" class="text-white">Hubungi Kami</a> | 
                        <a href="../terms.php" class="text-white">Syarat dan Ketentuan</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Contoh No. 123, Jakarta</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fill edit modal with address data
        document.addEventListener('DOMContentLoaded', function() {
            const editAddressModal = document.getElementById('editAddressModal');
            if (editAddressModal) {
                editAddressModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    
                    const addressId = button.getAttribute('data-address-id');
                    const addressLabel = button.getAttribute('data-address-label');
                    const address = button.getAttribute('data-address');
                    const latitude = button.getAttribute('data-latitude');
                    const longitude = button.getAttribute('data-longitude');
                    const isDefault = button.getAttribute('data-is-default') === '1';
                    
                    document.getElementById('edit_address_id').value = addressId;
                    document.getElementById('edit_address_label').value = addressLabel;
                    document.getElementById('edit_address').value = address;
                    document.getElementById('edit_latitude').value = latitude;
                    document.getElementById('edit_longitude').value = longitude;
                    document.getElementById('edit_is_default').checked = isDefault;
                });
            }
        });
    </script>
</body>
</html>
