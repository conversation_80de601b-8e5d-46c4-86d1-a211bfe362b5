<?php
/**
 * Query optimization functions for KikaZen Ship
 * 
 * This file contains functions for optimizing database queries to improve performance.
 */

// Include cache functions
require_once __DIR__ . '/cache.php';

/**
 * Execute an optimized query with caching
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param int $ttl Cache time to live in seconds (default: 3600 = 1 hour)
 * @param bool $useCache Whether to use cache (default: true)
 * @return array Query result
 */
function executeOptimizedQuery($conn, $query, $params = [], $ttl = 3600, $useCache = true) {
    // Check if it's a SELECT query (safe to cache)
    $isSelect = stripos(trim($query), 'SELECT') === 0;
    
    // Only cache SELECT queries
    if ($isSelect && $useCache) {
        return cacheQuery($conn, $query, $params, $ttl);
    }
    
    // For non-SELECT queries or when cache is disabled
    $stmt = $conn->prepare($query);
    
    // Bind parameters if any
    foreach ($params as $key => $value) {
        $stmt->bindValue(is_numeric($key) ? $key + 1 : $key, $value);
    }
    
    $stmt->execute();
    
    // For SELECT queries, return the result
    if ($isSelect) {
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // For non-SELECT queries, return affected rows
    return $stmt->rowCount();
}

/**
 * Get a single row from a query
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param int $ttl Cache time to live in seconds (default: 3600 = 1 hour)
 * @param bool $useCache Whether to use cache (default: true)
 * @return array|null Query result or null if no rows
 */
function getRow($conn, $query, $params = [], $ttl = 3600, $useCache = true) {
    $result = executeOptimizedQuery($conn, $query, $params, $ttl, $useCache);
    
    if (is_array($result) && count($result) > 0) {
        return $result[0];
    }
    
    return null;
}

/**
 * Get a single value from a query
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param int $ttl Cache time to live in seconds (default: 3600 = 1 hour)
 * @param bool $useCache Whether to use cache (default: true)
 * @return mixed|null Query result or null if no rows
 */
function getValue($conn, $query, $params = [], $ttl = 3600, $useCache = true) {
    $row = getRow($conn, $query, $params, $ttl, $useCache);
    
    if (is_array($row) && count($row) > 0) {
        return reset($row);
    }
    
    return null;
}

/**
 * Execute a batch of queries in a transaction
 * 
 * @param PDO $conn Database connection
 * @param array $queries Array of queries, each containing 'query' and 'params' keys
 * @return bool True on success, false on failure
 */
function executeBatch($conn, $queries) {
    try {
        $conn->beginTransaction();
        
        foreach ($queries as $queryData) {
            $query = $queryData['query'];
            $params = $queryData['params'] ?? [];
            
            $stmt = $conn->prepare($query);
            
            // Bind parameters if any
            foreach ($params as $key => $value) {
                $stmt->bindValue(is_numeric($key) ? $key + 1 : $key, $value);
            }
            
            $stmt->execute();
        }
        
        $conn->commit();
        return true;
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get paginated results
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query without LIMIT clause
 * @param array $params Query parameters
 * @param int $page Current page (1-based)
 * @param int $perPage Items per page
 * @param int $ttl Cache time to live in seconds (default: 3600 = 1 hour)
 * @param bool $useCache Whether to use cache (default: true)
 * @return array Paginated results with 'data', 'total', 'page', 'perPage', 'totalPages' keys
 */
function getPaginatedResults($conn, $query, $params = [], $page = 1, $perPage = 10, $ttl = 3600, $useCache = true) {
    // Ensure page and perPage are valid
    $page = max(1, (int)$page);
    $perPage = max(1, (int)$perPage);
    
    // Calculate offset
    $offset = ($page - 1) * $perPage;
    
    // Get total count
    $countQuery = preg_replace('/^SELECT\s+.+?\s+FROM\s+/is', 'SELECT COUNT(*) FROM ', $query);
    $countQuery = preg_replace('/\s+ORDER\s+BY\s+.+$/is', '', $countQuery);
    
    $total = (int)getValue($conn, $countQuery, $params, $ttl, $useCache);
    
    // Add LIMIT clause to the original query
    $limitedQuery = $query . " LIMIT $offset, $perPage";
    
    // Get paginated data
    $data = executeOptimizedQuery($conn, $limitedQuery, $params, $ttl, $useCache);
    
    // Calculate total pages
    $totalPages = ceil($total / $perPage);
    
    return [
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'perPage' => $perPage,
        'totalPages' => $totalPages
    ];
}

/**
 * Optimize a query by adding indexes if needed
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query
 * @return string Optimized query
 */
function optimizeQuery($query) {
    // This is a placeholder for more advanced query optimization
    // In a real implementation, you would analyze the query and suggest indexes
    
    // For now, just return the original query
    return $query;
}
