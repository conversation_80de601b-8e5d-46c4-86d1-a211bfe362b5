<?php
/**
 * Two-factor authentication functions for KikaZen Ship
 * 
 * This file contains functions for implementing two-factor authentication.
 */

// Include security functions
require_once __DIR__ . '/security.php';

// Define 2FA constants
define('OTP_LENGTH', 6);
define('OTP_EXPIRY', 300); // 5 minutes
define('MAX_OTP_ATTEMPTS', 3);

/**
 * Generate a one-time password (OTP)
 * 
 * @param int $length OTP length (default: 6)
 * @return string Generated OTP
 */
function generateOTP($length = OTP_LENGTH) {
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= mt_rand(0, 9);
    }
    return $otp;
}

/**
 * Store OTP in database
 * 
 * @param PDO $conn Database connection
 * @param int $userId User ID
 * @param string $otp One-time password
 * @param string $userType User type (user, driver, owner, admin)
 * @return bool True on success, false on failure
 */
function storeOTP($conn, $userId, $otp, $userType = 'user') {
    try {
        // Check if there's an existing OTP for this user
        $stmt = $conn->prepare("
            SELECT id FROM otp_codes 
            WHERE user_id = :user_id AND user_type = :user_type
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':user_type', $userType);
        $stmt->execute();
        
        $existingOTP = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingOTP) {
            // Update existing OTP
            $stmt = $conn->prepare("
                UPDATE otp_codes 
                SET otp = :otp, expires_at = DATE_ADD(NOW(), INTERVAL :expiry SECOND), attempts = 0
                WHERE user_id = :user_id AND user_type = :user_type
            ");
        } else {
            // Insert new OTP
            $stmt = $conn->prepare("
                INSERT INTO otp_codes (user_id, user_type, otp, expires_at)
                VALUES (:user_id, :user_type, :otp, DATE_ADD(NOW(), INTERVAL :expiry SECOND))
            ");
        }
        
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':user_type', $userType);
        $stmt->bindParam(':otp', $otp);
        $stmt->bindParam(':expiry', OTP_EXPIRY, PDO::PARAM_INT);
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Verify OTP
 * 
 * @param PDO $conn Database connection
 * @param int $userId User ID
 * @param string $otp One-time password to verify
 * @param string $userType User type (user, driver, owner, admin)
 * @return bool True if OTP is valid, false otherwise
 */
function verifyOTP($conn, $userId, $otp, $userType = 'user') {
    try {
        // Get OTP from database
        $stmt = $conn->prepare("
            SELECT id, otp, expires_at, attempts 
            FROM otp_codes 
            WHERE user_id = :user_id AND user_type = :user_type
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':user_type', $userType);
        $stmt->execute();
        
        $otpData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$otpData) {
            // No OTP found
            return false;
        }
        
        // Check if OTP has expired
        $expiresAt = strtotime($otpData['expires_at']);
        if (time() > $expiresAt) {
            // OTP has expired, delete it
            $stmt = $conn->prepare("
                DELETE FROM otp_codes 
                WHERE id = :id
            ");
            $stmt->bindParam(':id', $otpData['id']);
            $stmt->execute();
            
            return false;
        }
        
        // Check if max attempts reached
        if ($otpData['attempts'] >= MAX_OTP_ATTEMPTS) {
            // Max attempts reached, delete OTP
            $stmt = $conn->prepare("
                DELETE FROM otp_codes 
                WHERE id = :id
            ");
            $stmt->bindParam(':id', $otpData['id']);
            $stmt->execute();
            
            return false;
        }
        
        // Increment attempts
        $stmt = $conn->prepare("
            UPDATE otp_codes 
            SET attempts = attempts + 1
            WHERE id = :id
        ");
        $stmt->bindParam(':id', $otpData['id']);
        $stmt->execute();
        
        // Verify OTP
        if ($otpData['otp'] === $otp) {
            // OTP is valid, delete it (one-time use)
            $stmt = $conn->prepare("
                DELETE FROM otp_codes 
                WHERE id = :id
            ");
            $stmt->bindParam(':id', $otpData['id']);
            $stmt->execute();
            
            return true;
        }
        
        return false;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Send OTP via email
 * 
 * @param string $email Recipient email
 * @param string $otp One-time password
 * @param string $name Recipient name
 * @return bool True on success, false on failure
 */
function sendOTPEmail($email, $otp, $name) {
    $subject = 'Kode Verifikasi KikaZen Ship';
    
    $message = "
    <html>
    <head>
        <title>Kode Verifikasi KikaZen Ship</title>
    </head>
    <body>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
            <div style='background-color: #007bff; color: white; padding: 10px; text-align: center;'>
                <h2>KikaZen Ship</h2>
            </div>
            <div style='padding: 20px; border: 1px solid #ddd; border-top: none;'>
                <p>Halo $name,</p>
                <p>Berikut adalah kode verifikasi untuk login ke akun KikaZen Ship Anda:</p>
                <div style='background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; margin: 20px 0;'>
                    <strong>$otp</strong>
                </div>
                <p>Kode ini akan kedaluwarsa dalam 5 menit.</p>
                <p>Jika Anda tidak meminta kode ini, abaikan email ini.</p>
                <p>Terima kasih,<br>Tim KikaZen Ship</p>
            </div>
            <div style='text-align: center; margin-top: 20px; color: #777; font-size: 12px;'>
                <p>&copy; " . date('Y') . " KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Set email headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: KikaZen Ship <<EMAIL>>" . "\r\n";
    
    // Send email
    return mail($email, $subject, $message, $headers);
}

/**
 * Send OTP via SMS (placeholder function)
 * 
 * @param string $phone Recipient phone number
 * @param string $otp One-time password
 * @return bool True on success, false on failure
 */
function sendOTPSMS($phone, $otp) {
    // This is a placeholder function
    // In a real implementation, you would integrate with an SMS gateway
    
    // For now, just log the OTP
    error_log("SMS OTP for $phone: $otp");
    
    // Simulate success
    return true;
}

/**
 * Enable 2FA for a user
 * 
 * @param PDO $conn Database connection
 * @param int $userId User ID
 * @param string $method 2FA method (email, sms)
 * @param string $userType User type (user, driver, owner, admin)
 * @return bool True on success, false on failure
 */
function enable2FA($conn, $userId, $method = 'email', $userType = 'user') {
    try {
        // Update user's 2FA settings
        $tableName = '';
        $idColumn = '';
        
        switch ($userType) {
            case 'user':
                $tableName = 'users';
                $idColumn = 'user_id';
                break;
            case 'driver':
                $tableName = 'drivers';
                $idColumn = 'driver_id';
                break;
            case 'owner':
                $tableName = 'restaurant_owners';
                $idColumn = 'owner_id';
                break;
            case 'admin':
                $tableName = 'admins';
                $idColumn = 'admin_id';
                break;
            default:
                return false;
        }
        
        $stmt = $conn->prepare("
            UPDATE $tableName 
            SET two_factor_enabled = 1, two_factor_method = :method
            WHERE $idColumn = :user_id
        ");
        $stmt->bindParam(':method', $method);
        $stmt->bindParam(':user_id', $userId);
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Disable 2FA for a user
 * 
 * @param PDO $conn Database connection
 * @param int $userId User ID
 * @param string $userType User type (user, driver, owner, admin)
 * @return bool True on success, false on failure
 */
function disable2FA($conn, $userId, $userType = 'user') {
    try {
        // Update user's 2FA settings
        $tableName = '';
        $idColumn = '';
        
        switch ($userType) {
            case 'user':
                $tableName = 'users';
                $idColumn = 'user_id';
                break;
            case 'driver':
                $tableName = 'drivers';
                $idColumn = 'driver_id';
                break;
            case 'owner':
                $tableName = 'restaurant_owners';
                $idColumn = 'owner_id';
                break;
            case 'admin':
                $tableName = 'admins';
                $idColumn = 'admin_id';
                break;
            default:
                return false;
        }
        
        $stmt = $conn->prepare("
            UPDATE $tableName 
            SET two_factor_enabled = 0, two_factor_method = NULL
            WHERE $idColumn = :user_id
        ");
        $stmt->bindParam(':user_id', $userId);
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}
