<?php
/**
 * Authentication functions for KikaZen Ship
 */

// Start session if not already started
function startSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

// Register a new user
function registerUser($name, $email, $password, $phone, $address = '') {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    // Check if email already exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        return ['success' => false, 'message' => 'Email sudah terdaftar'];
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new user
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, phone, address) VALUES (?, ?, ?, ?, ?)");

    if ($stmt->execute([$name, $email, $hashedPassword, $phone, $address])) {
        return ['success' => true, 'user_id' => $conn->lastInsertId()];
    } else {
        return ['success' => false, 'message' => 'Pendaftaran gagal'];
    }
}

// Register a new driver
function registerDriver($name, $email, $password, $phone, $vehicleType, $licensePlate = '') {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    // Check if email already exists
    $stmt = $conn->prepare("SELECT driver_id FROM drivers WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        return ['success' => false, 'message' => 'Email sudah terdaftar'];
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new driver
    $stmt = $conn->prepare("INSERT INTO drivers (name, email, password, phone, vehicle_type, license_plate) VALUES (?, ?, ?, ?, ?, ?)");

    if ($stmt->execute([$name, $email, $hashedPassword, $phone, $vehicleType, $licensePlate])) {
        return ['success' => true, 'driver_id' => $conn->lastInsertId()];
    } else {
        return ['success' => false, 'message' => 'Pendaftaran gagal'];
    }
}

// Register a new restaurant owner
function registerRestaurantOwner($name, $email, $password, $phone, $address = '') {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    // Check if email already exists
    $stmt = $conn->prepare("SELECT owner_id FROM restaurant_owners WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        return ['success' => false, 'message' => 'Email sudah terdaftar'];
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new restaurant owner
    $stmt = $conn->prepare("INSERT INTO restaurant_owners (name, email, password, phone, address) VALUES (?, ?, ?, ?, ?)");

    if ($stmt->execute([$name, $email, $hashedPassword, $phone, $address])) {
        return ['success' => true, 'owner_id' => $conn->lastInsertId()];
    } else {
        return ['success' => false, 'message' => 'Pendaftaran gagal'];
    }
}

// Register a new admin
function registerAdmin($name, $email, $password, $role = 'admin') {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    // Check if email already exists
    $stmt = $conn->prepare("SELECT admin_id FROM admins WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        return ['success' => false, 'message' => 'Email sudah terdaftar'];
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new admin
    $stmt = $conn->prepare("INSERT INTO admins (name, email, password, role) VALUES (?, ?, ?, ?)");

    if ($stmt->execute([$name, $email, $hashedPassword, $role])) {
        return ['success' => true, 'admin_id' => $conn->lastInsertId()];
    } else {
        return ['success' => false, 'message' => 'Pendaftaran gagal'];
    }
}

// User login
function loginUser($email, $password) {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    $stmt = $conn->prepare("SELECT user_id, name, password FROM users WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        if (password_verify($password, $user['password'])) {
            startSession();
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $email;
            $_SESSION['user_type'] = 'customer';
            return ['success' => true, 'user' => $user];
        }
    }

    return ['success' => false, 'message' => 'Email atau kata sandi tidak valid'];
}

// Driver login
function loginDriver($email, $password) {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    $stmt = $conn->prepare("SELECT driver_id, name, password FROM drivers WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        $driver = $stmt->fetch();
        if (password_verify($password, $driver['password'])) {
            startSession();
            $_SESSION['driver_id'] = $driver['driver_id'];
            $_SESSION['driver_name'] = $driver['name'];
            $_SESSION['driver_email'] = $email;
            $_SESSION['user_type'] = 'driver';
            return ['success' => true, 'driver' => $driver];
        }
    }

    return ['success' => false, 'message' => 'Email atau kata sandi tidak valid'];
}

// Admin login
function loginAdmin($email, $password) {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    $stmt = $conn->prepare("SELECT admin_id, name, password, role FROM admins WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        $admin = $stmt->fetch();
        if (password_verify($password, $admin['password'])) {
            startSession();
            $_SESSION['admin_id'] = $admin['admin_id'];
            $_SESSION['admin_name'] = $admin['name'];
            $_SESSION['admin_email'] = $email;
            $_SESSION['admin_role'] = $admin['role'];
            $_SESSION['user_type'] = 'admin';
            return ['success' => true, 'admin' => $admin];
        }
    }

    return ['success' => false, 'message' => 'Email atau kata sandi tidak valid'];
}

// Restaurant owner login
function loginRestaurantOwner($email, $password) {
    require_once __DIR__ . '/../config/database.php';
    $conn = connectDB();

    $stmt = $conn->prepare("SELECT owner_id, name, password, status FROM restaurant_owners WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->rowCount() > 0) {
        $owner = $stmt->fetch();
        if (password_verify($password, $owner['password'])) {
            // Check if account is active
            if ($owner['status'] !== 'active') {
                return ['success' => false, 'message' => 'Akun Anda sedang menunggu persetujuan atau telah ditangguhkan. Silakan hubungi admin.'];
            }

            startSession();
            $_SESSION['owner_id'] = $owner['owner_id'];
            $_SESSION['owner_name'] = $owner['name'];
            $_SESSION['owner_email'] = $email;
            $_SESSION['user_type'] = 'owner';
            return ['success' => true, 'owner' => $owner];
        }
    }

    return ['success' => false, 'message' => 'Email atau kata sandi tidak valid'];
}

// Check if user is logged in
function isUserLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']);
}

// Check if driver is logged in
function isDriverLoggedIn() {
    startSession();
    return isset($_SESSION['driver_id']);
}

// Check if admin is logged in
function isAdminLoggedIn() {
    startSession();
    return isset($_SESSION['admin_id']);
}

// Check if restaurant owner is logged in
function isRestaurantOwnerLoggedIn() {
    startSession();
    return isset($_SESSION['owner_id']);
}

// Get current user type
function getCurrentUserType() {
    startSession();
    return $_SESSION['user_type'] ?? null;
}

// Logout
function logout() {
    startSession();
    session_unset();
    session_destroy();
    return true;
}
