<?php
/**
 * Cache management functions for KikaZen Ship
 * 
 * This file contains functions for caching data to improve application performance.
 * It implements a simple file-based caching system that can be used throughout the application.
 */

// Define cache directory
define('CACHE_DIR', __DIR__ . '/../cache');

// Ensure cache directory exists
if (!file_exists(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

/**
 * Get data from cache
 * 
 * @param string $key Cache key
 * @param int $ttl Time to live in seconds (default: 3600 = 1 hour)
 * @return mixed|null Cached data or null if not found or expired
 */
function getCache($key, $ttl = 3600) {
    $cacheFile = getCacheFilePath($key);
    
    if (!file_exists($cacheFile)) {
        return null;
    }
    
    $data = file_get_contents($cacheFile);
    if ($data === false) {
        return null;
    }
    
    $cacheData = json_decode($data, true);
    if (!is_array($cacheData) || !isset($cacheData['time']) || !isset($cacheData['data'])) {
        return null;
    }
    
    // Check if cache is expired
    if (time() - $cacheData['time'] > $ttl) {
        // Cache expired, delete the file
        @unlink($cacheFile);
        return null;
    }
    
    return $cacheData['data'];
}

/**
 * Set data to cache
 * 
 * @param string $key Cache key
 * @param mixed $data Data to cache
 * @param int $ttl Time to live in seconds (default: 3600 = 1 hour)
 * @return bool True on success, false on failure
 */
function setCache($key, $data, $ttl = 3600) {
    $cacheFile = getCacheFilePath($key);
    
    $cacheData = [
        'time' => time(),
        'ttl' => $ttl,
        'data' => $data
    ];
    
    $jsonData = json_encode($cacheData);
    if ($jsonData === false) {
        return false;
    }
    
    return file_put_contents($cacheFile, $jsonData) !== false;
}

/**
 * Delete a specific cache entry
 * 
 * @param string $key Cache key
 * @return bool True on success, false on failure
 */
function deleteCache($key) {
    $cacheFile = getCacheFilePath($key);
    
    if (file_exists($cacheFile)) {
        return @unlink($cacheFile);
    }
    
    return true;
}

/**
 * Clear all cache entries
 * 
 * @return bool True on success, false on failure
 */
function clearCache() {
    $files = glob(CACHE_DIR . '/*.cache');
    
    if ($files === false) {
        return false;
    }
    
    $success = true;
    foreach ($files as $file) {
        if (!@unlink($file)) {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Clear expired cache entries
 * 
 * @return bool True on success, false on failure
 */
function clearExpiredCache() {
    $files = glob(CACHE_DIR . '/*.cache');
    
    if ($files === false) {
        return false;
    }
    
    $success = true;
    foreach ($files as $file) {
        $data = file_get_contents($file);
        if ($data === false) {
            continue;
        }
        
        $cacheData = json_decode($data, true);
        if (!is_array($cacheData) || !isset($cacheData['time']) || !isset($cacheData['ttl'])) {
            continue;
        }
        
        // Check if cache is expired
        if (time() - $cacheData['time'] > $cacheData['ttl']) {
            if (!@unlink($file)) {
                $success = false;
            }
        }
    }
    
    return $success;
}

/**
 * Get cache file path for a key
 * 
 * @param string $key Cache key
 * @return string Cache file path
 */
function getCacheFilePath($key) {
    // Sanitize key to use as filename
    $safeKey = preg_replace('/[^a-zA-Z0-9_]/', '_', $key);
    return CACHE_DIR . '/' . $safeKey . '.cache';
}

/**
 * Cache a database query result
 * 
 * @param PDO $conn Database connection
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param int $ttl Time to live in seconds (default: 3600 = 1 hour)
 * @return array Query result
 */
function cacheQuery($conn, $query, $params = [], $ttl = 3600) {
    // Create a cache key based on the query and parameters
    $cacheKey = 'query_' . md5($query . serialize($params));
    
    // Try to get from cache first
    $cachedResult = getCache($cacheKey, $ttl);
    if ($cachedResult !== null) {
        return $cachedResult;
    }
    
    // Cache miss, execute the query
    $stmt = $conn->prepare($query);
    
    // Bind parameters if any
    foreach ($params as $key => $value) {
        $stmt->bindValue(is_numeric($key) ? $key + 1 : $key, $value);
    }
    
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Cache the result
    setCache($cacheKey, $result, $ttl);
    
    return $result;
}

/**
 * Invalidate cache for a specific table
 * 
 * @param string $table Table name
 * @return bool True on success, false on failure
 */
function invalidateTableCache($table) {
    $files = glob(CACHE_DIR . '/query_*.cache');
    
    if ($files === false) {
        return false;
    }
    
    $success = true;
    foreach ($files as $file) {
        $data = file_get_contents($file);
        if ($data === false) {
            continue;
        }
        
        $cacheData = json_decode($data, true);
        if (!is_array($cacheData) || !isset($cacheData['data'])) {
            continue;
        }
        
        // Check if the cached query contains the table name
        // This is a simple approach and might have false positives
        $cacheKey = basename($file, '.cache');
        if (strpos($cacheKey, $table) !== false) {
            if (!@unlink($file)) {
                $success = false;
            }
        }
    }
    
    return $success;
}
