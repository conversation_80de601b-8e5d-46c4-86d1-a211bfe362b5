<?php
/**
 * Security functions for KikaZen Ship
 * 
 * This file contains functions for enhancing application security.
 */

// Define security constants
define('HASH_ALGO', PASSWORD_ARGON2ID);
define('HASH_OPTIONS', ['memory_cost' => 65536, 'time_cost' => 4, 'threads' => 3]);
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_EXPIRY', 3600); // 1 hour

/**
 * Initialize security settings
 */
function initSecurity() {
    // Set secure session parameters
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isSecureConnection() ? 1 : 0);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    
    // Set session name
    session_name('KikaZenShipSession');
    
    // Regenerate session ID periodically
    if (isset($_SESSION['last_regeneration'])) {
        if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            regenerateSession();
        }
    } else {
        $_SESSION['last_regeneration'] = time();
    }
}

/**
 * Check if the current connection is secure (HTTPS)
 * 
 * @return bool True if connection is secure, false otherwise
 */
function isSecureConnection() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') || 
           (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
}

/**
 * Regenerate session ID
 */
function regenerateSession() {
    // Regenerate session ID
    session_regenerate_id(true);
    
    // Update last regeneration time
    $_SESSION['last_regeneration'] = time();
}

/**
 * Hash a password securely
 * 
 * @param string $password Plain text password
 * @return string Hashed password
 */
function hashPassword($password) {
    return password_hash($password, HASH_ALGO, HASH_OPTIONS);
}

/**
 * Verify a password against a hash
 * 
 * @param string $password Plain text password
 * @param string $hash Hashed password
 * @return bool True if password matches hash, false otherwise
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Check if password needs rehash
 * 
 * @param string $hash Hashed password
 * @return bool True if password needs rehash, false otherwise
 */
function passwordNeedsRehash($hash) {
    return password_needs_rehash($hash, HASH_ALGO, HASH_OPTIONS);
}

/**
 * Generate a CSRF token
 * 
 * @param string $formName Form name to generate token for
 * @return string CSRF token
 */
function generateCsrfToken($formName) {
    $token = bin2hex(random_bytes(32));
    
    $_SESSION['csrf_tokens'][$formName] = [
        'token' => $token,
        'expires' => time() + CSRF_TOKEN_EXPIRY
    ];
    
    return $token;
}

/**
 * Verify a CSRF token
 * 
 * @param string $formName Form name to verify token for
 * @param string $token Token to verify
 * @return bool True if token is valid, false otherwise
 */
function verifyCsrfToken($formName, $token) {
    if (!isset($_SESSION['csrf_tokens'][$formName])) {
        return false;
    }
    
    $storedToken = $_SESSION['csrf_tokens'][$formName];
    
    // Check if token has expired
    if (time() > $storedToken['expires']) {
        unset($_SESSION['csrf_tokens'][$formName]);
        return false;
    }
    
    // Check if token matches
    if (hash_equals($storedToken['token'], $token)) {
        // Token used, remove it (one-time use)
        unset($_SESSION['csrf_tokens'][$formName]);
        return true;
    }
    
    return false;
}

/**
 * Clean expired CSRF tokens
 */
function cleanExpiredCsrfTokens() {
    if (!isset($_SESSION['csrf_tokens'])) {
        return;
    }
    
    foreach ($_SESSION['csrf_tokens'] as $formName => $tokenData) {
        if (time() > $tokenData['expires']) {
            unset($_SESSION['csrf_tokens'][$formName]);
        }
    }
}

/**
 * Sanitize input data
 * 
 * @param mixed $data Data to sanitize
 * @return mixed Sanitized data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeInput($value);
        }
        return $data;
    }
    
    // Convert special characters to HTML entities
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate a random token
 * 
 * @param int $length Token length
 * @return string Random token
 */
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Encrypt sensitive data
 * 
 * @param string $data Data to encrypt
 * @param string $key Encryption key
 * @return string Encrypted data
 */
function encryptData($data, $key) {
    // Generate a random initialization vector
    $iv = random_bytes(16);
    
    // Encrypt the data
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    
    // Combine the IV and encrypted data
    return base64_encode($iv . $encrypted);
}

/**
 * Decrypt sensitive data
 * 
 * @param string $data Data to decrypt
 * @param string $key Encryption key
 * @return string|false Decrypted data or false on failure
 */
function decryptData($data, $key) {
    // Decode the combined data
    $data = base64_decode($data);
    
    // Extract the IV (first 16 bytes)
    $iv = substr($data, 0, 16);
    
    // Extract the encrypted data (everything after the IV)
    $encrypted = substr($data, 16);
    
    // Decrypt the data
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

/**
 * Generate a secure API key
 * 
 * @return string API key
 */
function generateApiKey() {
    return bin2hex(random_bytes(32));
}

/**
 * Log security events
 * 
 * @param string $event Event description
 * @param string $level Event level (info, warning, error)
 * @param array $data Additional data
 */
function logSecurityEvent($event, $level = 'info', $data = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'level' => $level,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'user_id' => $_SESSION['user_id'] ?? $_SESSION['driver_id'] ?? $_SESSION['owner_id'] ?? $_SESSION['admin_id'] ?? 'guest',
        'data' => $data
    ];
    
    $logFile = __DIR__ . '/../logs/security.log';
    
    // Ensure log directory exists
    $logDir = dirname($logFile);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // Append to log file
    file_put_contents($logFile, json_encode($logData) . PHP_EOL, FILE_APPEND);
}
