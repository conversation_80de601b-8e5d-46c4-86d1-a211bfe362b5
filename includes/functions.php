<?php
/**
 * Helper functions for KikaZen Ship
 */

// Format currency
function formatCurrency($amount) {
    return 'Rp' . number_format($amount, 0, ',', '.');
}

// Format date
function formatDate($date, $format = 'd F Y') {
    return date($format, strtotime($date));
}

// Format time
function formatTime($time, $format = 'H:i') {
    return date($format, strtotime($time));
}

// Format datetime
function formatDateTime($datetime, $format = 'd F Y H:i') {
    return date($format, strtotime($datetime));
}

// Get order status text
function getOrderStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'Menunggu';
        case 'confirmed':
            return 'Dikonfirmasi';
        case 'preparing':
            return 'Sedang Dipersiapkan';
        case 'ready_for_pickup':
            return 'Siap Diambil';
        case 'picked_up':
            return 'Diambil';
        case 'on_the_way':
            return '<PERSON><PERSON>';
        case 'delivered':
            return 'Terkirim';
        case 'cancelled':
            return 'Dibatalkan';
        default:
            return $status;
    }
}

// Get order status badge class
function getOrderStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning text-dark';
        case 'confirmed':
            return 'bg-info text-dark';
        case 'preparing':
            return 'bg-primary';
        case 'ready_for_pickup':
            return 'bg-primary';
        case 'picked_up':
            return 'bg-info';
        case 'on_the_way':
            return 'bg-info';
        case 'delivered':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// Get payment method text
function getPaymentMethodText($method) {
    switch ($method) {
        case 'cash':
            return 'Tunai';
        case 'credit_card':
            return 'Kartu Kredit';
        case 'debit_card':
            return 'Kartu Debit';
        case 'e_wallet':
            return 'E-Wallet';
        case 'bank_transfer':
            return 'Transfer Bank';
        default:
            return $method;
    }
}

// Get payment status text
function getPaymentStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'Menunggu Pembayaran';
        case 'paid':
            return 'Dibayar';
        case 'failed':
            return 'Gagal';
        case 'refunded':
            return 'Dikembalikan';
        default:
            return $status;
    }
}

// Get payment status badge class
function getPaymentStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning text-dark';
        case 'paid':
            return 'bg-success';
        case 'failed':
            return 'bg-danger';
        case 'refunded':
            return 'bg-info';
        default:
            return 'bg-secondary';
    }
}

// Generate random string
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

// Generate order number
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . generateRandomString(6);
}

// Calculate order total
function calculateOrderTotal($items) {
    $total = 0;
    foreach ($items as $item) {
        $total += $item['price'] * $item['quantity'];
    }
    return $total;
}

// Calculate delivery fee
function calculateDeliveryFee($distance) {
    // Base fee
    $fee = 10000;

    // Additional fee per km after 3km
    if ($distance > 3) {
        $fee += ($distance - 3) * 5000;
    }

    return $fee;
}

// Calculate estimated delivery time
function calculateEstimatedDeliveryTime($distance) {
    // Base time in minutes
    $time = 15;

    // Additional time per km
    $time += $distance * 3;

    return $time;
}

// Get distance between two coordinates
function getDistance($lat1, $lon1, $lat2, $lon2) {
    $earthRadius = 6371; // in km

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    $distance = $earthRadius * $c;

    return $distance;
}

// Validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Validate phone number
function validatePhone($phone) {
    // Remove non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);

    // Check if phone number is valid
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Get user type text
function getUserTypeText($type) {
    switch ($type) {
        case 'customer':
            return 'Pelanggan';
        case 'driver':
            return 'Pengemudi';
        case 'owner':
            return 'Pemilik Restoran';
        case 'admin':
            return 'Admin';
        default:
            return $type;
    }
}

// Get user dashboard URL
function getUserDashboardURL($type) {
    switch ($type) {
        case 'customer':
            return 'user/dashboard.php';
        case 'driver':
            return 'driver/dashboard.php';
        case 'owner':
            return 'owner/dashboard.php';
        case 'admin':
            return 'admin/dashboard.php';
        default:
            return 'index.php';
    }
}

// Check if user has permission
function hasPermission($permission) {
    // Admin has all permissions
    if (isset($_SESSION['admin_id'])) {
        return true;
    }

    // Check specific permissions
    switch ($permission) {
        case 'view_restaurant':
            return isset($_SESSION['admin_id']) || isset($_SESSION['owner_id']);
        case 'edit_restaurant':
            return isset($_SESSION['admin_id']) || isset($_SESSION['owner_id']);
        case 'view_order':
            return isset($_SESSION['admin_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['user_id']);
        case 'edit_order':
            return isset($_SESSION['admin_id']) || isset($_SESSION['owner_id']);
        case 'view_user':
            return isset($_SESSION['admin_id']);
        case 'edit_user':
            return isset($_SESSION['admin_id']);
        default:
            return false;
    }
}

// Get current page name
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF']);
}

// Check if current page is active
function isPageActive($page) {
    return getCurrentPage() == $page;
}

// Get base URL
function getBaseURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['PHP_SELF']);

    return $protocol . '://' . $host . $path;
}

// Redirect to URL
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

// Set flash message
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

// Get flash message
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }

    return null;
}

// Display flash message
function displayFlashMessage() {
    $message = getFlashMessage();

    if ($message) {
        $type = $message['type'];
        $text = $message['message'];

        echo '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">';
        echo $text;
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        echo '</div>';
    }
}

// Send notification to driver
function sendDriverNotification($driver_id, $order_id, $type, $message) {
    $conn = connectDB();

    $stmt = $conn->prepare("
        INSERT INTO notifications (
            driver_id, order_id, type, message
        ) VALUES (
            :driver_id, :order_id, :type, :message
        )
    ");

    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->bindParam(':order_id', $order_id);
    $stmt->bindParam(':type', $type);
    $stmt->bindParam(':message', $message);

    return $stmt->execute();
}

// Send notification to all available drivers
function notifyAvailableDrivers($order_id, $restaurant_id, $type, $message) {
    $conn = connectDB();

    // Get restaurant location
    $stmt = $conn->prepare("
        SELECT latitude, longitude
        FROM restaurants
        WHERE restaurant_id = :restaurant_id
    ");
    $stmt->bindParam(':restaurant_id', $restaurant_id);
    $stmt->execute();
    $restaurant = $stmt->fetch();

    if (!$restaurant) {
        return false;
    }

    // Find available drivers within 10km radius
    $stmt = $conn->prepare("
        SELECT driver_id
        FROM drivers
        WHERE status = 'available'
        AND (
            6371 * acos(
                cos(radians(:latitude)) *
                cos(radians(current_latitude)) *
                cos(radians(current_longitude) - radians(:longitude)) +
                sin(radians(:latitude)) *
                sin(radians(current_latitude))
            )
        ) <= 10
    ");
    $stmt->bindParam(':latitude', $restaurant['latitude']);
    $stmt->bindParam(':longitude', $restaurant['longitude']);
    $stmt->execute();
    $drivers = $stmt->fetchAll();

    // Send notification to each available driver
    foreach ($drivers as $driver) {
        sendDriverNotification($driver['driver_id'], $order_id, $type, $message);
    }

    return count($drivers) > 0;
}

// Get unread notifications count
function getUnreadNotificationsCount($driver_id) {
    $conn = connectDB();

    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM notifications
        WHERE driver_id = :driver_id
        AND is_read = 0
    ");
    $stmt->bindParam(':driver_id', $driver_id);
    $stmt->execute();

    return $stmt->fetch()['count'] ?? 0;
}

// Upload image function
function uploadImage($file, $directory, $prefix = '') {
    // Check if file was uploaded without errors
    if (isset($file) && $file['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $maxSize = 5 * 1024 * 1024; // 5MB

        // Validate file type
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'message' => 'Hanya file JPG, PNG, dan GIF yang diperbolehkan'];
        }

        // Validate file size
        if ($file['size'] > $maxSize) {
            return ['success' => false, 'message' => 'Ukuran file tidak boleh lebih dari 5MB'];
        }

        // Simplified path handling - use direct path
        $uploadDir = __DIR__ . '/../' . $directory . '/';

        // Log the path for debugging
        error_log("Upload directory: " . $uploadDir);

        if (!file_exists($uploadDir)) {
            error_log("Directory doesn't exist, creating: " . $uploadDir);
            if (!mkdir($uploadDir, 0777, true)) {
                error_log("Failed to create directory: " . $uploadDir);
                return ['success' => false, 'message' => 'Gagal membuat direktori upload: ' . $uploadDir];
            }
        }

        // Generate unique filename
        $fileName = $prefix . '_' . time() . '_' . basename($file['name']);
        $uploadPath = $uploadDir . $fileName;

        // Log the full path for debugging
        error_log("Upload path: " . $uploadPath);
        error_log("Temp file: " . $file['tmp_name']);

        // Try alternative methods if move_uploaded_file fails
        $uploadSuccess = false;

        // First try move_uploaded_file (only works for actual uploads)
        if (is_uploaded_file($file['tmp_name'])) {
            $uploadSuccess = move_uploaded_file($file['tmp_name'], $uploadPath);
            error_log("move_uploaded_file result: " . ($uploadSuccess ? "success" : "failed"));
        } else {
            // For testing or if the file is not from an upload
            error_log("File is not from an upload form, trying copy instead");
            $uploadSuccess = copy($file['tmp_name'], $uploadPath);
            error_log("copy result: " . ($uploadSuccess ? "success" : "failed"));
        }

        if ($uploadSuccess) {
            // Make sure the file has correct permissions
            chmod($uploadPath, 0644);
            error_log("File uploaded successfully to: " . $uploadPath);

            // Return relative path for use in HTML
            return [
                'success' => true,
                'file_path' => $directory . '/' . $fileName
            ];
        } else {
            // Get error details
            $error = error_get_last();
            $errorMsg = $error ? $error['message'] : 'Unknown error';
            error_log("Failed to upload file: " . $errorMsg);

            // Check if temp file exists and is readable
            if (!file_exists($file['tmp_name'])) {
                error_log("Temp file does not exist: " . $file['tmp_name']);
            } elseif (!is_readable($file['tmp_name'])) {
                error_log("Temp file is not readable: " . $file['tmp_name']);
            }

            // Check if upload directory is writable
            if (!is_writable($uploadDir)) {
                error_log("Upload directory is not writable: " . $uploadDir);
            }

            // Try one more method - file_put_contents
            error_log("Trying file_put_contents as last resort");
            $fileContent = file_get_contents($file['tmp_name']);
            if ($fileContent !== false) {
                if (file_put_contents($uploadPath, $fileContent)) {
                    chmod($uploadPath, 0644);
                    error_log("File uploaded successfully using file_put_contents");
                    return [
                        'success' => true,
                        'file_path' => $directory . '/' . $fileName
                    ];
                }
            }

            return [
                'success' => false,
                'message' => 'Gagal mengunggah file: ' . $errorMsg,
                'upload_path' => $uploadPath,
                'tmp_name' => $file['tmp_name']
            ];
        }
    } else {
        // Log the error code
        if (isset($file)) {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => 'Ukuran file melebihi upload_max_filesize di php.ini',
                UPLOAD_ERR_FORM_SIZE => 'Ukuran file melebihi MAX_FILE_SIZE yang ditentukan dalam form HTML',
                UPLOAD_ERR_PARTIAL => 'File hanya terunggah sebagian',
                UPLOAD_ERR_NO_FILE => 'Tidak ada file yang diunggah',
                UPLOAD_ERR_NO_TMP_DIR => 'Direktori sementara tidak ditemukan',
                UPLOAD_ERR_CANT_WRITE => 'Gagal menulis file ke disk',
                UPLOAD_ERR_EXTENSION => 'Unggahan file dihentikan oleh ekstensi PHP'
            ];

            $errorCode = $file['error'];
            $errorMessage = isset($errorMessages[$errorCode]) ? $errorMessages[$errorCode] : 'Kode error tidak dikenal: ' . $errorCode;
            error_log("File upload error: " . $errorMessage);

            return ['success' => false, 'message' => 'Error upload: ' . $errorMessage];
        }
    }

    return ['success' => false, 'message' => 'Tidak ada file yang diunggah atau terjadi kesalahan'];
}

/**
 * Create chat room for an order and add all participants
 */
function createOrderChatRoom($order_id) {
    $conn = connectDB();

    try {
        // Check if chat room already exists for this order
        $stmt = $conn->prepare("
            SELECT room_id FROM chat_rooms
            WHERE order_id = :order_id AND room_type = 'order'
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Chat room already exists
            $room = $stmt->fetch();
            return $room['room_id'];
        }

        // Get order details
        $stmt = $conn->prepare("
            SELECT o.*, r.owner_id, u.name as customer_name, ro.name as owner_name
            FROM orders o
            JOIN restaurants r ON o.restaurant_id = r.restaurant_id
            JOIN restaurant_owners ro ON r.owner_id = ro.owner_id
            JOIN users u ON o.user_id = u.user_id
            WHERE o.order_id = :order_id
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        $order = $stmt->fetch();

        if (!$order) {
            return false;
        }

        // Begin transaction
        $conn->beginTransaction();

        // Create chat room
        $stmt = $conn->prepare("
            INSERT INTO chat_rooms (order_id, room_type, status)
            VALUES (:order_id, 'order', 'active')
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        $room_id = $conn->lastInsertId();

        // Add customer as participant
        $stmt = $conn->prepare("
            INSERT INTO chat_participants (room_id, user_type, user_id)
            VALUES (:room_id, 'customer', :user_id)
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':user_id', $order['user_id']);
        $stmt->execute();

        // Add restaurant owner as participant
        $stmt = $conn->prepare("
            INSERT INTO chat_participants (room_id, user_type, user_id)
            VALUES (:room_id, 'restaurant_owner', :owner_id)
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':owner_id', $order['owner_id']);
        $stmt->execute();

        // Add driver as participant if assigned
        if ($order['driver_id']) {
            $stmt = $conn->prepare("
                INSERT INTO chat_participants (room_id, user_type, user_id)
                VALUES (:room_id, 'driver', :driver_id)
            ");
            $stmt->bindParam(':room_id', $room_id);
            $stmt->bindParam(':driver_id', $order['driver_id']);
            $stmt->execute();
        }

        // Add system welcome message
        $welcome_message = "Chat room untuk Pesanan #{$order_id} telah dibuat. Silakan berkomunikasi mengenai pesanan ini.";
        $stmt = $conn->prepare("
            INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
            VALUES (:room_id, 'system', 0, :message_text, 'system')
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':message_text', $welcome_message);
        $stmt->execute();

        // Commit transaction
        $conn->commit();

        return $room_id;

    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log("Error creating chat room for order {$order_id}: " . $e->getMessage());
        return false;
    }
}

/**
 * Add driver to existing order chat room
 */
function addDriverToChatRoom($order_id, $driver_id) {
    $conn = connectDB();

    try {
        // Get chat room for this order
        $stmt = $conn->prepare("
            SELECT room_id FROM chat_rooms
            WHERE order_id = :order_id AND room_type = 'order'
        ");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        $room = $stmt->fetch();

        if (!$room) {
            // Create chat room if it doesn't exist
            $room_id = createOrderChatRoom($order_id);
            return $room_id !== false;
        }

        $room_id = $room['room_id'];

        // Check if driver is already a participant
        $stmt = $conn->prepare("
            SELECT participant_id FROM chat_participants
            WHERE room_id = :room_id AND user_type = 'driver' AND user_id = :driver_id
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':driver_id', $driver_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Driver already in chat room
            return true;
        }

        // Add driver as participant
        $stmt = $conn->prepare("
            INSERT INTO chat_participants (room_id, user_type, user_id)
            VALUES (:room_id, 'driver', :driver_id)
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':driver_id', $driver_id);
        $stmt->execute();

        // Get driver name
        $stmt = $conn->prepare("SELECT name FROM drivers WHERE driver_id = :driver_id");
        $stmt->bindParam(':driver_id', $driver_id);
        $stmt->execute();
        $driver = $stmt->fetch();
        $driver_name = $driver ? $driver['name'] : 'Driver';

        // Add system message about driver joining
        $join_message = "Driver {$driver_name} telah bergabung dalam chat untuk pesanan #{$order_id}.";
        $stmt = $conn->prepare("
            INSERT INTO chat_messages (room_id, sender_type, sender_id, message_text, message_type)
            VALUES (:room_id, 'system', 0, :message_text, 'system')
        ");
        $stmt->bindParam(':room_id', $room_id);
        $stmt->bindParam(':message_text', $join_message);
        $stmt->execute();

        return true;

    } catch (PDOException $e) {
        error_log("Error adding driver to chat room for order {$order_id}: " . $e->getMessage());
        return false;
    }
}

/**
 * Get or create chat room for an order
 */
function getOrCreateOrderChatRoom($order_id) {
    $conn = connectDB();

    // Check if chat room already exists
    $stmt = $conn->prepare("
        SELECT room_id FROM chat_rooms
        WHERE order_id = :order_id AND room_type = 'order'
    ");
    $stmt->bindParam(':order_id', $order_id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $room = $stmt->fetch();
        return $room['room_id'];
    }

    // Create new chat room
    return createOrderChatRoom($order_id);
}
?>
