<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$conn = connectDB();

// Get sustainability initiatives
$stmt = $conn->prepare("
    SELECT * FROM sustainability_initiatives 
    WHERE is_active = 1
    ORDER BY created_at DESC
");
$stmt->execute();
$initiatives = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get eco-friendly restaurants
$stmt = $conn->prepare("
    SELECT r.restaurant_id, r.name, r.logo, r.cuisine_type, r.address, 
           GROUP_CONCAT(DISTINCT st.tag_name SEPARATOR ', ') as sustainability_tags
    FROM restaurants r
    JOIN restaurant_sustainability_tags rst ON r.restaurant_id = rst.restaurant_id
    JOIN sustainability_tags st ON rst.tag_id = st.tag_id
    WHERE r.is_active = 1
    GROUP BY r.restaurant_id
    ORDER BY COUNT(DISTINCT rst.tag_id) DESC
    LIMIT 8
");
$stmt->execute();
$ecoFriendlyRestaurants = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get donation programs
$stmt = $conn->prepare("
    SELECT * FROM donation_programs 
    WHERE is_active = 1 AND end_date >= CURDATE()
    ORDER BY end_date ASC
");
$stmt->execute();
$donationPrograms = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get sustainability stats
$stmt = $conn->prepare("
    SELECT 
        (SELECT COUNT(*) FROM orders WHERE eco_packaging = 1) as eco_packaging_count,
        (SELECT COUNT(*) FROM orders WHERE no_cutlery = 1) as no_cutlery_count,
        (SELECT SUM(amount) FROM donations) as total_donations,
        (SELECT COUNT(DISTINCT restaurant_id) FROM restaurant_sustainability_tags) as eco_restaurant_count
");
$stmt->execute();
$stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get sustainability tips
$stmt = $conn->prepare("
    SELECT * FROM sustainability_tips 
    WHERE is_active = 1
    ORDER BY RAND()
    LIMIT 5
");
$stmt->execute();
$sustainabilityTips = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);
$userName = '';

if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keberlanjutan & Tanggung Jawab Sosial - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
        .sustainability-hero {
            background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/sustainability-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            margin-bottom: 40px;
        }
        
        .eco-badge {
            background-color: #28a745;
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 30px 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #28a745;
        }
        
        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-card .stat-text {
            color: #6c757d;
        }
        
        .initiative-card {
            height: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .initiative-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .initiative-card .card-img-top {
            height: 200px;
            object-fit: cover;
        }
        
        .donation-progress {
            height: 10px;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        
        .eco-restaurant-card {
            height: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .eco-restaurant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .eco-restaurant-card .card-img-top {
            height: 160px;
            object-fit: cover;
        }
        
        .tip-card {
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sustainability.php">Keberlanjutan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Tentang Kami</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Kontak</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="fas fa-shopping-cart me-2"></i>Keranjang
                                <span class="badge bg-danger rounded-pill">
                                    <?php
                                        $cartCount = 0;
                                        if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                                            foreach ($_SESSION['cart'] as $items) {
                                                $cartCount += count($items);
                                            }
                                        }
                                        echo $cartCount;
                                    ?>
                                </span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="driver/orders.php">Pengiriman</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="owner/orders.php">Pesanan</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">Pengguna</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-2"></i>Masuk
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-2"></i>Daftar
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="sustainability-hero">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Keberlanjutan & Tanggung Jawab Sosial</h1>
            <p class="lead mb-4">Bersama-sama kita dapat membuat perbedaan untuk lingkungan dan masyarakat.</p>
            <div class="d-flex justify-content-center">
                <a href="#initiatives" class="btn btn-success me-3">
                    <i class="fas fa-leaf me-2"></i>Inisiatif Kami
                </a>
                <a href="#donate" class="btn btn-outline-light">
                    <i class="fas fa-hand-holding-heart me-2"></i>Donasi Sekarang
                </a>
            </div>
        </div>
    </section>

    <!-- Content -->
    <div class="container py-5">
        <!-- Stats Section -->
        <section class="mb-5">
            <h2 class="text-center mb-4">Dampak Kita Bersama</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <div class="col">
                    <div class="stat-card bg-white">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-number"><?= number_format($stats['eco_packaging_count'] ?? 0) ?></div>
                        <div class="stat-text">Kemasan Ramah Lingkungan</div>
                    </div>
                </div>
                <div class="col">
                    <div class="stat-card bg-white">
                        <div class="stat-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="stat-number"><?= number_format($stats['no_cutlery_count'] ?? 0) ?></div>
                        <div class="stat-text">Alat Makan Dihemat</div>
                    </div>
                </div>
                <div class="col">
                    <div class="stat-card bg-white">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding-heart"></i>
                        </div>
                        <div class="stat-number">Rp <?= number_format($stats['total_donations'] ?? 0) ?></div>
                        <div class="stat-text">Total Donasi</div>
                    </div>
                </div>
                <div class="col">
                    <div class="stat-card bg-white">
                        <div class="stat-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="stat-number"><?= number_format($stats['eco_restaurant_count'] ?? 0) ?></div>
                        <div class="stat-text">Restoran Ramah Lingkungan</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Initiatives Section -->
        <section class="mb-5" id="initiatives">
            <h2 class="mb-4">Inisiatif Keberlanjutan Kami</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                <?php foreach ($initiatives as $initiative): ?>
                    <div class="col">
                        <div class="card initiative-card h-100">
                            <img src="<?= $initiative['image_url'] ?>" class="card-img-top" alt="<?= $initiative['title'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $initiative['title'] ?></h5>
                                <p class="card-text"><?= $initiative['description'] ?></p>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="initiative_detail.php?id=<?= $initiative['initiative_id'] ?>" class="btn btn-outline-success">
                                    <i class="fas fa-info-circle me-2"></i>Pelajari Lebih Lanjut
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Eco-Friendly Restaurants Section -->
        <section class="mb-5">
            <h2 class="mb-4">Restoran Ramah Lingkungan</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                <?php foreach ($ecoFriendlyRestaurants as $restaurant): ?>
                    <div class="col">
                        <div class="card eco-restaurant-card h-100">
                            <img src="<?= !empty($restaurant['logo']) ? $restaurant['logo'] : 'images/restaurant-placeholder.png' ?>" 
                                 class="card-img-top" alt="<?= $restaurant['name'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $restaurant['name'] ?></h5>
                                <p class="card-text">
                                    <i class="fas fa-utensils me-2"></i><?= $restaurant['cuisine_type'] ?>
                                </p>
                                <div>
                                    <?php 
                                    $tags = explode(', ', $restaurant['sustainability_tags']);
                                    foreach ($tags as $tag): 
                                    ?>
                                        <span class="eco-badge">
                                            <i class="fas fa-leaf me-1"></i><?= $tag ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <a href="restaurant_detail.php?id=<?= $restaurant['restaurant_id'] ?>" class="btn btn-success w-100">
                                    <i class="fas fa-utensils me-2"></i>Lihat Menu
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-4">
                <a href="eco_restaurants.php" class="btn btn-outline-success">
                    <i class="fas fa-list me-2"></i>Lihat Semua Restoran Ramah Lingkungan
                </a>
            </div>
        </section>

        <!-- Donation Programs Section -->
        <section class="mb-5" id="donate">
            <h2 class="mb-4">Program Donasi</h2>
            <div class="row row-cols-1 row-cols-md-2 g-4">
                <?php foreach ($donationPrograms as $program): ?>
                    <div class="col">
                        <div class="card h-100">
                            <div class="row g-0">
                                <div class="col-md-4">
                                    <img src="<?= $program['image_url'] ?>" class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="<?= $program['title'] ?>">
                                </div>
                                <div class="col-md-8">
                                    <div class="card-body">
                                        <h5 class="card-title"><?= $program['title'] ?></h5>
                                        <p class="card-text"><?= $program['description'] ?></p>
                                        
                                        <?php 
                                        $percentage = ($program['current_amount'] / $program['target_amount']) * 100;
                                        $percentage = min(100, $percentage);
                                        ?>
                                        
                                        <div class="progress donation-progress">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?= $percentage ?>%" 
                                                 aria-valuenow="<?= $percentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <p class="small text-muted">
                                            Rp <?= number_format($program['current_amount']) ?> dari Rp <?= number_format($program['target_amount']) ?>
                                        </p>
                                        
                                        <a href="donation.php?id=<?= $program['program_id'] ?>" class="btn btn-success">
                                            <i class="fas fa-hand-holding-heart me-2"></i>Donasi Sekarang
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Tips Section -->
        <section class="mb-5">
            <h2 class="mb-4">Tips Keberlanjutan</h2>
            <div class="row">
                <div class="col-lg-8">
                    <?php foreach ($sustainabilityTips as $tip): ?>
                        <div class="tip-card">
                            <h5><i class="fas fa-lightbulb text-success me-2"></i><?= $tip['title'] ?></h5>
                            <p class="mb-0"><?= $tip['content'] ?></p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Bagikan Tips Anda</h5>
                        </div>
                        <div class="card-body">
                            <p>Punya tips keberlanjutan yang ingin dibagikan? Kirimkan kepada kami!</p>
                            <form action="submit_tip.php" method="post">
                                <div class="mb-3">
                                    <label for="tipTitle" class="form-label">Judul</label>
                                    <input type="text" class="form-control" id="tipTitle" name="title" required>
                                </div>
                                <div class="mb-3">
                                    <label for="tipContent" class="form-label">Konten</label>
                                    <textarea class="form-control" id="tipContent" name="content" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-paper-plane me-2"></i>Kirim Tip
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat dan ramah lingkungan.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="sustainability.php" class="text-white">Keberlanjutan</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
                <p class="small">
                    <i class="fas fa-leaf me-2"></i>Dicetak di atas kertas digital untuk menyelamatkan pohon.
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
