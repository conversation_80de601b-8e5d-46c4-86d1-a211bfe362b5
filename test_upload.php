<?php
// Aktifkan semua error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include functions
require_once 'includes/functions.php';

// Buat direktori uploads jika belum ada
$uploadDirs = [
    'uploads',
    'uploads/menu',
    'uploads/restaurants',
    'uploads/users',
    'uploads/drivers'
];

echo "<h1>Test Upload Function</h1>";

// Cek direktori uploads
echo "<h2>Checking Upload Directories</h2>";
foreach ($uploadDirs as $dir) {
    echo "<p>Directory: $dir - ";
    
    if (!file_exists($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "Created successfully";
        } else {
            echo "Failed to create";
        }
    } else {
        echo "Already exists";
    }
    
    echo " (Permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . ")";
    echo " - " . (is_writable($dir) ? "Writable" : "Not writable");
    echo "</p>";
}

// Cek fungsi uploadImage
echo "<h2>Testing uploadImage Function</h2>";

// Buat file test
$testFile = 'test_image.jpg';
$testContent = file_get_contents('https://via.placeholder.com/150');

if ($testContent === false) {
    echo "<p>Failed to get test image content</p>";
} else {
    if (file_put_contents($testFile, $testContent)) {
        echo "<p>Test image created successfully</p>";
        
        // Buat array $_FILES simulasi
        $fakeFile = [
            'name' => 'test_image.jpg',
            'type' => 'image/jpeg',
            'tmp_name' => __DIR__ . '/' . $testFile,
            'error' => UPLOAD_ERR_OK,
            'size' => filesize($testFile)
        ];
        
        // Test uploadImage function
        echo "<p>Testing uploadImage function with parameters:</p>";
        echo "<pre>";
        print_r($fakeFile);
        echo "</pre>";
        
        $result = uploadImage($fakeFile, 'uploads/menu', 'test');
        
        echo "<p>Result:</p>";
        echo "<pre>";
        print_r($result);
        echo "</pre>";
        
        // Hapus file test
        unlink($testFile);
        echo "<p>Test image deleted</p>";
    } else {
        echo "<p>Failed to create test image</p>";
    }
}

// Cek konfigurasi PHP
echo "<h2>PHP Configuration</h2>";
echo "<p>upload_max_filesize: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>post_max_size: " . ini_get('post_max_size') . "</p>";
echo "<p>memory_limit: " . ini_get('memory_limit') . "</p>";
echo "<p>max_execution_time: " . ini_get('max_execution_time') . "</p>";
echo "<p>file_uploads: " . (ini_get('file_uploads') ? 'On' : 'Off') . "</p>";
echo "<p>upload_tmp_dir: " . ini_get('upload_tmp_dir') . "</p>";

// Cek fungsi yang digunakan
echo "<h2>Function Availability</h2>";
$functions = [
    'move_uploaded_file',
    'copy',
    'rename',
    'mkdir',
    'chmod',
    'file_exists',
    'is_writable',
    'is_readable',
    'fileperms',
    'basename',
    'dirname',
    'pathinfo'
];

foreach ($functions as $function) {
    echo "<p>$function: " . (function_exists($function) ? 'Available' : 'Not available') . "</p>";
}

// Cek disabled functions
$disabled = ini_get('disable_functions');
echo "<h2>Disabled Functions</h2>";
echo "<p>" . (empty($disabled) ? 'None' : $disabled) . "</p>";

// Cek server info
echo "<h2>Server Information</h2>";
echo "<p>Server software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script filename: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p>Current directory: " . __DIR__ . "</p>";
echo "<p>PHP version: " . phpversion() . "</p>";
echo "<p>Current user: " . get_current_user() . "</p>";
echo "<p>OS: " . PHP_OS . "</p>";

// Cek fungsi uploadImage
echo "<h2>uploadImage Function Code</h2>";
$functionCode = file_get_contents('includes/functions.php');
if ($functionCode !== false) {
    $pattern = '/function uploadImage.*?\{.*?\}/s';
    if (preg_match($pattern, $functionCode, $matches)) {
        echo "<pre>";
        highlight_string("<?php\n" . $matches[0] . "\n?>");
        echo "</pre>";
    } else {
        echo "<p>Function code not found</p>";
    }
} else {
    echo "<p>Failed to read functions.php</p>";
}
?>
