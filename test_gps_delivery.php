<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GPS & Delivery Calculation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map { height: 400px; }
        .coordinate-display { font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test GPS & Perhitungan Biaya Pengiriman</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>GPS Location Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="getCurrentLocation()">
                            <i class="fas fa-location-arrow"></i> Dapatkan Lokasi GPS
                        </button>
                        
                        <div id="location-status" class="mt-3"></div>
                        
                        <div id="location-info" class="mt-3" style="display: none;">
                            <h6>Koordinat Anda:</h6>
                            <p class="coordinate-display">
                                Latitude: <span id="user-lat">-</span><br>
                                Longitude: <span id="user-lng">-</span><br>
                                Akurasi: <span id="accuracy">-</span> meter
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Delivery Calculation Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Pilih Restoran:</label>
                            <select class="form-select" id="restaurant-select">
                                <option value="">Pilih restoran...</option>
                                <?php
                                require_once 'config/database.php';
                                $conn = connectDB();
                                $stmt = $conn->prepare("SELECT restaurant_id, name, address, latitude, longitude FROM restaurants ORDER BY name");
                                $stmt->execute();
                                $restaurants = $stmt->fetchAll();
                                
                                foreach ($restaurants as $restaurant) {
                                    echo "<option value='{$restaurant['restaurant_id']}' data-lat='{$restaurant['latitude']}' data-lng='{$restaurant['longitude']}'>";
                                    echo htmlspecialchars($restaurant['name']) . " - " . htmlspecialchars($restaurant['address']);
                                    echo "</option>";
                                }
                                ?>
                            </select>
                        </div>
                        
                        <button class="btn btn-success" onclick="calculateDelivery()" disabled id="calculate-btn">
                            <i class="fas fa-calculator"></i> Hitung Biaya Pengiriman
                        </button>
                        
                        <div id="delivery-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Peta Lokasi</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Manual Distance Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Distance (km):</label>
                                <input type="number" class="form-control" id="manual-distance" step="0.1" min="0" max="50" value="5">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Fee Calculation:</label>
                                <button class="btn btn-info w-100" onclick="calculateManualFee()">Calculate</button>
                            </div>
                        </div>
                        <div id="manual-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;"></div>
                        <button class="btn btn-secondary btn-sm mt-2" onclick="clearLog()">Clear Log</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let userMarker;
        let restaurantMarker;
        let userLat, userLng;
        
        // Initialize map
        function initMap() {
            map = L.map('map').setView([-6.2088, 106.8456], 12); // Jakarta default
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
        }
        
        // Get current GPS location
        function getCurrentLocation() {
            const statusDiv = document.getElementById('location-status');
            const infoDiv = document.getElementById('location-info');
            const calculateBtn = document.getElementById('calculate-btn');
            
            statusDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Mendapatkan lokasi GPS...</div>';
            
            if (!navigator.geolocation) {
                statusDiv.innerHTML = '<div class="alert alert-danger">Geolocation tidak didukung oleh browser ini.</div>';
                return;
            }
            
            const options = {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 0
            };
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLat = position.coords.latitude;
                    userLng = position.coords.longitude;
                    const accuracy = position.coords.accuracy;
                    
                    // Update UI
                    document.getElementById('user-lat').textContent = userLat.toFixed(8);
                    document.getElementById('user-lng').textContent = userLng.toFixed(8);
                    document.getElementById('accuracy').textContent = Math.round(accuracy);
                    
                    statusDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check"></i> Lokasi GPS berhasil didapatkan!</div>';
                    infoDiv.style.display = 'block';
                    calculateBtn.disabled = false;
                    
                    // Update map
                    map.setView([userLat, userLng], 15);
                    
                    if (userMarker) {
                        userMarker.setLatLng([userLat, userLng]);
                    } else {
                        userMarker = L.marker([userLat, userLng])
                            .addTo(map)
                            .bindPopup('Lokasi Anda')
                            .openPopup();
                    }
                    
                    logMessage(`GPS Location: ${userLat.toFixed(6)}, ${userLng.toFixed(6)} (±${Math.round(accuracy)}m)`);
                },
                function(error) {
                    let errorMsg = 'Error mendapatkan lokasi: ';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMsg += 'Izin lokasi ditolak oleh user.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMsg += 'Informasi lokasi tidak tersedia.';
                            break;
                        case error.TIMEOUT:
                            errorMsg += 'Request timeout.';
                            break;
                        default:
                            errorMsg += 'Error tidak diketahui.';
                            break;
                    }
                    
                    statusDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${errorMsg}</div>`;
                    logMessage(`GPS Error: ${errorMsg}`);
                },
                options
            );
        }
        
        // Calculate delivery fee
        function calculateDelivery() {
            const restaurantSelect = document.getElementById('restaurant-select');
            const resultDiv = document.getElementById('delivery-result');
            
            if (!restaurantSelect.value) {
                alert('Pilih restoran terlebih dahulu');
                return;
            }
            
            if (!userLat || !userLng) {
                alert('Dapatkan lokasi GPS terlebih dahulu');
                return;
            }
            
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Menghitung biaya pengiriman...</div>';
            
            // Get restaurant coordinates
            const selectedOption = restaurantSelect.options[restaurantSelect.selectedIndex];
            const restaurantLat = parseFloat(selectedOption.dataset.lat);
            const restaurantLng = parseFloat(selectedOption.dataset.lng);
            
            // Add restaurant marker to map
            if (restaurantMarker) {
                restaurantMarker.setLatLng([restaurantLat, restaurantLng]);
            } else {
                restaurantMarker = L.marker([restaurantLat, restaurantLng])
                    .addTo(map)
                    .bindPopup('Restoran: ' + selectedOption.text);
            }
            
            // Fit map to show both markers
            if (userMarker && restaurantMarker) {
                const group = new L.featureGroup([userMarker, restaurantMarker]);
                map.fitBounds(group.getBounds().pad(0.1));
            }
            
            // Call API
            fetch('api/calculate_delivery.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    restaurant_id: restaurantSelect.value,
                    user_lat: userLat,
                    user_lng: userLng
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check"></i> Perhitungan Berhasil</h6>
                            <p><strong>Jarak:</strong> ${data.distance} km</p>
                            <p><strong>Biaya Pengiriman:</strong> ${data.delivery_fee.formatted}</p>
                            <p><strong>Estimasi Waktu:</strong> ${data.estimated_time} menit</p>
                            <small class="text-muted">
                                Restaurant: ${data.restaurant.coordinates.lat}, ${data.restaurant.coordinates.lng}<br>
                                User: ${data.user_coordinates.lat}, ${data.user_coordinates.lng}
                            </small>
                        </div>
                    `;
                    
                    logMessage(`Delivery Calculation: ${data.distance}km = ${data.delivery_fee.formatted} (${data.estimated_time}min)`);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${data.error}</div>`;
                    logMessage(`Delivery Error: ${data.error}`);
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Network Error: ${error.message}</div>`;
                logMessage(`Network Error: ${error.message}`);
            });
        }
        
        // Calculate manual fee
        function calculateManualFee() {
            const distance = parseFloat(document.getElementById('manual-distance').value);
            const resultDiv = document.getElementById('manual-result');
            
            if (isNaN(distance) || distance < 0) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Masukkan jarak yang valid</div>';
                return;
            }
            
            // Calculate using the same formula as backend
            let fee = 5000; // Base fee
            if (distance > 3) {
                fee += (distance - 3) * 2000;
            }
            fee = Math.max(fee, 3000); // Minimum
            fee = Math.min(fee, 25000); // Maximum
            
            const feeFormatted = 'Rp' + fee.toLocaleString('id-ID');
            
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>Jarak:</strong> ${distance} km<br>
                    <strong>Biaya:</strong> ${feeFormatted}<br>
                    <small class="text-muted">
                        Formula: Base Rp5.000 + (jarak > 3km ? (jarak-3) × Rp2.000 : 0)
                    </small>
                </div>
            `;
            
            logMessage(`Manual Calculation: ${distance}km = ${feeFormatted}`);
        }
        
        // Log messages
        function logMessage(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            logMessage('GPS & Delivery Test initialized');
        });
    </script>
</body>
</html>
