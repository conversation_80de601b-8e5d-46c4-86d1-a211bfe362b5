<?php
// Start session
session_start();

// Include required files
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) || isset($_SESSION['driver_id']) || isset($_SESSION['owner_id']) || isset($_SESSION['admin_id']);

// Get user name if logged in
$userName = '';
if (isset($_SESSION['user_id'])) {
    $userName = $_SESSION['user_name'] ?? 'Pengguna';
} elseif (isset($_SESSION['driver_id'])) {
    $userName = $_SESSION['driver_name'] ?? 'Pengemudi';
} elseif (isset($_SESSION['owner_id'])) {
    $userName = $_SESSION['owner_name'] ?? 'Pemilik Restoran';
} elseif (isset($_SESSION['admin_id'])) {
    $userName = $_SESSION['admin_name'] ?? 'Admin';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keranjang Belanja - KikaZen Ship</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-utensils me-2"></i>KikaZen Ship
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="restaurants.php">Restoran</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="recommendations.php">Rekomendasi</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Keranjang
                            <span class="badge bg-danger rounded-pill" id="cart-count">0</span>
                        </a>
                    </li>
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= $userName ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <li><a class="dropdown-item" href="user/dashboard.php">Dasbor</a></li>
                                    <li><a class="dropdown-item" href="user/orders.php">Pesanan</a></li>
                                    <li><a class="dropdown-item" href="user/profile.php">Profil</a></li>
                                <?php elseif (isset($_SESSION['driver_id'])): ?>
                                    <li><a class="dropdown-item" href="driver/dashboard.php">Dasbor</a></li>
                                <?php elseif (isset($_SESSION['owner_id'])): ?>
                                    <li><a class="dropdown-item" href="owner/dashboard.php">Dasbor</a></li>
                                <?php elseif (isset($_SESSION['admin_id'])): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">Dasbor</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Keluar</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Masuk</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Daftar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Cart Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h1>Keranjang Belanja</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Beranda</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Keranjang</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Item Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div id="cart-items">
                            <!-- Cart items will be dynamically added here -->
                            <p class="text-center py-4">Memuat item keranjang...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Ringkasan Pesanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="cart-subtotal">Rp0</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Biaya Pengiriman:</span>
                            <span id="delivery-fee">Rp15.000</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="fw-bold">Total:</span>
                            <span class="fw-bold" id="cart-total">Rp0</span>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" id="checkout-btn" onclick="proceedToCheckout()">
                                <i class="fas fa-shopping-cart me-2"></i>Lanjut ke Pembayaran
                            </button>
                            <button class="btn btn-outline-secondary" id="clear-cart-btn" onclick="clearCart()">
                                <i class="fas fa-trash me-2"></i>Kosongkan Keranjang
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Opsi Keberlanjutan</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="no_utensils">
                            <label class="form-check-label" for="no_utensils">
                                <i class="fas fa-utensils-slash me-2 text-success"></i> Tanpa Alat Makan
                                <p class="text-muted small mb-0">Pilih opsi ini jika Anda tidak memerlukan alat makan sekali pakai</p>
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="eco_packaging">
                            <label class="form-check-label" for="eco_packaging">
                                <i class="fas fa-box me-2 text-success"></i> Kemasan Ramah Lingkungan
                                <p class="text-muted small mb-0">Pilih opsi ini untuk mendapatkan kemasan yang lebih ramah lingkungan</p>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>KikaZen Ship</h5>
                    <p>Makanan favorit Anda, diantar dengan cepat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Tautan Cepat</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">Beranda</a></li>
                        <li><a href="restaurants.php" class="text-white">Restoran</a></li>
                        <li><a href="about.php" class="text-white">Tentang Kami</a></li>
                        <li><a href="contact.php" class="text-white">Kontak</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Hubungi Kami</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> Jl. Utama No. 123, Kota</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> KikaZen Ship. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="position-fixed bottom-0 end-0 p-3"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Try to load cart from server first
            fetch('api/cart.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.cart) {
                        // Update localStorage with server cart data
                        localStorage.setItem('cart', JSON.stringify(data.cart));
                    }
                    // Update cart display (whether server data was found or not)
                    updateCartDisplay();
                })
                .catch(error => {
                    console.error('Error loading cart from server:', error);
                    // Fall back to localStorage if server request fails
                    updateCartDisplay();
                });
        });

        function updateCartDisplay() {
            const cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };
            const cartItemsElement = document.getElementById('cart-items');
            const cartSubtotalElement = document.getElementById('cart-subtotal');
            const cartTotalElement = document.getElementById('cart-total');
            const checkoutBtn = document.getElementById('checkout-btn');

            // Clear current cart items
            cartItemsElement.innerHTML = '';

            if (cart.items.length === 0) {
                cartItemsElement.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h4>Keranjang belanja Anda kosong</h4>
                        <p class="text-muted">Jelajahi restoran untuk mulai memesan.</p>
                        <a href="restaurants.php" class="btn btn-primary mt-2">Jelajahi Restoran</a>
                    </div>
                `;
                cartSubtotalElement.textContent = 'Rp0';
                cartTotalElement.textContent = 'Rp0';
                checkoutBtn.disabled = true;
                return;
            }

            // Enable checkout button
            checkoutBtn.disabled = false;

            // Calculate subtotal
            let subtotal = 0;

            // Add each item to cart display
            cart.items.forEach(item => {
                const priceInRupiah = item.price * 15000;
                const itemTotal = priceInRupiah * item.quantity;
                subtotal += itemTotal;

                const itemElement = document.createElement('div');
                itemElement.className = 'card mb-3';
                itemElement.innerHTML = `
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="card-title">${item.name}</h5>
                                <p class="card-text text-muted">Rp${formatRupiah(priceInRupiah)} per item</p>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center justify-content-end">
                                    <button class="btn btn-sm btn-outline-secondary quantity-btn" data-item-id="${item.id}" data-action="decrease">-</button>
                                    <span class="mx-2">${item.quantity}</span>
                                    <button class="btn btn-sm btn-outline-secondary quantity-btn" data-item-id="${item.id}" data-action="increase">+</button>
                                    <button class="btn btn-sm btn-outline-danger ms-2 remove-from-cart" data-item-id="${item.id}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="text-end mt-2">
                                    <span class="fw-bold">Rp${formatRupiah(itemTotal)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                cartItemsElement.appendChild(itemElement);
            });

            // Add event listeners to new buttons
            const quantityButtons = cartItemsElement.querySelectorAll('.quantity-btn');
            quantityButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.itemId;
                    const action = this.dataset.action;
                    updateCartItemQuantity({ currentTarget: this });
                });
            });

            const removeButtons = cartItemsElement.querySelectorAll('.remove-from-cart');
            removeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.itemId;
                    removeFromCart({ currentTarget: this });
                });
            });

            // Update subtotal
            cartSubtotalElement.textContent = `Rp${formatRupiah(subtotal)}`;

            // Calculate total (subtotal + delivery fee)
            const deliveryFee = 15000;
            const total = subtotal + deliveryFee;

            // Update total
            cartTotalElement.textContent = `Rp${formatRupiah(total)}`;
        }

        function proceedToCheckout() {
            const cart = JSON.parse(localStorage.getItem('cart')) || { items: [], restaurant_id: null };

            if (cart.items.length === 0) {
                showToast('Keranjang belanja Anda kosong', 'error');
                return;
            }

            // Save sustainability options to localStorage
            const sustainabilityOptions = {
                no_utensils: document.getElementById('no_utensils').checked,
                eco_packaging: document.getElementById('eco_packaging').checked
            };
            localStorage.setItem('sustainability_options', JSON.stringify(sustainabilityOptions));

            // Redirect to checkout page
            window.location.href = 'checkout.php';
        }

        function clearCart() {
            if (confirm('Apakah Anda yakin ingin mengosongkan keranjang belanja?')) {
                localStorage.setItem('cart', JSON.stringify({ items: [], restaurant_id: null }));
                updateCartDisplay();
                showToast('Keranjang belanja telah dikosongkan');
            }
        }

        function formatRupiah(number) {
            return new Intl.NumberFormat('id-ID').format(number);
        }

        function showToast(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');

            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');

            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }
    </script>
</body>
</html>
